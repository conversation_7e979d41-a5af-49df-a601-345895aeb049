"""
API管理模块 - Gemini API管理器
从complete_report_generator.py中提取的完整API管理类
"""
import time
import threading
from typing import Dict, Any, List, Tuple
import google.generativeai as genai

# 导入配置模块
from config import API_KEYS, GeminiModelConfig


class GeminiAPILimits:
    """Gemini API精确限制配置"""

    # 根据用户提供的实际限制
    LIMITS = {
        "gemini-2.5-pro": {
            "rpm": 5,      # 每分钟5次请求
            "tpm": 250000, # 每分钟250K tokens
            "rpd": 1000     # 每天100次请求
        },
        "gemini-2.5-flash": {
            "rpm": 10,     # 每分钟10次请求
            "tpm": 250000, # 每分钟250K tokens
            "rpd": 2500     # 每天250次请求
        }
    }

    @staticmethod
    def get_model_limits(model_name: str) -> dict:
        """获取指定模型的限制"""
        if "pro" in model_name.lower():
            return GeminiAPILimits.LIMITS["gemini-2.5-pro"]
        else:
            return GeminiAPILimits.LIMITS["gemini-2.5-flash"]

    @staticmethod
    def calculate_safe_rpm(model_name: str, api_count: int) -> int:
        """计算安全的每分钟请求数"""
        limits = GeminiAPILimits.get_model_limits(model_name)
        base_rpm = limits["rpm"]

        # 保守策略：使用80%的限制，并在多个API间分配
        safe_rpm = int(base_rpm * 0.9)
        per_api_rpm = max(1, safe_rpm // api_count) if api_count > 0 else 1

        return per_api_rpm


class AsyncConfig:
    """异步配置参数（基于实际API限制）"""

    @staticmethod
    def get_available_api_count():
        """获取可用的API密钥数量"""
        valid_keys = [key for key in API_KEYS if key and "YOUR_GEMINI_API_KEY" not in key and len(key) > 10]
        return len(valid_keys)

    @staticmethod
    def get_max_concurrent_requests():
        """获取最大并发请求数（等于API密钥数量）"""
        api_count = AsyncConfig.get_available_api_count()
        # 使用所有可用的API密钥进行并发
        return api_count

    @staticmethod
    def get_performance_info():
        """获取性能信息"""
        api_count = AsyncConfig.get_available_api_count()
        max_concurrent = AsyncConfig.get_max_concurrent_requests()
        
        return {
            "available_api_keys": api_count,
            "max_concurrent_requests": max_concurrent,
            "estimated_total_api_calls": 100,
            "pro_rpm_limit": 5,
            "flash_rpm_limit": 10,
            "estimated_speedup": f"{min(api_count, 10)}x（智能并发）"
        }


class BaseGeminiAPIManager:
    """Gemini API管理器基类 - 包含通用方法"""

    def _extract_task_purpose(self, prompt: str, context: dict = None) -> str:
        """从prompt中提取详细的任务目的"""
        prompt_lower = prompt.lower()
        
        # 使用context信息增强任务目的提取
        if context:
            # 如果有上下文信息，可以更精确地判断任务类型
            task_type = context.get('task_type', '')
            section_info = context.get('section_info', {})
            
            if task_type == 'framework_generation':
                return "🎯 统筹模型生成报告框架结构"
            elif task_type == 'content_generation' and section_info:
                section_title = section_info.get('title', '未知章节')
                section_level = section_info.get('level', '未知级别')
                return f"⚡ 执行模型生成第{section_level}级节点: {section_title}"
            elif task_type == 'review' and section_info:
                section_title = section_info.get('title', '未知章节')
                return f"🔍 统筹模型审核章节: {section_title}"
            elif task_type == 'optimization' and section_info:
                section_title = section_info.get('title', '未知章节')
                return f"✨ 统筹模型优化章节: {section_title}"

        if "框架" in prompt and "json" in prompt_lower:
            return "🎯 统筹模型生成报告框架结构"
        elif "审核" in prompt and "章节" in prompt:
            # 提取章节标题
            try:
                title = self._extract_title_from_prompt(prompt)
            except:
                title = "未知章节"
            return f"🔍 统筹模型审核章节: {title}"
        elif "优化" in prompt and "章节" in prompt:
            try:
                title = self._extract_title_from_prompt(prompt)
            except:
                title = "未知章节"
            return f"✨ 统筹模型优化章节: {title}"
        elif "审核" in prompt and "整体" in prompt:
            return "📄 统筹模型审核整体文档质量"
        elif "优化" in prompt and "整体" in prompt:
            return "🔧 统筹模型优化整体文档结构"
        elif "ocr" in prompt_lower or "图片" in prompt or "pdf" in prompt_lower:
            return "🖼️ Gemini OCR处理PDF图片内容"
        elif "生成" in prompt and ("内容" in prompt or "详细" in prompt):
            # 提取节点标题和级别
            try:
                title = self._extract_title_from_prompt(prompt)
                level = self._extract_level_from_prompt(prompt)
                if title == "未知标题":
                    title = "未知节点"
            except:
                title = "未知节点"
                level = "未知级别"
            return f"⚡ 执行模型生成第{level}级节点: {title}"
        elif "第" in prompt and "级" in prompt:
            level_match = prompt.find("第") + 1
            if level_match < len(prompt):
                level_char = prompt[level_match]
                try:
                    title = self._extract_title_from_prompt(prompt)
                except:
                    title = "未知标题"
                return f"📝 执行模型生成第{level_char}级标题: {title}"
        else:
            return "🤖 AI模型执行任务"

    def _extract_title_from_prompt(self, prompt: str) -> str:
        """从prompt中提取标题"""
        # 查找引号中的标题
        import re

        # 匹配 "标题" 或 「标题」
        title_patterns = [
            r'"([^"]+)"',
            r'「([^」]+)」',
            r'《([^》]+)》',
            r'【([^】]+)】'
        ]

        for pattern in title_patterns:
            match = re.search(pattern, prompt)
            if match:
                return match.group(1)

        # 如果没有找到引号，尝试查找"章节标题："后的内容
        if "章节标题：" in prompt:
            start = prompt.find("章节标题：") + 5
            end = prompt.find("\n", start)
            if end == -1:
                end = start + 50
            return prompt[start:end].strip()

        return "未知标题"

    def _extract_level_from_prompt(self, prompt: str) -> str:
        """从prompt中提取级别"""
        import re

        # 查找"第X级"
        level_match = re.search(r'第(\d+)级', prompt)
        if level_match:
            return level_match.group(1)

        return "未知级别"


class GeminiAPIManager(BaseGeminiAPIManager):
    """API轮换管理器 - 支持参数配置"""

    def __init__(self, api_keys: List[str], model_names: List[str], model_config: GeminiModelConfig = None):
        self.api_configs = []

        for i, key in enumerate(api_keys):
            if key and "YOUR_GEMINI_API_KEY" not in key and len(key) > 10:
                self.api_configs.append({
                    "name": f"API Key {i+1}",
                    "key": key,
                    "models": model_names,
                    "current_model_index": 0
                })

        if not self.api_configs:
            raise ValueError("FATAL: No valid Google API keys are configured.")

        self.total_keys = len(self.api_configs)
        self.current_api_index = 0
        self.lock = threading.Lock()
        self.max_rotations = 10000
        self.total_rotations_completed = 0
        self.usage_counts = {i: 0 for i in range(self.total_keys)}
        self.consecutive_cleanup_counts = {i: 0 for i in range(self.total_keys)}
        self.api_call_counts = {i: 0 for i in range(self.total_keys)}

        # 模型参数配置
        self.model_config = model_config or GeminiModelConfig()

        print(f"Gemini API Manager initialized with {self.total_keys} active keys.")
    
    def generate_content_with_model(self, prompt: str, model_name: str) -> Tuple[Any, int]:
        """使用指定模型生成内容"""
        with self.lock:
            if self.total_rotations_completed >= self.max_rotations:
                raise Exception(f"已完成 {self.max_rotations} 轮完整的API密钥轮换，程序终止。")
            
            if self.total_keys == 0:
                raise Exception("No configured API keys to use for generation.")
        
        # Simple implementation for testing
        current_api_index = self.current_api_index
        api_config = self.api_configs[current_api_index]
        api_key = api_config["key"]
        
        try:
            genai.configure(api_key=api_key)
            generation_config = self.model_config.create_generation_config(model_name)
            model = genai.GenerativeModel(model_name, generation_config=generation_config)
            response = model.generate_content([prompt])
            return response, current_api_index
        except Exception as e:
            raise Exception(f"API call failed: {str(e)}")
    
    def record_successful_processing(self, key_index: int):
        """记录成功处理"""
        with self.lock:
            self.usage_counts[key_index] += 1