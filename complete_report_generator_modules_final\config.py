"""
配置管理模块 - 包含所有配置相关的类和常量
从原始文件完整提取，严格不做任何修改
"""
from pathlib import Path
from typing import List, Dict, Any
import google.generativeai as genai

# API密钥列表 - 用户提供的完整密钥列表（支持动态数量）
API_KEYS = [
    "AIzaSyAuvPJmOQzYGi-zfmxvMAEUIRTaWelwXwQ",
    "AIzaSyCz4ND6v_5_eGtlgok53Monj6gvTh-0XGE",
    "AIzaSyDDJ7DGAXY2RElU1QCXlOQpCWRk9mhgwY8",
    "AIzaSyBTec7MOadr0yOt-omEudzD0PxANwG67qc",
    "AIzaSyCe_XVHffYL1GpoHc7Z7cguoPpLKlHI6YY",
    "AIzaSyDJD1E55O771LcM7JA5_rla_2DcYz4fNIs",
    "AIzaSyDHNm93ybs8DRoO7XUMgqQt0ZqD8_BcTzY",
    "AIzaSyAAfACI-vjIZe78e7pfusUn56xJPY6kcKU",
    "AIzaSyDKW-0DSIGNnjacCfSGADC7OmoDvAaReac",
    "AIzaSyApBdUyH_XTZWffyZrreQq0DskEjdKTzpg",
    # 可以继续添加更多API密钥...
]

MODEL_NAMES = ['gemini-2.5-pro', 'gemini-2.5-flash']
MAX_CONSECUTIVE_CLEANUP_COUNT = 100

class GeminiModelConfig:
    """Gemini模型参数配置类"""

    def __init__(self):
        # 默认配置
        self.default_configs = {
            "gemini-2.5-pro": {
                "temperature": 0.1,
                "top_p": 0.8,
                "top_k": 40,
                "max_output_tokens": 65000,
                "candidate_count": 1,
                "stop_sequences": [],
                "safety_settings": None,
                "response_mime_type": "text/plain"
            },
            "gemini-2.5-flash": {
                "temperature": 0.0,
                "top_p": 0.9,
                "top_k": 32,
                "max_output_tokens": 65000,
                "candidate_count": 1,
                "stop_sequences": [],
                "safety_settings": None,
                "response_mime_type": "text/plain"
            }
        }

        # 当前配置（可以被用户修改）
        self.current_configs = {}
        for model, config in self.default_configs.items():
            self.current_configs[model] = config.copy()

    def update_model_config(self, model_name: str, **kwargs):
        """更新指定模型的参数配置"""
        if model_name not in self.current_configs:
            raise ValueError(f"不支持的模型: {model_name}")

        valid_params = [
            'temperature', 'top_p', 'top_k', 'max_output_tokens',
            'candidate_count', 'stop_sequences', 'safety_settings', 'response_mime_type'
        ]

        for param, value in kwargs.items():
            if param not in valid_params:
                print(f"⚠️ 警告: 忽略无效参数 {param}")
                continue

            # 参数验证
            if param == 'temperature' and not (0.0 <= value <= 2.0):
                raise ValueError("temperature 必须在 0.0-2.0 之间")
            elif param == 'top_p' and not (0.0 <= value <= 1.0):
                raise ValueError("top_p 必须在 0.0-1.0 之间")
            elif param == 'top_k' and not (1 <= value <= 100):
                raise ValueError("top_k 必须在 1-100 之间")
            elif param == 'max_output_tokens' and not (1 <= value <= 1000000):
                raise ValueError("max_output_tokens 必须在 1-1000000 之间")
            elif param == 'candidate_count' and not (1 <= value <= 8):
                raise ValueError("candidate_count 必须在 1-8 之间")

            self.current_configs[model_name][param] = value
            print(f"✅ 已更新 {model_name} 的 {param} = {value}")

    def get_model_config(self, model_name: str) -> dict:
        """获取指定模型的配置"""
        if model_name not in self.current_configs:
            raise ValueError(f"不支持的模型: {model_name}")
        return self.current_configs[model_name].copy()

    def reset_model_config(self, model_name: str):
        """重置指定模型的配置为默认值"""
        if model_name not in self.current_configs:
            raise ValueError(f"不支持的模型: {model_name}")

        self.current_configs[model_name] = self.default_configs[model_name].copy()
        print(f"✅ 已重置 {model_name} 的配置为默认值")

    def reset_all_configs(self):
        """重置所有模型配置为默认值"""
        for model in self.current_configs:
            self.current_configs[model] = self.default_configs[model].copy()
        print("✅ 已重置所有模型配置为默认值")

    def print_current_configs(self):
        """打印当前所有模型的配置"""
        print("\n📋 当前Gemini模型参数配置:")
        print("=" * 60)
        for model, config in self.current_configs.items():
            print(f"\n🤖 {model}:")
            for param, value in config.items():
                if param == 'safety_settings' and value is None:
                    continue
                print(f"   {param}: {value}")
        print("=" * 60)

    def create_generation_config(self, model_name: str):
        """为指定模型创建GenerationConfig对象"""
        import google.generativeai as genai

        config = self.get_model_config(model_name)

        # 过滤掉None值和不支持的参数
        filtered_config = {}
        for key, value in config.items():
            if value is not None and key != 'safety_settings':
                filtered_config[key] = value

        return genai.types.GenerationConfig(**filtered_config)


class ImageMatcher:
    """图片匹配器 - 使用AI匹配内容与图片"""

    def __init__(self, data_sources_paths: List[str], api_manager=None):
        self.data_sources_paths = data_sources_paths
        self.api_manager = api_manager
        self.image_cache = {}
        self.analysis_cache = {}  # 缓存图片分析结果
        self._scan_images()

    def _scan_images(self):
        """递归扫描所有数据源路径中的图片"""
        print("🔍 扫描数据源中的图片文件...")

        image_extensions = {'.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff', '.webp'}
        total_images = 0

        for data_source in self.data_sources_paths:
            source_path = Path(data_source)
            if source_path.is_file():
                # 如果是文件，扫描其所在目录
                source_path = source_path.parent

            if source_path.exists() and source_path.is_dir():
                # 递归搜索所有图片
                for image_path in source_path.rglob('*'):
                    if image_path.is_file() and image_path.suffix.lower() in image_extensions:
                        try:
                            # 尝试获取相对路径，如果失败则使用绝对路径
                            relative_path = str(image_path.relative_to(Path.cwd()))
                        except ValueError:
                            # 如果不在当前工作目录下，使用绝对路径
                            relative_path = str(image_path.absolute())

                        self.image_cache[str(image_path)] = {
                            'path': str(image_path),
                            'relative_path': relative_path,
                            'name': image_path.name,
                            'size': image_path.stat().st_size,
                            'analyzed': False
                        }
                        total_images += 1

        print(f"   📊 共发现 {total_images} 张图片")
        return self.image_cache

    def embed_relevant_images(self, content: str) -> str:
        """嵌入相关图片到内容中"""
        # 简化的图片嵌入逻辑
        return content