{"checkpoint_id": "task_instructions_generated_1754621479", "stage": "task_instructions_generated", "timestamp": 1754621479, "created_time": "2025-08-08 10:51:19", "report_config": {"title": "核电产业研究报告", "data_source": "C:/Users/<USER>/Desktop/新能源/研究报告/核电/PDF", "output_dir": "output", "max_depth": 5, "primary_sections": 6, "target_words": 50000, "reference_report": "D:/new/11.30copy/桌面/产业研究院2024年工作成果汇总/2024年工作成果—殷黎/01 产业研究/12 钛合金中间合金", "max_tokens": 250000, "enable_chart_generation": true, "enable_image_embedding": true, "enable_search_enhancement": true, "search_auto_confirm": true, "predefined_framework": null}, "data": {"framework": {"sections": [{"title": "市场概览与现状分析", "level": 1, "children": [{"title": "市场概览与现状分析的发展历程", "level": 2, "children": [{"title": "早期发展阶段", "level": 3, "children": [{"title": "要点分析", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "请依据数据源6，提炼并阐述第一个关键要素的现状核心要点。要求观点明确、数据支撑、论述客观。", "word_count": "150-250字"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "基于上一节点的要点，进行深层原因、内在逻辑或潜在影响的解读。分析该要点背后的驱动因素及其对未来发展的意义。", "word_count": "200-300字"}}], "task_instruction": {"content_requirements": "作为一组分析的引子，简要介绍此部分将从哪些具体要点切入，对关键要素进行剖析。起到过渡作用。", "word_count": "50-100字"}}, {"title": "深度解读", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "请依据数据源6，提炼并阐述第二个关键要素的现状核心要点。要求观点明确、数据支撑、论述客观。", "word_count": "150-250字"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "基于上一节点的要点，进行深层原因、内在逻辑或潜在影响的解读。分析该要点背后的驱动因素及其对未来发展的意义。", "word_count": "200-300字"}}], "task_instruction": {"content_requirements": "作为另一组分析的引子，简要说明此部分将对哪些要点进行更深层次的解读，揭示其内在联系或趋势。", "word_count": "50-100字"}}], "task_instruction": {"content_requirements": "承接上文，引出即将要分析的、影响未来发展的几个核心要素。简要说明为何选择这些要素进行分析，以及它们的重要性。", "word_count": "80-120字"}}, {"title": "快速成长阶段", "level": 3, "children": [{"title": "要点分析", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "请依据数据源6及前文分析，提出第一条具体、可操作的发展建议。清晰说明建议的内容（做什么）。", "word_count": "150-250字"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "详细阐述上一节点所提建议的理由、预期效果和实施路径。解释为何要这么做，以及它将如何促进发展。", "word_count": "200-300字"}}], "task_instruction": {"content_requirements": "作为一组建议的引子，简要介绍此部分将从哪些具体方面提出建议。起到过渡作用。", "word_count": "50-100字"}}, {"title": "深度解读", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "请依据数据源6及前文分析，提出第二条具体、可操作的发展建议。清晰说明建议的内容（做什么）。", "word_count": "150-250字"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "详细阐述上一节点所提建议的理由、预期效果和实施路径。解释为何要这么做，以及它将如何促进发展。", "word_count": "200-300字"}}], "task_instruction": {"content_requirements": "作为另一组建议的引子，简要说明此部分将对哪些建议进行更深层次的解读，阐明其战略价值。", "word_count": "50-100字"}}], "task_instruction": {"content_requirements": "基于前文对现状和关键要素的分析，引出本部分将提出的针对性发展建议。简要说明这些建议旨在解决哪些问题或抓住哪些机遇。", "word_count": "80-120字"}}], "task_instruction": {"content_requirements": "作为本节的引言，请概述当前发展环境的宏观背景、面临的主要机遇与挑战。为后续的关键要素分析和发展建议提供宏观视角和背景铺垫。", "word_count": "100-150字"}}, {"title": "市场概览与现状分析的关键指标", "level": 2, "children": [{"title": "核心数据分析", "level": 3, "children": [{"title": "要点分析", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "请依据数据源6，提炼并阐述实现未来方向所需的第一个关键要素。例如某个新兴技术、市场趋势或政策导向。", "word_count": "150-250字"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "深入解读上一节点提出的关键要素，分析其发展潜力、实现路径以及可能带来的变革性影响。", "word_count": "200-300字"}}], "task_instruction": {"content_requirements": "作为一组分析的引子，简要介绍此部分将从哪些具体要点切入，对未来方向的关键要素进行剖析。起到过渡作用。", "word_count": "50-100字"}}, {"title": "深度解读", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "请依据数据源6，提炼并阐述实现未来方向所需的第二个关键要素。例如某个新兴技术、市场趋势或政策导向。", "word_count": "150-250字"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "深入解读上一节点提出的关键要素，分析其发展潜力、实现路径以及可能带来的变革性影响。", "word_count": "200-300字"}}], "task_instruction": {"content_requirements": "作为另一组分析的引子，简要说明此部分将对哪些未来要素进行更深层次的解读，揭示其战略价值。", "word_count": "50-100字"}}], "task_instruction": {"content_requirements": "承接上文，引出为实现未来发展方向所需要关注和培育的几个关键要素。简要说明这些要素对于实现未来蓝图的重要性。", "word_count": "80-120字"}}, {"title": "指标变化趋势", "level": 3, "children": [{"title": "要点分析", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "请依据数据源6及前文分析，提出为实现未来发展方向所需的第一条具体、可操作的建议。", "word_count": "150-250字"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "详细阐述上一节点所提建议的战略意义、实施要点和预期成果，说明其如何支撑未来发展方向的实现。", "word_count": "200-300字"}}], "task_instruction": {"content_requirements": "作为一组建议的引子，简要介绍此部分将从哪些具体方面提出建议，以确保未来发展方向的实现。", "word_count": "50-100字"}}, {"title": "深度解读", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "请依据数据源6及前文分析，提出为实现未来发展方向所需的第二条具体、可操作的建议。", "word_count": "150-250字"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "详细阐述上一节点所提建议的战略意义、实施要点和预期成果，说明其如何支撑未来发展方向的实现。", "word_count": "200-300字"}}], "task_instruction": {"content_requirements": "作为另一组建议的引子，简要说明此部分将对哪些建议进行更深层次的解读，阐明其对长远发展的重要性。", "word_count": "50-100字"}}], "task_instruction": {"content_requirements": "基于前文对未来方向和关键要素的分析，引出为实现该发展方向而需采取的行动建议。简要说明建议的总体思路。", "word_count": "80-120字"}}], "task_instruction": {"content_requirements": "作为本节的引言，基于前面的现状分析，高屋建瓴地提出未来发展的总体方向和战略构想。明确未来的目标和愿景。", "word_count": "100-150字"}}], "task_instruction": {"content_requirements": "作为章节开篇，请简要概述本章将要探讨的核心内容，包括对现状的分析、对未来方向的研判，以及最终提出的发展建议。起到提纲挈领、总览全章的作用。", "word_count": "50-100字"}}, {"title": "技术发展趋势", "level": 1, "children": [{"title": "技术发展趋势的核心技术", "level": 2, "children": [{"title": "技术架构分析", "level": 3, "children": [{"title": "要点分析", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为技术架构分析提供详细、专业的分析内容", "word_count": "2300-3000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "技术优势评估", "level": 3, "children": [{"title": "要点分析", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为技术优势评估提供详细、专业的分析内容", "word_count": "2300-3000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为技术发展趋势的核心技术提供详细、专业的分析内容", "word_count": "2800-3500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "技术发展趋势的发展趋势", "level": 2, "children": [{"title": "技术演进路径", "level": 3, "children": [{"title": "要点分析", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为技术演进路径提供详细、专业的分析内容", "word_count": "2300-3000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "创新突破点", "level": 3, "children": [{"title": "要点分析", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为创新突破点提供详细、专业的分析内容", "word_count": "2300-3000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为技术发展趋势的发展趋势提供详细、专业的分析内容", "word_count": "2800-3500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为技术发展趋势提供详细、专业的分析内容", "word_count": "3000-4000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "竞争格局分析", "level": 1, "children": [{"title": "竞争格局分析的现状分析", "level": 2, "children": [{"title": "主要参与者", "level": 3, "children": [{"title": "要点分析", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为主要参与者提供详细、专业的分析内容", "word_count": "2300-3000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "竞争优势对比", "level": 3, "children": [{"title": "要点分析", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为竞争优势对比提供详细、专业的分析内容", "word_count": "2300-3000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为竞争格局分析的现状分析提供详细、专业的分析内容", "word_count": "2800-3500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "竞争格局分析的发展方向", "level": 2, "children": [{"title": "主要参与者", "level": 3, "children": [{"title": "要点分析", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为主要参与者提供详细、专业的分析内容", "word_count": "2300-3000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "竞争优势对比", "level": 3, "children": [{"title": "要点分析", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为竞争优势对比提供详细、专业的分析内容", "word_count": "2300-3000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为竞争格局分析的发展方向提供详细、专业的分析内容", "word_count": "2800-3500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为竞争格局分析提供详细、专业的分析内容", "word_count": "3000-4000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "政策环境分析", "level": 1, "children": [{"title": "政策环境分析的法规框架", "level": 2, "children": [{"title": "政策体系构建", "level": 3, "children": [{"title": "要点分析", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为政策体系构建提供详细、专业的分析内容", "word_count": "2300-3000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "监管机制完善", "level": 3, "children": [{"title": "要点分析", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为监管机制完善提供详细、专业的分析内容", "word_count": "2300-3000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为政策环境分析的法规框架提供详细、专业的分析内容", "word_count": "2800-3500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "政策环境分析的实施情况", "level": 2, "children": [{"title": "执行效果评估", "level": 3, "children": [{"title": "要点分析", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为执行效果评估提供详细、专业的分析内容", "word_count": "2300-3000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "改进建议", "level": 3, "children": [{"title": "要点分析", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为改进建议提供详细、专业的分析内容", "word_count": "2300-3000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为政策环境分析的实施情况提供详细、专业的分析内容", "word_count": "2800-3500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为政策环境分析提供详细、专业的分析内容", "word_count": "3000-4000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "投资与融资分析", "level": 1, "children": [{"title": "投资与融资分析的资金流向", "level": 2, "children": [{"title": "投资分布分析", "level": 3, "children": [{"title": "要点分析", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为投资分布分析提供详细、专业的分析内容", "word_count": "2300-3000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "资金配置优化", "level": 3, "children": [{"title": "要点分析", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为资金配置优化提供详细、专业的分析内容", "word_count": "2300-3000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为投资与融资分析的资金流向提供详细、专业的分析内容", "word_count": "2800-3500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "投资与融资分析的投资机会", "level": 2, "children": [{"title": "重点投资领域", "level": 3, "children": [{"title": "要点分析", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为重点投资领域提供详细、专业的分析内容", "word_count": "2300-3000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "投资风险评估", "level": 3, "children": [{"title": "要点分析", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为投资风险评估提供详细、专业的分析内容", "word_count": "2300-3000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为投资与融资分析的投资机会提供详细、专业的分析内容", "word_count": "2800-3500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为投资与融资分析提供详细、专业的分析内容", "word_count": "3000-4000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "未来发展展望", "level": 1, "children": [{"title": "未来发展展望的现状分析", "level": 2, "children": [{"title": "关键要素分析", "level": 3, "children": [{"title": "要点分析", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为关键要素分析提供详细、专业的分析内容", "word_count": "2300-3000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "发展建议", "level": 3, "children": [{"title": "要点分析", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为发展建议提供详细、专业的分析内容", "word_count": "2300-3000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为未来发展展望的现状分析提供详细、专业的分析内容", "word_count": "2800-3500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "未来发展展望的发展方向", "level": 2, "children": [{"title": "关键要素分析", "level": 3, "children": [{"title": "要点分析", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为关键要素分析提供详细、专业的分析内容", "word_count": "2300-3000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "发展建议", "level": 3, "children": [{"title": "要点分析", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为发展建议提供详细、专业的分析内容", "word_count": "2300-3000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为未来发展展望的发展方向提供详细、专业的分析内容", "word_count": "2800-3500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为未来发展展望提供详细、专业的分析内容", "word_count": "3000-4000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}]}, "topic": "核电产业研究报告", "data_sources": ["C:\\Users\\<USER>\\Desktop\\新能源\\研究报告\\核电\\PDF_classified"], "framework_file_path": "C:/Users/<USER>/Desktop/深海机器人/地热/框架/固态电池产业研究报告框架.md", "total_nodes": 186, "sections": [{"title": "市场概览与现状分析", "level": 1, "children": [{"title": "市场概览与现状分析的发展历程", "level": 2, "children": [{"title": "早期发展阶段", "level": 3, "children": [{"title": "要点分析", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "请依据数据源6，提炼并阐述第一个关键要素的现状核心要点。要求观点明确、数据支撑、论述客观。", "word_count": "150-250字"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "基于上一节点的要点，进行深层原因、内在逻辑或潜在影响的解读。分析该要点背后的驱动因素及其对未来发展的意义。", "word_count": "200-300字"}}], "task_instruction": {"content_requirements": "作为一组分析的引子，简要介绍此部分将从哪些具体要点切入，对关键要素进行剖析。起到过渡作用。", "word_count": "50-100字"}}, {"title": "深度解读", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "请依据数据源6，提炼并阐述第二个关键要素的现状核心要点。要求观点明确、数据支撑、论述客观。", "word_count": "150-250字"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "基于上一节点的要点，进行深层原因、内在逻辑或潜在影响的解读。分析该要点背后的驱动因素及其对未来发展的意义。", "word_count": "200-300字"}}], "task_instruction": {"content_requirements": "作为另一组分析的引子，简要说明此部分将对哪些要点进行更深层次的解读，揭示其内在联系或趋势。", "word_count": "50-100字"}}], "task_instruction": {"content_requirements": "承接上文，引出即将要分析的、影响未来发展的几个核心要素。简要说明为何选择这些要素进行分析，以及它们的重要性。", "word_count": "80-120字"}}, {"title": "快速成长阶段", "level": 3, "children": [{"title": "要点分析", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "请依据数据源6及前文分析，提出第一条具体、可操作的发展建议。清晰说明建议的内容（做什么）。", "word_count": "150-250字"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "详细阐述上一节点所提建议的理由、预期效果和实施路径。解释为何要这么做，以及它将如何促进发展。", "word_count": "200-300字"}}], "task_instruction": {"content_requirements": "作为一组建议的引子，简要介绍此部分将从哪些具体方面提出建议。起到过渡作用。", "word_count": "50-100字"}}, {"title": "深度解读", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "请依据数据源6及前文分析，提出第二条具体、可操作的发展建议。清晰说明建议的内容（做什么）。", "word_count": "150-250字"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "详细阐述上一节点所提建议的理由、预期效果和实施路径。解释为何要这么做，以及它将如何促进发展。", "word_count": "200-300字"}}], "task_instruction": {"content_requirements": "作为另一组建议的引子，简要说明此部分将对哪些建议进行更深层次的解读，阐明其战略价值。", "word_count": "50-100字"}}], "task_instruction": {"content_requirements": "基于前文对现状和关键要素的分析，引出本部分将提出的针对性发展建议。简要说明这些建议旨在解决哪些问题或抓住哪些机遇。", "word_count": "80-120字"}}], "task_instruction": {"content_requirements": "作为本节的引言，请概述当前发展环境的宏观背景、面临的主要机遇与挑战。为后续的关键要素分析和发展建议提供宏观视角和背景铺垫。", "word_count": "100-150字"}}, {"title": "市场概览与现状分析的关键指标", "level": 2, "children": [{"title": "核心数据分析", "level": 3, "children": [{"title": "要点分析", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "请依据数据源6，提炼并阐述实现未来方向所需的第一个关键要素。例如某个新兴技术、市场趋势或政策导向。", "word_count": "150-250字"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "深入解读上一节点提出的关键要素，分析其发展潜力、实现路径以及可能带来的变革性影响。", "word_count": "200-300字"}}], "task_instruction": {"content_requirements": "作为一组分析的引子，简要介绍此部分将从哪些具体要点切入，对未来方向的关键要素进行剖析。起到过渡作用。", "word_count": "50-100字"}}, {"title": "深度解读", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "请依据数据源6，提炼并阐述实现未来方向所需的第二个关键要素。例如某个新兴技术、市场趋势或政策导向。", "word_count": "150-250字"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "深入解读上一节点提出的关键要素，分析其发展潜力、实现路径以及可能带来的变革性影响。", "word_count": "200-300字"}}], "task_instruction": {"content_requirements": "作为另一组分析的引子，简要说明此部分将对哪些未来要素进行更深层次的解读，揭示其战略价值。", "word_count": "50-100字"}}], "task_instruction": {"content_requirements": "承接上文，引出为实现未来发展方向所需要关注和培育的几个关键要素。简要说明这些要素对于实现未来蓝图的重要性。", "word_count": "80-120字"}}, {"title": "指标变化趋势", "level": 3, "children": [{"title": "要点分析", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "请依据数据源6及前文分析，提出为实现未来发展方向所需的第一条具体、可操作的建议。", "word_count": "150-250字"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "详细阐述上一节点所提建议的战略意义、实施要点和预期成果，说明其如何支撑未来发展方向的实现。", "word_count": "200-300字"}}], "task_instruction": {"content_requirements": "作为一组建议的引子，简要介绍此部分将从哪些具体方面提出建议，以确保未来发展方向的实现。", "word_count": "50-100字"}}, {"title": "深度解读", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "请依据数据源6及前文分析，提出为实现未来发展方向所需的第二条具体、可操作的建议。", "word_count": "150-250字"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "详细阐述上一节点所提建议的战略意义、实施要点和预期成果，说明其如何支撑未来发展方向的实现。", "word_count": "200-300字"}}], "task_instruction": {"content_requirements": "作为另一组建议的引子，简要说明此部分将对哪些建议进行更深层次的解读，阐明其对长远发展的重要性。", "word_count": "50-100字"}}], "task_instruction": {"content_requirements": "基于前文对未来方向和关键要素的分析，引出为实现该发展方向而需采取的行动建议。简要说明建议的总体思路。", "word_count": "80-120字"}}], "task_instruction": {"content_requirements": "作为本节的引言，基于前面的现状分析，高屋建瓴地提出未来发展的总体方向和战略构想。明确未来的目标和愿景。", "word_count": "100-150字"}}], "task_instruction": {"content_requirements": "作为章节开篇，请简要概述本章将要探讨的核心内容，包括对现状的分析、对未来方向的研判，以及最终提出的发展建议。起到提纲挈领、总览全章的作用。", "word_count": "50-100字"}}, {"title": "技术发展趋势", "level": 1, "children": [{"title": "技术发展趋势的核心技术", "level": 2, "children": [{"title": "技术架构分析", "level": 3, "children": [{"title": "要点分析", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为技术架构分析提供详细、专业的分析内容", "word_count": "2300-3000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "技术优势评估", "level": 3, "children": [{"title": "要点分析", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为技术优势评估提供详细、专业的分析内容", "word_count": "2300-3000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为技术发展趋势的核心技术提供详细、专业的分析内容", "word_count": "2800-3500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "技术发展趋势的发展趋势", "level": 2, "children": [{"title": "技术演进路径", "level": 3, "children": [{"title": "要点分析", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为技术演进路径提供详细、专业的分析内容", "word_count": "2300-3000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "创新突破点", "level": 3, "children": [{"title": "要点分析", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为创新突破点提供详细、专业的分析内容", "word_count": "2300-3000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为技术发展趋势的发展趋势提供详细、专业的分析内容", "word_count": "2800-3500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为技术发展趋势提供详细、专业的分析内容", "word_count": "3000-4000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "竞争格局分析", "level": 1, "children": [{"title": "竞争格局分析的现状分析", "level": 2, "children": [{"title": "主要参与者", "level": 3, "children": [{"title": "要点分析", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为主要参与者提供详细、专业的分析内容", "word_count": "2300-3000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "竞争优势对比", "level": 3, "children": [{"title": "要点分析", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为竞争优势对比提供详细、专业的分析内容", "word_count": "2300-3000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为竞争格局分析的现状分析提供详细、专业的分析内容", "word_count": "2800-3500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "竞争格局分析的发展方向", "level": 2, "children": [{"title": "主要参与者", "level": 3, "children": [{"title": "要点分析", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为主要参与者提供详细、专业的分析内容", "word_count": "2300-3000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "竞争优势对比", "level": 3, "children": [{"title": "要点分析", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为竞争优势对比提供详细、专业的分析内容", "word_count": "2300-3000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为竞争格局分析的发展方向提供详细、专业的分析内容", "word_count": "2800-3500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为竞争格局分析提供详细、专业的分析内容", "word_count": "3000-4000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "政策环境分析", "level": 1, "children": [{"title": "政策环境分析的法规框架", "level": 2, "children": [{"title": "政策体系构建", "level": 3, "children": [{"title": "要点分析", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为政策体系构建提供详细、专业的分析内容", "word_count": "2300-3000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "监管机制完善", "level": 3, "children": [{"title": "要点分析", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为监管机制完善提供详细、专业的分析内容", "word_count": "2300-3000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为政策环境分析的法规框架提供详细、专业的分析内容", "word_count": "2800-3500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "政策环境分析的实施情况", "level": 2, "children": [{"title": "执行效果评估", "level": 3, "children": [{"title": "要点分析", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为执行效果评估提供详细、专业的分析内容", "word_count": "2300-3000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "改进建议", "level": 3, "children": [{"title": "要点分析", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为改进建议提供详细、专业的分析内容", "word_count": "2300-3000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为政策环境分析的实施情况提供详细、专业的分析内容", "word_count": "2800-3500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为政策环境分析提供详细、专业的分析内容", "word_count": "3000-4000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "投资与融资分析", "level": 1, "children": [{"title": "投资与融资分析的资金流向", "level": 2, "children": [{"title": "投资分布分析", "level": 3, "children": [{"title": "要点分析", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为投资分布分析提供详细、专业的分析内容", "word_count": "2300-3000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "资金配置优化", "level": 3, "children": [{"title": "要点分析", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为资金配置优化提供详细、专业的分析内容", "word_count": "2300-3000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为投资与融资分析的资金流向提供详细、专业的分析内容", "word_count": "2800-3500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "投资与融资分析的投资机会", "level": 2, "children": [{"title": "重点投资领域", "level": 3, "children": [{"title": "要点分析", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为重点投资领域提供详细、专业的分析内容", "word_count": "2300-3000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "投资风险评估", "level": 3, "children": [{"title": "要点分析", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为投资风险评估提供详细、专业的分析内容", "word_count": "2300-3000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为投资与融资分析的投资机会提供详细、专业的分析内容", "word_count": "2800-3500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为投资与融资分析提供详细、专业的分析内容", "word_count": "3000-4000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "未来发展展望", "level": 1, "children": [{"title": "未来发展展望的现状分析", "level": 2, "children": [{"title": "关键要素分析", "level": 3, "children": [{"title": "要点分析", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为关键要素分析提供详细、专业的分析内容", "word_count": "2300-3000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "发展建议", "level": 3, "children": [{"title": "要点分析", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为发展建议提供详细、专业的分析内容", "word_count": "2300-3000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为未来发展展望的现状分析提供详细、专业的分析内容", "word_count": "2800-3500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "未来发展展望的发展方向", "level": 2, "children": [{"title": "关键要素分析", "level": 3, "children": [{"title": "要点分析", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为关键要素分析提供详细、专业的分析内容", "word_count": "2300-3000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "发展建议", "level": 3, "children": [{"title": "要点分析", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 4, "children": [{"title": "要点分析", "level": 5, "children": [], "task_instruction": {"content_requirements": "为要点分析提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}, {"title": "深度解读", "level": 5, "children": [], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1500-2000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为深度解读提供详细、专业的分析内容", "word_count": "1800-2500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为发展建议提供详细、专业的分析内容", "word_count": "2300-3000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为未来发展展望的发展方向提供详细、专业的分析内容", "word_count": "2800-3500", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}], "task_instruction": {"content_requirements": "为未来发展展望提供详细、专业的分析内容", "word_count": "3000-4000", "structure": "采用逻辑清晰的结构，包含要点分析和总结", "data_usage": "充分利用数据源中的相关信息和数据"}}]}, "version": "1.0"}