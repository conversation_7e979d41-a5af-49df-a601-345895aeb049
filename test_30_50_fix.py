#!/usr/bin/env python3
"""
测试30-50字的逗号修复
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from complete_report_generator import safe_json_loads
import json

def test_30_50_comma_fix():
    """测试30-50字的逗号修复"""
    
    print("🔧 测试30-50字的逗号修复")
    print("=" * 60)
    
    # 根据实际错误构造的问题JSON
    # 模拟清理后的状态，word_count后面缺少逗号
    problem_json = '''{
    "instructions": {
        "节点001": {
            "title": "技术发展趋势",
            "content_requirements": "",
            "word_count": "30-50字"
        },
        "节点005": {
            "title": "要点分析",
            "content_requirements": "详细分析要点",
            "word_count": "50-80字"
        }
    }
}'''
    
    print("📝 测试30-50字逗号问题")
    print("原始JSON:")
    print(problem_json)
    print()
    
    # 标准解析测试
    print("❌ 标准json.loads解析:")
    try:
        result = json.loads(problem_json)
        print(f"✅ 标准解析成功: {len(result.get('instructions', {}))} 个指导")
    except json.JSONDecodeError as e:
        print(f"💥 标准解析失败: {e}")
        print(f"   错误位置: 行{e.lineno}, 列{e.colno}")
    
    print()
    
    # 修复解析测试
    print("✅ safe_json_loads解析:")
    try:
        result = safe_json_loads(problem_json)
        if result:
            print("🎯 修复解析成功!")
            instructions = result.get("instructions", {})
            print(f"   📊 指导数量: {len(instructions)}")
            for node_id, instruction in instructions.items():
                title = instruction.get("title", "无标题")
                word_count = instruction.get("word_count", "")
                content_req = instruction.get("content_requirements", "")
                print(f"   📋 {node_id}: {title}")
                print(f"      字数要求: {word_count}")
                print(f"      内容要求: {content_req}")
        else:
            print("❌ 修复解析失败，返回空结果")
    except Exception as e:
        print(f"❌ 修复解析异常: {e}")
    
    print()

def test_problematic_json_exact():
    """测试精确的问题JSON"""
    
    print("🎯 测试精确的问题JSON")
    print("=" * 60)
    
    # 根据错误信息构造的精确问题JSON
    # 错误位置：行17, 列81 - 这意味着在第17行的第81个字符位置有问题
    problematic_json = '''{
    "instructions": {
        "节点001": {
            "title": "技术发展趋势",
            "content_requirements": "",
            "word_count": "30-50字"},
        "节点005": {
            "title": "要点分析",
            "content_requirements": "详细分析要点",
            "word_count": "50-80字"
        },
        "节点010": {
            "title": "市场前景",
            "content_requirements": "",
            "word_count": "80-100字"
        }
    }
}'''
    
    print("📝 精确问题JSON（模拟实际错误）:")
    lines = problematic_json.split('\n')
    for i, line in enumerate(lines, 1):
        print(f"{i:2d}: {line}")
    print()
    
    # 标准解析
    print("❌ 标准json.loads解析:")
    try:
        result = json.loads(problematic_json)
        print(f"✅ 标准解析成功: {len(result.get('instructions', {}))} 个指导")
    except json.JSONDecodeError as e:
        print(f"💥 标准解析失败: {e}")
        print(f"   错误位置: 行{e.lineno}, 列{e.colno}")
        
        # 显示错误位置
        if e.lineno <= len(lines):
            error_line = lines[e.lineno - 1]
            print(f"   错误行: {error_line}")
            if e.colno <= len(error_line):
                print(f"   错误字符: '{error_line[e.colno-1:e.colno+5]}'")
    
    print()
    
    # 修复解析
    print("✅ safe_json_loads解析:")
    try:
        result = safe_json_loads(problematic_json)
        if result:
            print("🎯 修复解析成功!")
            instructions = result.get("instructions", {})
            print(f"   📊 指导数量: {len(instructions)}")
            for node_id, instruction in instructions.items():
                title = instruction.get("title", "无标题")
                word_count = instruction.get("word_count", "")
                print(f"   📋 {node_id}: {title} ({word_count})")
        else:
            print("❌ 修复解析失败")
    except Exception as e:
        print(f"❌ 修复解析异常: {e}")

if __name__ == "__main__":
    test_30_50_comma_fix()
    test_problematic_json_exact()
    
    print()
    print("🎉 测试完成！")
    print("📋 如果修复成功，应该能够正确解析包含30-50字等各种字数要求的JSON")
