#!/usr/bin/env python3
"""
测试图片嵌入修复功能
"""

import sys
import warnings
from pathlib import Path
import tempfile
import shutil

# 禁用matplotlib字体警告
warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')

def create_test_images():
    """创建测试图片"""
    print("🖼️ 创建测试图片...")
    
    try:
        from complete_report_generator import ChartGenerator, MATPLOTLIB_AVAILABLE
        
        if not MATPLOTLIB_AVAILABLE:
            print("❌ matplotlib不可用，无法创建测试图片")
            return []
        
        # 创建测试图片目录
        test_images_dir = Path("test_images_embedding")
        test_images_dir.mkdir(exist_ok=True)
        
        # 创建图表生成器
        chart_generator = ChartGenerator(test_images_dir)
        
        # 生成测试图片
        test_charts = []
        
        # 1. 产业链图表
        chart_path = chart_generator.generate_industry_chain_chart("测试主题", {})
        if chart_path and Path(chart_path).exists():
            test_charts.append({
                'path': chart_path,
                'description': '产业链结构图',
                'type': 'industry_chain'
            })
        
        # 2. 市场规模图表
        chart_path = chart_generator.generate_market_size_chart("测试主题", {})
        if chart_path and Path(chart_path).exists():
            test_charts.append({
                'path': chart_path,
                'description': '市场规模分析图',
                'type': 'market_size'
            })
        
        # 3. 技术趋势图表
        chart_path = chart_generator.generate_technology_trend_chart("测试主题", {})
        if chart_path and Path(chart_path).exists():
            test_charts.append({
                'path': chart_path,
                'description': '技术发展趋势图',
                'type': 'tech_trend'
            })
        
        print(f"✅ 成功创建 {len(test_charts)} 个测试图片")
        return test_charts
        
    except Exception as e:
        print(f"❌ 创建测试图片失败: {e}")
        return []

def test_image_placeholder_insertion():
    """测试图片占位符插入"""
    print("\n📝 测试图片占位符插入...")
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        # 创建报告生成器
        generator = CompleteReportGenerator()
        
        # 创建测试图片
        test_images = create_test_images()
        if not test_images:
            print("❌ 没有测试图片可用")
            return False
        
        # 测试内容
        test_content = """
这是一个测试章节的内容。

本章节将分析相关的市场情况和技术发展趋势。

通过深入研究，我们可以得出以下结论。
"""
        
        # 测试图片占位符插入
        enhanced_content = generator._insert_image_placeholders_to_content(
            test_content, test_images
        )
        
        print("原始内容:")
        print(test_content)
        print("\n增强后内容:")
        print(enhanced_content)
        
        # 检查是否包含图片占位符
        has_placeholders = "[IMAGE:" in enhanced_content
        print(f"\n包含图片占位符: {has_placeholders}")
        
        return has_placeholders
        
    except Exception as e:
        print(f"❌ 图片占位符插入测试失败: {e}")
        return False

def test_docx_image_processing():
    """测试DOCX图片处理"""
    print("\n📄 测试DOCX图片处理...")
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        # 创建报告生成器
        generator = CompleteReportGenerator()
        
        # 创建测试图片
        test_images = create_test_images()
        if not test_images:
            print("❌ 没有测试图片可用")
            return False
        
        # 创建包含图片占位符的测试内容
        test_content = f"""
# 测试报告

这是一个测试章节。

[IMAGE:{test_images[0]['path']},{test_images[0]['description']}]

这里是更多内容。

[IMAGE:{test_images[1]['path']},{test_images[1]['description']}]

结论部分。
"""
        
        # 测试DOCX生成
        test_docx_path = Path("test_image_embedding.docx")
        
        try:
            from docx import Document
            
            doc = Document()
            doc.add_heading("测试报告", 0)
            
            # 处理图片占位符
            generator._process_image_placeholders(doc, test_content, [])
            
            # 保存文档
            doc.save(str(test_docx_path))
            
            if test_docx_path.exists():
                size_kb = test_docx_path.stat().st_size / 1024
                print(f"✅ DOCX文档生成成功: {test_docx_path} ({size_kb:.1f} KB)")
                return True
            else:
                print("❌ DOCX文档生成失败")
                return False
                
        except ImportError:
            print("⚠️ python-docx不可用，跳过DOCX测试")
            return True
        
    except Exception as e:
        print(f"❌ DOCX图片处理测试失败: {e}")
        return False

def test_markdown_image_processing():
    """测试Markdown图片处理"""
    print("\n📝 测试Markdown图片处理...")
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        # 创建报告生成器
        generator = CompleteReportGenerator()
        
        # 创建测试图片
        test_images = create_test_images()
        if not test_images:
            print("❌ 没有测试图片可用")
            return False
        
        # 创建包含图片占位符的测试内容
        test_content = f"""
# 测试报告

这是一个测试章节。

[IMAGE:{test_images[0]['path']},{test_images[0]['description']}]

这里是更多内容。

[IMAGE:{test_images[1]['path']},{test_images[1]['description']}]

结论部分。
"""
        
        # 处理Markdown图片
        processed_content = generator._process_markdown_content_with_images(test_content)
        
        print("原始内容:")
        print(test_content[:200] + "...")
        print("\n处理后内容:")
        print(processed_content[:400] + "...")
        
        # 检查是否包含Markdown图片语法
        has_markdown_images = "![" in processed_content and "](" in processed_content
        print(f"\n包含Markdown图片语法: {has_markdown_images}")
        
        # 保存测试Markdown文件
        test_md_path = Path("test_image_embedding.md")
        with open(test_md_path, 'w', encoding='utf-8') as f:
            f.write(processed_content)
        
        if test_md_path.exists():
            size_kb = test_md_path.stat().st_size / 1024
            print(f"✅ Markdown文档生成成功: {test_md_path} ({size_kb:.1f} KB)")
        
        return has_markdown_images
        
    except Exception as e:
        print(f"❌ Markdown图片处理测试失败: {e}")
        return False

def test_image_path_resolution():
    """测试图片路径解析"""
    print("\n🔍 测试图片路径解析...")
    
    try:
        # 创建测试图片
        test_images = create_test_images()
        if not test_images:
            print("❌ 没有测试图片可用")
            return False
        
        # 测试路径解析
        for img in test_images:
            img_path = Path(img['path'])
            print(f"图片路径: {img_path}")
            print(f"  - 存在: {img_path.exists()}")
            print(f"  - 绝对路径: {img_path.is_absolute()}")
            print(f"  - 文件名: {img_path.name}")
            print(f"  - 大小: {img_path.stat().st_size / 1024:.1f} KB")
        
        return True
        
    except Exception as e:
        print(f"❌ 图片路径解析测试失败: {e}")
        return False

def cleanup_test_files():
    """清理测试文件"""
    print("\n🧹 清理测试文件...")
    
    cleanup_paths = [
        "test_images_embedding",
        "test_image_embedding.docx",
        "test_image_embedding.md"
    ]
    
    for path_str in cleanup_paths:
        path = Path(path_str)
        try:
            if path.is_dir():
                shutil.rmtree(path)
                print(f"  ✅ 删除目录: {path}")
            elif path.exists():
                path.unlink()
                print(f"  ✅ 删除文件: {path}")
        except Exception as e:
            print(f"  ⚠️ 删除失败 {path}: {e}")

def main():
    """主测试函数"""
    print("🚀 开始测试图片嵌入修复功能...")
    print("=" * 60)
    
    tests = [
        ("图片占位符插入测试", test_image_placeholder_insertion),
        ("DOCX图片处理测试", test_docx_image_processing),
        ("Markdown图片处理测试", test_markdown_image_processing),
        ("图片路径解析测试", test_image_path_resolution),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 40)
        try:
            if test_func():
                print(f"✅ {test_name} 通过")
                passed += 1
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    # 清理测试文件
    cleanup_test_files()
    
    if passed == total:
        print("🎉 所有测试通过！图片嵌入功能正常！")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
