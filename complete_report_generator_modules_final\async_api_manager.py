"""
异步API管理模块 - 异步Gemini API管理器
从complete_report_generator.py中提取的完整AsyncGeminiAPIManager类
"""
import asyncio
import time
import concurrent.futures
from typing import Dict, Any, List, Tuple, Optional
import google.generativeai as genai

# 导入基础模块
from api_manager import BaseGeminiAPIManager, AsyncConfig
from config import GeminiModelConfig


class AsyncGeminiAPIManager(BaseGeminiAPIManager):
    """
    异步API管理器 - 智能配额管理版本
    添加配额监控、速率限制和智能降级策略
    """

    def __init__(self, api_keys: List[str], model_names: List[str], model_config: GeminiModelConfig = None):
        # 保持原有的API配置逻辑
        self.api_configs = []

        for i, key in enumerate(api_keys):
            if key and "YOUR_GEMINI_API_KEY" not in key and len(key) > 10:
                self.api_configs.append({
                    "name": f"API Key {i+1}",
                    "key": key,
                    "models": model_names,
                    "current_model_index": 0,
                    "semaphore": asyncio.Semaphore(1),  # 每个密钥最大并发=1
                    # 精确的配额管理（基于实际API限制）
                    "quota_used": 0,
                    "quota_limit": 250000,  # TPM限制
                    "quota_reset_time": 0,
                    "requests_per_minute": 0,
                    "requests_per_day": 0,
                    "daily_reset_time": 0,
                    "last_request_time": 0,
                    # 按模型分别跟踪RPM
                    "pro_requests_this_minute": 0,
                    "flash_requests_this_minute": 0,
                    "pro_minute_reset": 0,
                    "flash_minute_reset": 0,
                    # 错误跟踪
                    "error_count": 0,
                    "last_error_time": 0,
                    "is_available": True,
                    "consecutive_quota_errors": 0,
                    "backoff_until": 0  # 退避时间
                })

        if not self.api_configs:
            raise ValueError("FATAL: No valid Google API keys are configured.")

        self.total_keys = len(self.api_configs)
        self.current_api_index = 0
        self.index_lock = asyncio.Lock()
        self.max_rotations = 10000
        self.total_rotations_completed = 0
        self.usage_counts = {i: 0 for i in range(self.total_keys)}
        self.consecutive_cleanup_counts = {i: 0 for i in range(self.total_keys)}
        self.api_call_counts = {i: 0 for i in range(self.total_keys)}
        self.success_counts = {i: 0 for i in range(self.total_keys)}

        # 模型参数配置
        self.model_config = model_config or GeminiModelConfig()

        # 全局速率限制 - 使用API密钥数量作为最大并发数
        self.global_rate_limiter = asyncio.Semaphore(self.total_keys)  # 全局最大并发等于API密钥数
        self.last_global_request = 0
        self.min_request_interval = 5.0  # 最小请求间隔降低到1秒

        # 全局配额管理
        import time
        self.global_quota_exhausted = False
        self.global_quota_reset_time = time.time()
        self.consecutive_global_failures = 0
        self.last_successful_request = time.time()

        print(f"🔧 智能异步API管理器初始化完成:")
        print(f"   可用API密钥: {self.total_keys} 个")
        print(f"   每密钥并发: 1")
        print(f"   全局最大并发: {self.total_keys}")
        print(f"   最小请求间隔: {self.min_request_interval}秒")
        print(f"   配额监控: 启用")

        # 显示性能信息
        perf_info = AsyncConfig.get_performance_info()
        print(f"📊 性能配置:")
        print(f"   预计总调用: {perf_info['estimated_total_api_calls']} 次")
        print(f"   预计加速: 保守估计2-3x（智能限速）")

        if self.total_keys > 0:
            self._log_current_key_status()

    def _log_current_key_status(self):
        """记录当前密钥状态"""
        print(f"\n--- 所有API密钥状态 (已完成轮换: {self.total_rotations_completed}/{self.max_rotations}) ---")
        for i in range(self.total_keys):
            config = self.api_configs[i]
            status = "🔴 当前" if i == self.current_api_index else "⚪️ 待用"
            current_model = config['models'][config['current_model_index']]
            print(f" {status} {config['name']}: 模型='{current_model}', "
                  f"成功处理={self.usage_counts[i]}, "
                  f"API总调用={self.api_call_counts[i]}")
        print("-----------------------------------------------------------\n")

    async def _get_available_api_config(self) -> Optional[Dict[str, Any]]:
        """获取可用的API配置（强制使用所有API版本）"""
        import time
        current_time = time.time()

        # 首先检查全局配额状态
        if not self._check_global_quota_status():
            print(f"🚨 全局配额耗尽，无法获取API配置")
            return None

        async with self.index_lock:
            if self.total_rotations_completed >= self.max_rotations:
                raise Exception(f"已完成 {self.max_rotations} 轮完整的API密钥轮换，程序终止。")

            if self.total_keys == 0:
                raise Exception("No configured API keys to use for generation.")

            # 强制重置策略：每次调用都尝试重置API状态以确保最大并发
            self._reset_apis_for_max_concurrency()

            # 第一轮：寻找完全可用的API（放宽条件）
            attempts = 0
            available_apis = []

            while attempts < self.total_keys:
                api_config = self.api_configs[self.current_api_index]

                # 放宽退避期检查：只检查严重退避（超过5分钟）
                if current_time < api_config.get("backoff_until", 0):
                    backoff_remaining = api_config.get("backoff_until", 0) - current_time
                    if backoff_remaining > 300:  # 只有超过5分钟的退避才跳过
                        print(f"   ⏳ {api_config['name']} 长期退避中，跳过")
                        self.current_api_index = (self.current_api_index + 1) % self.total_keys
                        attempts += 1
                        continue
                    else:
                        # 短期退避，强制重置
                        api_config["backoff_until"] = 0
                        print(f"   🔄 {api_config['name']} 短期退避已重置")

                # 放宽配额检查：只在真正耗尽时才跳过
                if self._is_quota_severely_exhausted(api_config, current_time):
                    print(f"   📊 {api_config['name']} 配额严重耗尽，跳过")
                    self.current_api_index = (self.current_api_index + 1) % self.total_keys
                    attempts += 1
                    continue

                # 放宽可用性检查：只要信号量未锁定就可用
                if not api_config["semaphore"].locked():
                    # 找到可用的API
                    model_name = api_config["models"][api_config["current_model_index"]]
                    result = {
                        "api_index": self.current_api_index,
                        "api_name": api_config["name"],
                        "api_key": api_config["key"],
                        "model_name": model_name,
                        "semaphore": api_config["semaphore"]
                    }

                    # 移动到下一个API（负载均衡）
                    self.current_api_index = (self.current_api_index + 1) % self.total_keys
                    return result

                # 记录可能可用的API
                available_apis.append((self.current_api_index, api_config))

                # 尝试下一个API
                self.current_api_index = (self.current_api_index + 1) % self.total_keys
                attempts += 1

            # 第二轮：如果没有完全可用的API，尝试使用有轻微问题的API
            if available_apis:
                print(f"   🔄 所有API都有问题，尝试使用最佳可用API")
                # 选择错误最少的API
                best_api = min(available_apis,
                             key=lambda x: x[1].get("consecutive_quota_errors", 0))
                api_index, api_config = best_api

                model_name = api_config["models"][api_config["current_model_index"]]
                result = {
                    "api_index": api_index,
                    "api_name": api_config["name"],
                    "api_key": api_config["key"],
                    "model_name": model_name,
                    "semaphore": api_config["semaphore"]
                }
                return result

            # 第三轮：智能处理全局配额耗尽
            print(f"   🚨 所有API都不可用，检查全局配额状态")

            # 检查是否是全局配额问题
            quota_exhausted_count = sum(1 for config in self.api_configs
                                      if config.get("consecutive_quota_errors", 0) >= 2)  # 降低阈值

            # 更严格的全局配额检测
            if quota_exhausted_count >= self.total_keys * 0.6:  # 60%的API都有配额问题
                print(f"   🚨 检测到全局配额耗尽（{quota_exhausted_count}/{self.total_keys} API配额错误）")
                self._handle_global_quota_exhaustion()
                self.global_quota_exhausted = True
                return None

            # 检查最近是否刚刚强制重置过
            current_time = time.time()
            if hasattr(self, 'last_force_reset_time') and (current_time - self.last_force_reset_time) < 300:  # 5分钟内
                print(f"   🚨 5分钟内已强制重置过，判定为全局配额耗尽")
                self._handle_global_quota_exhaustion()
                self.global_quota_exhausted = True
                return None

            # 如果不是全局配额问题，执行强制重置
            print(f"   🔄 执行强制重置（非全局配额问题）")
            await self._force_reset_all_apis()
            self.last_force_reset_time = current_time

            # 使用第一个API，但添加保护机制
            if self.api_configs:
                api_config = self.api_configs[0]
                model_name = api_config["models"][api_config["current_model_index"]]
                result = {
                    "api_index": 0,
                    "api_name": api_config["name"],
                    "api_key": api_config["key"],
                    "model_name": model_name,
                    "semaphore": api_config["semaphore"]
                }
                print(f"   ⚠️ 强制使用 {api_config['name']}（最后尝试）")
                return result

            return None

    def _reset_apis_for_max_concurrency(self):
        """重置API状态以确保最大并发（强制版本）"""
        import time
        current_time = time.time()

        reset_count = 0
        for config in self.api_configs:
            # 重置轻微的错误状态
            if config.get("consecutive_quota_errors", 0) < 10:  # 只保留严重错误的API
                config["consecutive_quota_errors"] = 0
                config["is_available"] = True
                reset_count += 1

            # 重置短期退避
            if config.get("backoff_until", 0) - current_time < 300:  # 5分钟内的退避
                config["backoff_until"] = 0
                reset_count += 1

        if reset_count > 0:
            print(f"🔄 为最大并发重置了 {reset_count} 个API状态")

    def _force_reset_all_apis_for_max_concurrency(self):
        """强制重置所有API状态（外部调用接口）"""
        self._reset_apis_for_max_concurrency()

    def _is_quota_severely_exhausted(self, api_config: Dict[str, Any], current_time: float) -> bool:
        """检查API配额是否严重耗尽（更严格的标准）"""
        # 检查是否需要重置配额计数器（每分钟重置）
        if current_time - api_config.get("quota_reset_time", 0) > 60:
            api_config["quota_used"] = 0
            api_config["requests_per_minute"] = 0
            api_config["pro_requests_this_minute"] = 0
            api_config["flash_requests_this_minute"] = 0
            api_config["quota_reset_time"] = current_time

        # 检查是否需要重置每日计数器
        if current_time - api_config.get("daily_reset_time", 0) > 86400:  # 24小时
            api_config["requests_per_day"] = 0
            api_config["daily_reset_time"] = current_time

        # 严格的配额检查 - 只在真正接近限制时才返回True

        # 检查PRO模型的RPM限制（接近实际限制）
        if api_config.get("pro_requests_this_minute", 0) >= 4:  # 接近实际限制：5次/分钟
            return True

        # 检查FLASH模型的RPM限制（接近实际限制）
        if api_config.get("flash_requests_this_minute", 0) >= 9:  # 接近实际限制：10次/分钟
            return True

        # 检查总的每分钟请求数（严格限制）
        if api_config.get("requests_per_minute", 0) >= 10:  # 严格限制
            return True

        # 检查每日请求数限制（严格限制）
        if api_config.get("requests_per_day", 0) >= 100:  # 严格限制
            return True

        # 检查连续配额错误次数（严重错误才算耗尽）
        if api_config.get("consecutive_quota_errors", 0) >= 15:  # 提高阈值
            return True

        return False

    def _is_quota_exhausted(self, api_config: Dict[str, Any], current_time: float) -> bool:
        """检查API配额是否耗尽（宽松版本，防止卡死）"""
        # 检查是否需要重置配额计数器（每分钟重置）
        if current_time - api_config.get("quota_reset_time", 0) > 60:
            api_config["quota_used"] = 0
            api_config["requests_per_minute"] = 0
            api_config["pro_requests_this_minute"] = 0
            api_config["flash_requests_this_minute"] = 0
            api_config["quota_reset_time"] = current_time

        # 检查是否需要重置每日计数器
        if current_time - api_config.get("daily_reset_time", 0) > 86400:  # 24小时
            api_config["requests_per_day"] = 0
            api_config["daily_reset_time"] = current_time

        # 宽松的配额检查 - 只在真正接近限制时才返回True

        # 检查PRO模型的RPM限制（放宽到接近实际限制）
        if api_config.get("pro_requests_this_minute", 0) >= 5:  # 接近实际限制：5次/分钟
            return True

        # 检查FLASH模型的RPM限制（放宽到接近实际限制）
        if api_config.get("flash_requests_this_minute", 0) >= 10:  # 接近实际限制：10次/分钟
            return True

        # 检查总的每分钟请求数（放宽限制）
        if api_config.get("requests_per_minute", 0) >= 12:  # 放宽到12次/分钟
            return True

        # 检查每日请求数限制（放宽限制）
        if api_config.get("requests_per_day", 0) >= 150:  # 放宽到150次/天
            return True

        # 检查token配额使用量（放宽阈值）
        if api_config.get("quota_used", 0) >= api_config.get("quota_limit", 250000) * 1.0:  # 90%阈值
            return True

        return False

    def _update_quota_usage(self, api_config: Dict[str, Any], model_name: str, estimated_tokens: int):
        """更新配额使用情况"""
        import time
        current_time = time.time()

        # 更新token使用量
        api_config["quota_used"] = api_config.get("quota_used", 0) + estimated_tokens

        # 更新请求计数
        api_config["requests_per_minute"] = api_config.get("requests_per_minute", 0) + 1
        api_config["requests_per_day"] = api_config.get("requests_per_day", 0) + 1

        # 根据模型类型更新特定计数
        if "pro" in model_name.lower():
            api_config["pro_requests_this_minute"] = api_config.get("pro_requests_this_minute", 0) + 1
        else:
            api_config["flash_requests_this_minute"] = api_config.get("flash_requests_this_minute", 0) + 1

        api_config["last_request_time"] = current_time

    async def _reset_error_states(self):
        """重置API错误状态（智能版）"""
        import time
        current_time = time.time()
        async with self.index_lock:
            reset_count = 0
            for config in self.api_configs:
                # 如果距离上次错误超过5分钟，重置状态
                if current_time - config.get("last_error_time", 0) > 300:
                    config["error_count"] = 0
                    config["consecutive_quota_errors"] = 0
                    config["is_available"] = True
                    config["backoff_until"] = 0
                    reset_count += 1

            if reset_count > 0:
                print(f"🔄 重置了 {reset_count} 个API的错误状态")

    async def _force_reset_all_apis(self):
        """强制重置所有API状态（防卡死机制）"""
        import time
        current_time = time.time()

        async with self.index_lock:
            print(f"🚨 强制重置所有API状态")
            for i, config in enumerate(self.api_configs):
                # 强制重置所有状态
                config["error_count"] = 0
                config["consecutive_quota_errors"] = 0
                config["is_available"] = True
                config["backoff_until"] = 0
                config["last_error_time"] = 0

                # 重置配额计数器
                config["quota_used"] = 0
                config["requests_per_minute"] = 0
                config["pro_requests_this_minute"] = 0
                config["flash_requests_this_minute"] = 0
                config["requests_per_day"] = 0
                config["quota_reset_time"] = current_time
                config["daily_reset_time"] = current_time

                print(f"   ✅ 重置 {config['name']}")

            # 重置轮换计数
            self.current_api_index = 0
            print(f"🔄 强制重置完成，重新从第一个API开始")

    def _log_api_status_summary(self):
        """记录API状态摘要（防卡死诊断）"""
        import time
        current_time = time.time()

        available_count = 0
        backoff_count = 0
        quota_exhausted_count = 0
        error_count = 0

        for config in self.api_configs:
            if current_time < config.get("backoff_until", 0):
                backoff_count += 1
            elif self._is_quota_exhausted(config, current_time):
                quota_exhausted_count += 1
            elif not config.get("is_available", True):
                error_count += 1
            else:
                available_count += 1

        print(f"📊 API状态摘要: 可用={available_count}, 退避={backoff_count}, 配额耗尽={quota_exhausted_count}, 错误={error_count}")

        if available_count == 0:
            print(f"🚨 警告：没有可用的API！")
            # 显示详细状态
            for i, config in enumerate(self.api_configs):
                status = "❌"
                reason = ""
                if current_time < config.get("backoff_until", 0):
                    remaining = int(config.get("backoff_until", 0) - current_time)
                    reason = f"退避中({remaining}秒)"
                elif self._is_quota_exhausted(config, current_time):
                    reason = "配额耗尽"
                elif not config.get("is_available", True):
                    reason = "错误过多"
                else:
                    status = "✅"
                    reason = "可用"

                print(f"   {status} API {i+1}: {reason}")

        return available_count > 0

    def get_currently_available_api_count(self) -> int:
        """获取当前实际可用的API数量（动态检测）"""
        import time
        current_time = time.time()

        available_count = 0
        for config in self.api_configs:
            # 检查API是否真正可用
            if (current_time >= config.get("backoff_until", 0) and  # 不在退避期
                config.get("is_available", True) and              # 标记为可用
                not config["semaphore"].locked() and              # 信号量未被锁定
                config.get("consecutive_quota_errors", 0) < 5):   # 配额错误次数不多
                available_count += 1

        return available_count

    def get_api_status_summary(self) -> dict:
        """获取API状态摘要"""
        import time
        current_time = time.time()

        status = {
            "total": self.total_keys,
            "available": 0,
            "backoff": 0,
            "quota_exhausted": 0,
            "error": 0,
            "locked": 0
        }

        for config in self.api_configs:
            if current_time < config.get("backoff_until", 0):
                status["backoff"] += 1
            elif self._is_quota_exhausted(config, current_time):
                status["quota_exhausted"] += 1
            elif not config.get("is_available", True):
                status["error"] += 1
            elif config["semaphore"].locked():
                status["locked"] += 1
            else:
                status["available"] += 1

        return status

    def _check_global_quota_status(self) -> bool:
        """检查全局配额状态"""
        import time
        current_time = time.time()

        # 检查是否需要重置全局配额状态（每小时重置）
        if current_time - self.global_quota_reset_time > 3600:  # 1小时
            self.global_quota_exhausted = False
            self.consecutive_global_failures = 0
            self.global_quota_reset_time = current_time
            print(f"🔄 全局配额状态已重置")

        # 检查最近是否有成功请求
        time_since_success = current_time - self.last_successful_request
        if time_since_success > 1800:  # 30分钟没有成功请求
            print(f"⚠️ 已经 {time_since_success/60:.1f} 分钟没有成功的API请求")

        # 修复：只有在未被手动重置的情况下才检查连续失败次数
        if not hasattr(self, '_manual_quota_reset_time'):
            self._manual_quota_reset_time = 0

        # 如果最近手动重置过，给一些缓冲时间
        if current_time - self._manual_quota_reset_time < 300:  # 5分钟内手动重置过
            return True

        # 如果连续失败次数过多，标记为全局配额耗尽（提高阈值避免误判）
        if self.consecutive_global_failures >= 200:  # 大幅提高阈值，避免过早判定
            self.global_quota_exhausted = True
            print(f"🚨 检测到全局配额耗尽（连续{self.consecutive_global_failures}次失败）")

        return not self.global_quota_exhausted

    def _handle_global_quota_exhaustion(self):
        """处理全局配额耗尽（严格版本）"""
        import time
        current_time = time.time()

        print(f"🚨 全局配额耗尽处理启动")
        print(f"📊 当前状态: 连续失败{self.consecutive_global_failures}次")

        # 计算建议等待时间
        time_since_reset = current_time - self.global_quota_reset_time
        remaining_time = max(0, 3600 - time_since_reset)  # 距离下次重置的时间

        if remaining_time > 0:
            wait_minutes = remaining_time / 60
            print(f"⏰ 所有API配额已耗尽，建议等待 {wait_minutes:.1f} 分钟后重试")
        else:
            print(f"⏰ 配额应该已重置，但仍遇到限制，建议检查API设置")

        print(f"💡 解决方案:")
        print(f"   1. 等待配额重置（每小时重置）")
        print(f"   2. 检查API计费设置和配额限制")
        print(f"   3. 考虑升级API套餐获得更高配额")
        print(f"   4. 暂时使用备用内容继续工作")

        # 设置所有API为长时间退避
        for config in self.api_configs:
            config["backoff_until"] = current_time + 3600  # 1小时退避
            config["consecutive_quota_errors"] = 20  # 标记为严重错误
            config["is_available"] = False

        self.global_quota_exhausted = True

        # 记录全局配额耗尽时间
        self.global_quota_exhausted_time = current_time

    async def _mark_api_error(self, api_index: int, error_msg: str):
        """标记API错误（全局配额感知版本）"""
        import time
        current_time = time.time()

        async with self.index_lock:
            if api_index < len(self.api_configs):
                config = self.api_configs[api_index]
                config["error_count"] = config.get("error_count", 0) + 1
                config["last_error_time"] = current_time

                # 检查是否是配额错误
                if any(keyword in error_msg for keyword in ["quota", "limit", "permission", "resource_exhausted", "429"]):
                    config["consecutive_quota_errors"] = config.get("consecutive_quota_errors", 0) + 1
                    self.consecutive_global_failures += 1  # 增加全局失败计数

                    # 动态调整退避时间（基于全局状态）
                    if self.consecutive_global_failures >= 15:
                        # 全局配额问题，延长退避时间
                        backoff_time = min(300, 60 + self.consecutive_global_failures * 10)  # 最多5分钟
                        config["backoff_until"] = current_time + backoff_time
                        config["is_available"] = False
                        print(f"⚠️ API {config['name']} 全局配额问题，退避{backoff_time}秒")
                    elif config["consecutive_quota_errors"] >= 5:
                        # 退避2分钟
                        config["backoff_until"] = current_time + 180
                        config["is_available"] = False
                        print(f"⚠️ API {config['name']} 连续配额错误，退避3分钟")
                    elif config["consecutive_quota_errors"] >= 3:
                        # 退避1分钟
                        config["backoff_until"] = current_time + 60
                        print(f"⚠️ API {config['name']} 配额错误，退避1分钟")
                    elif config["consecutive_quota_errors"] >= 2:
                        # 退避30秒
                        config["backoff_until"] = current_time + 30
                        print(f"⚠️ API {config['name']} 配额错误，退避30秒")
                    else:
                        # 退避15秒
                        config["backoff_until"] = current_time + 15
                        print(f"⚠️ API {config['name']} 配额错误，退避15秒")

                # 如果错误次数过多，暂时禁用
                elif config["error_count"] >= 8:
                    config["is_available"] = False
                    config["backoff_until"] = current_time + 120
                    print(f"⚠️ API {config['name']} 暂时禁用（错误次数过多）")
                else:
                    # 非配额错误，增加全局失败计数但权重较小
                    self.consecutive_global_failures += 0.5

    async def generate_content_with_model_async(self, prompt: str, model_name: str) -> Tuple[Any, int]:
        """异步生成内容，使用指定模型（全局配额感知版）"""

        # 修复：检查全局配额状态，但避免无限循环
        if self.global_quota_exhausted:
            print(f"🚨 全局配额状态已标记为耗尽，执行智能重置")
            # 记录手动重置时间，避免立即再次被标记为耗尽
            import time
            self._manual_quota_reset_time = time.time()
            self.global_quota_exhausted = False  # 重置状态
            self.consecutive_global_failures = 0  # 重置失败计数
            # 强制重置所有API状态
            await self._force_reset_all_apis()
            print(f"✅ 全局配额状态已智能重置，继续尝试")

        # 检查连续失败次数 - 但不要过于频繁地重置
        if self.consecutive_global_failures >= 50:  # 提高阈值
            print(f"🚨 连续失败次数过多({self.consecutive_global_failures})，执行深度重置")
            # 记录手动重置时间
            import time
            self._manual_quota_reset_time = time.time()
            self.consecutive_global_failures = 0  # 重置失败计数
            # 强制重置所有API状态
            await self._force_reset_all_apis()
            # 增加等待时间，避免立即重试
            await asyncio.sleep(10)

        base_delay = 5   # 增加基础延迟
        attempt = 0
        max_consecutive_failures = 50  # 最大连续失败次数，防止无限循环

        # 全局速率限制
        async with self.global_rate_limiter:
            # 确保最小请求间隔
            import time
            current_time = time.time()
            time_since_last = current_time - self.last_global_request
            if time_since_last < self.min_request_interval:
                sleep_time = self.min_request_interval - time_since_last
                print(f"⏳ 全局速率限制，等待 {sleep_time:.1f} 秒...")
                await asyncio.sleep(sleep_time)

            self.last_global_request = time.time()

            while attempt < max_consecutive_failures:  # 改为while循环，但有上限防止无限循环
                # 获取可用的API配置
                api_config = await self._get_available_api_config()

                if api_config is None:
                    # 记录API状态摘要，帮助诊断问题
                    self._log_api_status_summary()

                    # 检查是否是全局配额耗尽 - 避免无限循环
                    if self.global_quota_exhausted:
                        print(f"🚨 全局配额状态已标记为耗尽，执行有限重置")
                        # 记录手动重置时间
                        import time
                        self._manual_quota_reset_time = time.time()
                        self.global_quota_exhausted = False
                        await self._force_reset_all_apis()  # 强制重置所有API状态
                        # 增加等待时间，避免立即重试
                        await asyncio.sleep(5)

                    if attempt < max_consecutive_failures - 1:
                        wait_time = min(base_delay * (2 ** (attempt // len(self.api_configs))), 30)  # 最大等待30秒
                        print(f"⚠️ 暂时无可用API，等待 {wait_time} 秒后重试...")
                        await asyncio.sleep(wait_time)

                        # 在重试前，尝试强制重置一些API状态
                        if attempt % 10 == 9:  # 每10次尝试重置一次API状态
                            print(f"🔄 重试前尝试重置API状态")
                            await self._reset_error_states()

                        attempt += 1  # 增加尝试计数
                        continue
                    else:
                        # 最后一次尝试，强制使用第一个API
                        print(f"🚨 最后一次尝试，强制使用第一个API")
                        if self.api_configs:
                            # 强制重置第一个API的状态
                            first_config = self.api_configs[0]
                            first_config["is_available"] = True
                            first_config["backoff_until"] = 0
                            first_config["consecutive_quota_errors"] = 0

                            api_config = {
                                "api_index": 0,
                                "api_name": first_config["name"],
                                "api_key": first_config["key"],
                                "model_name": model_name,
                                "semaphore": first_config["semaphore"]
                            }
                        else:
                            print(f"❌ 所有API都不可用，强制重置状态继续尝试")
                            await self._force_reset_all_apis()  # 强制重置所有API状态
                            # 继续循环，不返回备用内容

                api_index = api_config["api_index"]
                semaphore = api_config["semaphore"]

                # 使用信号量控制并发
                semaphore_acquired = False
                try:
                    # 获取信号量，使用较长的超时时间
                    try:
                        await asyncio.wait_for(semaphore.acquire(), timeout=30)
                        semaphore_acquired = True
                    except asyncio.TimeoutError:
                        print(f"⏰ 信号量获取超时: {api_config['api_name']}")
                        # 标记当前API为超时错误
                        await self._mark_api_error(api_index, "semaphore_timeout")
                        print(f"   → 信号量超时，切换到下一个API")
                        # 增加尝试计数并立即尝试下一个API
                        attempt += 1
                        continue

                    # 估算token使用量
                    estimated_tokens = len(prompt) // 4  # 简单估算

                    # 更新配额使用情况
                    async with self.index_lock:
                        self._update_quota_usage(self.api_configs[api_index], model_name, estimated_tokens)
                        self.api_call_counts[api_index] += 1

                    # 从prompt中提取任务信息
                    try:
                        task_purpose = self._extract_task_purpose(prompt)
                    except Exception:
                        task_purpose = f"🤖 AI模型执行任务"

                    print(f"\n[智能异步API调用] 使用: {api_config['api_name']} | 模型: {model_name}")
                    print(f"[调用统计] 总调用: {self.api_call_counts[api_index]} | 成功: {self.success_counts[api_index]}")
                    print(f"[任务目的] {task_purpose}")

                    # 显示prompt摘要
                    prompt_summary = prompt.replace('\n', ' ')[:100] + "..." if len(prompt) > 100 else prompt.replace('\n', ' ')
                    print(f"[任务内容] {prompt_summary}")

                    # 在线程池中执行同步的API调用
                    loop = asyncio.get_event_loop()

                    # 根据模型类型设置不同的超时时间
                    timeout = 1200 if "pro" in model_name.lower() else 900

                    response = await asyncio.wait_for(
                        loop.run_in_executor(
                            None,
                            self._sync_api_call,
                            api_config["api_key"],
                            model_name,
                            prompt
                        ),
                        timeout=timeout
                    )

                    # 记录成功
                    async with self.index_lock:
                        self.usage_counts[api_index] += 1
                        self.consecutive_cleanup_counts[api_index] = 0
                        self.success_counts[api_index] += 1
                        # 重置连续配额错误计数
                        self.api_configs[api_index]["consecutive_quota_errors"] = 0
                        # 重置全局失败计数
                        self.consecutive_global_failures = max(0, self.consecutive_global_failures - 2)
                        self.last_successful_request = time.time()
                        # 如果成功，重置全局配额耗尽状态
                        if self.global_quota_exhausted:
                            self.global_quota_exhausted = False
                            print(f"✅ 全局配额状态已恢复")

                    print(f"✅ API调用成功: {api_config['api_name']}")
                    # 成功时重置尝试计数器
                    attempt = 0
                    return response, api_index

                except asyncio.TimeoutError:
                    print(f"⏰ API调用超时: {api_config['api_name']} (超时时间: {timeout}秒)")
                    # 标记当前API为超时错误
                    await self._mark_api_error(api_index, "timeout")
                    print(f"   → 超时错误，切换到下一个API")
                    # 增加尝试计数并立即尝试下一个API
                    attempt += 1
                    continue

                except Exception as e:
                    error_msg = str(e).lower()
                    print(f"❌ API调用失败: {api_config['api_name']} - {error_msg}")

                    # 标记错误
                    await self._mark_api_error(api_index, error_msg)

                    # 检查是否是配额错误
                    if any(keyword in error_msg for keyword in ["quota", "limit", "permission", "resource_exhausted", "429"]):
                        print(f"   → 配额/权限问题，切换API")
                        # 立即尝试下一个API，不等待
                        continue
                    else:
                        # 其他错误，等待后重试
                        if attempt < max_consecutive_failures - 1:
                            wait_time = base_delay * (2 ** (attempt // len(self.api_configs)))
                            print(f"   → 其他错误，等待 {wait_time} 秒后重试")
                            await asyncio.sleep(wait_time)
                            attempt += 1  # 增加尝试计数
                            continue
                        else:
                            print(f"❌ 当前API所有重试都失败，切换到下一个API")
                            # 继续外层循环，尝试下一个API
                            attempt += 1  # 增加尝试计数
                            continue

                finally:
                    # 确保信号量总是被释放
                    if semaphore_acquired:
                        semaphore.release()

            # 所有API都失败时，避免无限递归
            print(f"❌ 所有API调用都失败，返回备用内容避免无限循环")
            # 记录手动重置时间
            import time
            self._manual_quota_reset_time = time.time()
            await self._force_reset_all_apis()  # 强制重置所有API状态
            self.consecutive_global_failures = 0  # 重置全局失败计数
            # 返回备用内容，避免无限递归
            return self._generate_fallback_content(prompt, "API配额限制")

    def _create_fallback_response(self, prompt: str):
        """创建智能备用响应对象（全局配额感知版本）"""
        class FallbackResponse:
            def __init__(self, text):
                self.text = text
                self.candidates = [type('obj', (object,), {'content': type('obj', (object,), {'parts': [type('obj', (object,), {'text': text})]})()})]

        # 分析prompt内容，生成相关的备用内容
        prompt_lower = prompt.lower()

        # 检查是否是全局配额耗尽
        if self.global_quota_exhausted:
            quota_notice = """

⚠️ **API配额状态提醒**
所有API密钥已达到配额限制。建议：
1. 等待配额重置（通常每小时重置）
2. 检查API计费设置和配额限制
3. 考虑升级API套餐获得更高配额
4. 联系技术支持获取帮助

"""
        else:
            quota_notice = "\n\n*注：由于临时网络问题，使用智能备用内容。*"

        # 根据prompt类型生成不同的备用内容
        if any(keyword in prompt_lower for keyword in ["框架", "结构", "大纲", "目录"]):
            fallback_text = f"""
{{
    "sections": [
        {{
            "title": "概述与背景",
            "level": 1,
            "children": [
                {{"title": "研究背景", "level": 2}},
                {{"title": "研究目的", "level": 2}},
                {{"title": "研究方法", "level": 2}}
            ]
        }},
        {{
            "title": "现状分析",
            "level": 1,
            "children": [
                {{"title": "发展现状", "level": 2}},
                {{"title": "主要特征", "level": 2}},
                {{"title": "存在问题", "level": 2}}
            ]
        }},
        {{
            "title": "深入研究",
            "level": 1,
            "children": [
                {{"title": "技术发展", "level": 2}},
                {{"title": "市场分析", "level": 2}},
                {{"title": "竞争格局", "level": 2}}
            ]
        }},
        {{
            "title": "结论与建议",
            "level": 1,
            "children": [
                {{"title": "主要结论", "level": 2}},
                {{"title": "发展建议", "level": 2}},
                {{"title": "未来展望", "level": 2}}
            ]
        }}
    ]
}}{quota_notice}
"""
        elif any(keyword in prompt_lower for keyword in ["分析", "研究", "调查", "市场", "技术"]):
            fallback_text = f"""
## 📊 专业分析报告

### 现状概述
当前发展呈现积极态势，主要表现在技术创新加速、市场需求增长、政策环境优化等方面。

### 核心发现
- **技术层面**：关键技术不断突破，应用场景持续拓展
- **市场层面**：市场规模稳步增长，竞争格局日趋明朗
- **政策层面**：支持政策密集出台，发展环境持续改善

### 发展趋势
预计未来将呈现以下发展特点：
1. 技术创新将成为核心驱动力
2. 市场集中度将进一步提升
3. 国际合作将更加深入
4. 可持续发展将成为主流

### 战略建议
- 加强技术研发投入，提升核心竞争力
- 优化市场布局，抢占发展先机
- 深化产业合作，实现协同发展{quota_notice}
"""
        else:
            fallback_text = f"""
## 📄 内容概要

### 主要内容
本部分将提供与主题相关的核心信息和深入分析。

### 关键要点
- 全面的背景介绍和现状分析
- 深入的问题研究和解决方案
- 专业的见解和发展建议
- 前瞻的趋势预测和战略规划

### 分析框架
采用系统性分析方法，从多个维度进行深入研究，确保分析的全面性和准确性。

### 预期成果
将为决策制定提供有力支撑，为实践应用提供指导方向。{quota_notice}
"""

        return FallbackResponse(fallback_text)

    def _sync_api_call(self, api_key: str, model_name: str, prompt: str):
        """同步API调用（在线程池中执行）"""
        genai.configure(api_key=api_key)

        # 使用配置的模型参数
        generation_config = self.model_config.create_generation_config(model_name)

        model = genai.GenerativeModel(model_name, generation_config=generation_config)
        response = model.generate_content([prompt])

        return response

    # 保持同步接口的兼容性
    def generate_content_with_model(self, prompt: str, model_name: str) -> Tuple[Any, int]:
        """同步接口（为了兼容现有代码）"""
        try:
            # 检查是否已经在事件循环中
            loop = asyncio.get_running_loop()
            # 如果已经在事件循环中，使用线程池执行
            import concurrent.futures

            def run_in_thread():
                new_loop = asyncio.new_event_loop()
                asyncio.set_event_loop(new_loop)
                try:
                    return new_loop.run_until_complete(
                        self.generate_content_with_model_async(prompt, model_name)
                    )
                finally:
                    new_loop.close()

            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(run_in_thread)
                return future.result()

        except RuntimeError:
            # 没有运行中的事件循环，可以创建新的
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                return loop.run_until_complete(self.generate_content_with_model_async(prompt, model_name))
            finally:
                loop.close()

    def record_successful_processing(self, key_index: int):
        """记录成功处理（保持兼容性）"""
        # 异步版本中已经在API调用时记录了
        pass

