#!/usr/bin/env python3
"""
Tokens计算器演示
展示智能文件读取和tokens管理功能
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from complete_report_generator import CompleteReportGenerator

def create_demo_files():
    """创建演示文件"""
    print("🔧 创建演示文件...")
    
    demo_dir = Path("demo_token_files")
    demo_dir.mkdir(exist_ok=True)
    
    # 创建不同大小的文件来演示tokens管理
    files_info = []
    
    # 1. 小文件 (约1000 tokens)
    small_file = demo_dir / "small_report.md"
    with open(small_file, 'w', encoding='utf-8') as f:
        content = """# 小型报告
        
## 概述
这是一个小型报告，用于演示tokens计算功能。

## 内容
包含基本的文本内容，tokens数量较少。

## 结论
小文件可以完整读取。
""" * 20  # 重复20次，约1000 tokens
        f.write(content)
    files_info.append(("small_report.md", "小文件", "约1,000 tokens"))
    
    # 2. 中等文件 (约50000 tokens)  
    medium_file = demo_dir / "medium_analysis.txt"
    with open(medium_file, 'w', encoding='utf-8') as f:
        content = """这是一个中等大小的分析报告。
包含详细的数据分析和市场研究内容。
用于测试tokens计算器在中等文件上的表现。
""" * 2000  # 重复2000次，约50000 tokens
        f.write(content)
    files_info.append(("medium_analysis.txt", "中等文件", "约50,000 tokens"))
    
    # 3. 大文件 (约200000 tokens)
    large_file = demo_dir / "large_dataset.csv"
    with open(large_file, 'w', encoding='utf-8') as f:
        f.write("年份,市场规模,增长率,主要厂商\n")
        for i in range(20000):  # 20000行数据，约200000 tokens
            f.write(f"202{i%5},{1000+i*10},{5+i%10}%,厂商{i%100}\n")
    files_info.append(("large_dataset.csv", "大文件", "约200,000 tokens"))
    
    # 4. 超大文件 (约400000 tokens)
    xlarge_file = demo_dir / "xlarge_research.txt"
    with open(xlarge_file, 'w', encoding='utf-8') as f:
        content = """这是一个超大的研究报告文件。
包含大量的研究数据、分析结果和详细说明。
用于测试tokens计算器在超大文件上的处理能力。
这种文件通常会超过API的tokens限制，需要智能处理。
""" * 5000  # 重复5000次，约400000 tokens
        f.write(content)
    files_info.append(("xlarge_research.txt", "超大文件", "约400,000 tokens"))
    
    # 5. JSON配置文件 (小文件)
    json_file = demo_dir / "config.json"
    with open(json_file, 'w', encoding='utf-8') as f:
        f.write('{"project": "AI报告生成", "version": "1.0", "description": "配置文件"}')
    files_info.append(("config.json", "配置文件", "约50 tokens"))
    
    print(f"✅ 创建了 {len(files_info)} 个演示文件:")
    for filename, desc, tokens in files_info:
        print(f"   📄 {filename}: {desc} ({tokens})")
    
    return demo_dir, [str(demo_dir / info[0]) for info in files_info]

def demo_token_calculation():
    """演示tokens计算功能"""
    print("\n🧮 演示tokens计算功能")
    print("=" * 60)
    
    try:
        demo_dir, file_paths = create_demo_files()
        generator = CompleteReportGenerator(use_async=False)
        token_manager = generator.token_manager
        
        print(f"\n📊 单个文件tokens估算:")
        total_estimated = 0
        
        for file_path in file_paths:
            file_info = token_manager.estimate_file_tokens(file_path, include_images=True)
            file_name = Path(file_path).name
            tokens = file_info.get("estimated_tokens", 0)
            file_type = file_info.get("file_type", "unknown")
            file_size = file_info.get("file_size", 0)
            
            total_estimated += tokens
            print(f"   📄 {file_name}")
            print(f"      Tokens: {tokens:,}")
            print(f"      类型: {file_type}")
            print(f"      大小: {file_size:,} 字节")
            print()
        
        print(f"📊 总计估算tokens: {total_estimated:,}")
        print(f"📊 Token限制: {token_manager.max_tokens:,}")
        print(f"📊 是否超过限制: {'是' if total_estimated > token_manager.max_tokens else '否'}")
        
        return demo_dir, file_paths
        
    except Exception as e:
        print(f"❌ tokens计算演示失败: {str(e)}")
        return None, []

def demo_reading_strategies():
    """演示不同的读取策略"""
    print("\n🎯 演示读取策略")
    print("=" * 60)
    
    try:
        demo_dir, file_paths = demo_token_calculation()
        if not demo_dir:
            return False
        
        generator = CompleteReportGenerator(use_async=False)
        token_manager = generator.token_manager
        
        # 策略1：包含图片的完整读取
        print(f"\n📋 策略1：完整读取（包含图片）")
        strategy1 = token_manager.get_optimal_reading_strategy(file_paths)
        print(f"   策略类型: {strategy1['strategy']}")
        print(f"   包含图片: {'是' if strategy1['include_images'] else '否'}")
        print(f"   预计tokens: {strategy1['estimated_tokens']:,}")
        print(f"   读取文件数: {len(strategy1['files_to_read'])}")
        
        # 策略2：只读取文本
        print(f"\n📋 策略2：只读取文本内容")
        # 模拟超过限制的情况
        original_max = token_manager.max_tokens
        token_manager.max_tokens = 100000  # 临时降低限制
        
        strategy2 = token_manager.get_optimal_reading_strategy(file_paths)
        print(f"   策略类型: {strategy2['strategy']}")
        print(f"   包含图片: {'是' if strategy2['include_images'] else '否'}")
        print(f"   预计tokens: {strategy2['estimated_tokens']:,}")
        print(f"   读取文件数: {len(strategy2['files_to_read'])}")
        
        if strategy2.get('excluded_files'):
            print(f"   排除文件数: {len(strategy2['excluded_files'])}")
            for excluded in strategy2['excluded_files'][:3]:
                print(f"     - {Path(excluded).name}")
        
        # 恢复原始限制
        token_manager.max_tokens = original_max
        
        # 策略3：选择性读取
        print(f"\n📋 策略3：选择性读取重要文件")
        token_manager.max_tokens = 50000  # 更严格的限制
        
        strategy3 = token_manager.get_optimal_reading_strategy(file_paths)
        print(f"   策略类型: {strategy3['strategy']}")
        print(f"   包含图片: {'是' if strategy3['include_images'] else '否'}")
        print(f"   预计tokens: {strategy3['estimated_tokens']:,}")
        print(f"   读取文件数: {len(strategy3['files_to_read'])}")
        
        if strategy3.get('excluded_files'):
            print(f"   排除文件数: {len(strategy3['excluded_files'])}")
        
        # 恢复原始限制
        token_manager.max_tokens = original_max
        
        return True
        
    except Exception as e:
        print(f"❌ 读取策略演示失败: {str(e)}")
        return False
    finally:
        # 清理演示文件
        try:
            import shutil
            if demo_dir and demo_dir.exists():
                shutil.rmtree(demo_dir)
                print(f"\n🗑️ 清理演示目录: {demo_dir}")
        except:
            pass

def demo_actual_reading():
    """演示实际文件读取"""
    print("\n📖 演示实际文件读取")
    print("=" * 60)
    
    try:
        demo_dir, file_paths = create_demo_files()
        generator = CompleteReportGenerator(use_async=False)
        
        print(f"\n🔍 实际读取演示目录: {demo_dir}")
        
        # 读取所有文件
        data_files, content, image_files = generator._read_data_source_detailed(str(demo_dir))
        
        actual_tokens = generator.token_manager.estimate_tokens(content)
        
        print(f"\n📊 读取结果:")
        print(f"   数据文件: {len(data_files)} 个")
        print(f"   图片文件: {len(image_files)} 个")
        print(f"   内容长度: {len(content):,} 字符")
        print(f"   实际tokens: {actual_tokens:,}")
        print(f"   是否超过限制: {'是' if actual_tokens > generator.token_manager.max_tokens else '否'}")
        
        # 显示内容预览
        print(f"\n📄 内容预览 (前500字符):")
        print(content[:500] + "..." if len(content) > 500 else content)
        
        return True
        
    except Exception as e:
        print(f"❌ 实际读取演示失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 清理演示文件
        try:
            import shutil
            if demo_dir and demo_dir.exists():
                shutil.rmtree(demo_dir)
                print(f"\n🗑️ 清理演示目录: {demo_dir}")
        except:
            pass

if __name__ == "__main__":
    print("🚀 Tokens计算器功能演示")
    print("=" * 80)
    
    print("\n📋 功能说明:")
    print("1. 智能tokens估算：精确计算文本和文件的tokens数量")
    print("2. 读取策略制定：根据tokens限制自动选择最优策略")
    print("3. 内容智能截断：超过限制时保留最重要的内容")
    print("4. 图片处理控制：可选择性包含或排除图片分析")
    
    # 演示tokens计算
    demo_token_calculation()
    
    # 演示读取策略
    strategies_success = demo_reading_strategies()
    
    # 演示实际读取
    reading_success = demo_actual_reading()
    
    if strategies_success and reading_success:
        print("\n🎉 Tokens计算器演示完成！")
        print("✅ 所有功能正常工作，已解决API tokens限制问题")
    else:
        print("\n⚠️ 部分演示功能存在问题")
    
    print("\n📋 使用说明:")
    print("1. 系统会自动检测文件总tokens是否超过250,000限制")
    print("2. 超过限制时优先排除图片分析，只读取文本内容")
    print("3. 如果文本内容仍超过限制，会选择性读取重要文件")
    print("4. 提供详细的tokens使用报告和读取策略说明")
    print("5. 支持内容智能截断，确保不超过API限制")
