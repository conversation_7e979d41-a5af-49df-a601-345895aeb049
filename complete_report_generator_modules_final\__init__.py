"""
完整代码重构模块 - 主入口文件
提供与原文件相同的接口，确保向后兼容性
"""

# 导入所有主要类和函数
from .config import (
    API_KEYS, 
    MODEL_NAMES, 
    MAX_CONSECUTIVE_CLEANUP_COUNT,
    GeminiModelConfig,
    ImageMatcher
)

from .token_manager import TokenManager

from .api_manager import (
    GeminiAPILimits,
    AsyncConfig,
    BaseGeminiAPIManager,
    GeminiAPIManager
)

from .async_api_manager import AsyncGeminiAPIManager

from .chart_generator import ChartGenerator

from .search_manager import (
    SearchTrigger,
    SearchManager,
    SearchToolManager,
    ContentValidator
)

from .content_generator import (
    StructuredContentGenerator,
    RobustContentCleaner
)

from .report_generator import CompleteReportGenerator

from .utils import (
    create_directories,
    create_framework_file,
    create_sample_data,
    get_user_inputs,
    create_topic_file_template,
    create_custom_framework_template,
    validate_data_sources,
    main,
    manage_checkpoints
)

# 版本信息
__version__ = "1.0.0"
__author__ = "AI Report Generator Team"
__description__ = "完整的AI报告生成器 - 模块化重构版本"

# 导出所有公共接口
__all__ = [
    # 配置相关
    'API_KEYS',
    'MODEL_NAMES', 
    'MAX_CONSECUTIVE_CLEANUP_COUNT',
    'GeminiModelConfig',
    'ImageMatcher',
    
    # Token管理
    'TokenManager',
    
    # API管理
    'GeminiAPILimits',
    'AsyncConfig',
    'BaseGeminiAPIManager',
    'GeminiAPIManager',
    'AsyncGeminiAPIManager',
    
    # 图表生成
    'ChartGenerator',
    
    # 搜索管理
    'SearchTrigger',
    'SearchManager', 
    'SearchToolManager',
    'ContentValidator',
    
    # 内容生成
    'StructuredContentGenerator',
    'RobustContentCleaner',
    
    # 报告生成
    'CompleteReportGenerator',
    
    # 工具函数
    'create_directories',
    'create_framework_file',
    'create_sample_data',
    'get_user_inputs',
    'create_topic_file_template',
    'create_custom_framework_template',
    'validate_data_sources',
    'main',
    'manage_checkpoints'
]