#!/usr/bin/env python3
"""
测试模型分工修复
验证统筹任务使用gemini-2.5-pro，执行任务使用gemini-2.5-flash
"""

import sys
import os
from pathlib import Path
from unittest.mock import patch, MagicMock

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_model_assignment_logic():
    """测试模型分工逻辑"""
    print("🧪 测试模型分工逻辑")
    print("=" * 50)
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        # 创建报告生成器
        generator = CompleteReportGenerator(use_async=False)
        
        print(f"📋 模型配置:")
        print(f"   统筹模型: {generator.ORCHESTRATOR_MODEL}")
        print(f"   执行模型: {generator.EXECUTOR_MODEL}")
        
        # 测试模型分工逻辑
        print(f"\n🔧 测试模型分工:")
        
        # 1. 测试允许降级的情况
        task_id_1 = "test_task_1"
        model_1 = generator.model_fallback_manager.get_model_for_task(task_id_1, generator.ORCHESTRATOR_MODEL)
        print(f"   任务1 (允许降级): {model_1}")
        
        # 2. 模拟失败5次，触发降级
        for i in range(5):
            should_fallback = generator.model_fallback_manager.record_pro_failure(task_id_1)
            print(f"   任务1 失败{i+1}次，是否降级: {should_fallback}")
        
        # 3. 再次获取模型，应该是flash
        model_1_after = generator.model_fallback_manager.get_model_for_task(task_id_1, generator.ORCHESTRATOR_MODEL)
        print(f"   任务1 (降级后): {model_1_after}")
        
        # 4. 测试不允许降级的情况（新的逻辑）
        print(f"\n🔒 测试强制使用统筹模型:")
        
        # 创建一个新的任务ID，也触发降级
        task_id_2 = "test_task_2"
        for i in range(5):
            generator.model_fallback_manager.record_pro_failure(task_id_2)
        
        # 检查是否被标记为需要降级
        should_use_fallback = generator.model_fallback_manager.should_use_fallback(task_id_2)
        print(f"   任务2 是否被标记为降级: {should_use_fallback}")
        
        # 但是如果我们强制使用统筹模型，应该仍然返回pro
        forced_model = generator.ORCHESTRATOR_MODEL  # 新逻辑中的强制模型
        print(f"   任务2 (强制统筹模型): {forced_model}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型分工逻辑测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_audit_method_model_usage():
    """测试审核方法的模型使用"""
    print("\n🧪 测试审核方法的模型使用")
    print("=" * 50)
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        # 创建报告生成器
        generator = CompleteReportGenerator(use_async=False)
        
        # 模拟API管理器
        mock_api_manager = MagicMock()
        mock_response = MagicMock()
        mock_response.text = '{"overall_score": 8.0, "needs_optimization": false, "issues": []}'
        
        # 设置模拟返回值
        mock_api_manager.generate_content_with_model.return_value = (mock_response, 0)
        mock_api_manager.record_successful_processing.return_value = None
        
        # 替换API管理器
        original_api_manager = generator.api_manager
        generator.api_manager = mock_api_manager
        
        try:
            # 创建测试章节
            test_section = {
                "title": "市场概览与现状分析",
                "content": "这是一个测试章节的内容。"
            }
            
            # 创建测试数据源
            test_data_source = "test_data"
            
            print(f"📋 测试同步审核方法:")
            
            # 调用审核方法
            audit_result = generator._audit_section_with_orchestrator(
                test_section, test_data_source, 1
            )
            
            print(f"   审核结果: {audit_result}")
            
            # 检查API调用
            if mock_api_manager.generate_content_with_model.called:
                call_args = mock_api_manager.generate_content_with_model.call_args
                used_model = call_args[0][1]  # 第二个参数是模型名
                print(f"   实际使用的模型: {used_model}")
                
                if used_model == generator.ORCHESTRATOR_MODEL:
                    print(f"   ✅ 正确使用统筹模型")
                else:
                    print(f"   ❌ 错误使用了: {used_model}")
                    return False
            else:
                print(f"   ❌ API未被调用")
                return False
            
            return True
            
        finally:
            # 恢复原始API管理器
            generator.api_manager = original_api_manager
        
    except Exception as e:
        print(f"❌ 审核方法模型使用测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_content_generation_model_usage():
    """测试内容生成的模型使用"""
    print("\n🧪 测试内容生成的模型使用")
    print("=" * 50)
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        # 创建报告生成器
        generator = CompleteReportGenerator(use_async=False)
        
        # 模拟API管理器
        mock_api_manager = MagicMock()
        mock_response = MagicMock()
        mock_response.text = "这是生成的内容。"
        
        # 设置模拟返回值
        mock_api_manager.generate_content_with_model.return_value = (mock_response, 0)
        mock_api_manager.record_successful_processing.return_value = None
        
        # 替换API管理器
        original_api_manager = generator.api_manager
        generator.api_manager = mock_api_manager
        
        try:
            print(f"📋 测试执行模型调用:")
            
            # 调用执行模型
            test_prompt = "请生成一段关于AI发展的内容。"
            content = generator.call_executor_model(test_prompt)
            
            print(f"   生成内容: {content}")
            
            # 检查API调用
            if mock_api_manager.generate_content_with_model.called:
                call_args = mock_api_manager.generate_content_with_model.call_args
                used_model = call_args[0][1]  # 第二个参数是模型名
                print(f"   实际使用的模型: {used_model}")
                
                if used_model == generator.EXECUTOR_MODEL:
                    print(f"   ✅ 正确使用执行模型")
                else:
                    print(f"   ❌ 错误使用了: {used_model}")
                    return False
            else:
                print(f"   ❌ API未被调用")
                return False
            
            return True
            
        finally:
            # 恢复原始API管理器
            generator.api_manager = original_api_manager
        
    except Exception as e:
        print(f"❌ 内容生成模型使用测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_model_assignment_summary():
    """测试模型分工总结"""
    print("\n📊 模型分工总结")
    print("=" * 50)
    
    print("🎯 修复后的模型分工:")
    print("   📋 统筹任务 (gemini-2.5-pro):")
    print("      - 审核章节内容")
    print("      - 优化章节质量")
    print("      - 生成报告框架")
    print("      - 分析整体结构")
    print("      - 压缩章节内容")
    print("      - 学习参考报告")
    print("   ")
    print("   ⚡ 执行任务 (gemini-2.5-flash):")
    print("      - 根据指导生成内容")
    print("      - 读取文档并生成章节")
    print("      - 基于数据源创建内容")
    print("   ")
    print("   🔄 降级机制:")
    print("      - 统筹任务: 不允许降级，始终使用gemini-2.5-pro")
    print("      - 执行任务: 默认使用gemini-2.5-flash")
    print("      - 特殊情况: 只有在pro模型完全不可用时才考虑降级")

if __name__ == "__main__":
    print("🚀 模型分工修复测试")
    print("=" * 80)
    
    print("\n📋 测试内容:")
    print("1. 模型分工逻辑测试")
    print("2. 审核方法模型使用测试")
    print("3. 内容生成模型使用测试")
    print("4. 模型分工总结")
    
    # 测试1：模型分工逻辑
    test1_success = test_model_assignment_logic()
    
    # 测试2：审核方法模型使用
    test2_success = test_audit_method_model_usage()
    
    # 测试3：内容生成模型使用
    test3_success = test_content_generation_model_usage()
    
    # 测试4：模型分工总结
    test_model_assignment_summary()
    
    # 总结
    all_tests_passed = all([test1_success, test2_success, test3_success])
    
    print(f"\n📊 测试结果总结:")
    print(f"   模型分工逻辑: {'✅ 通过' if test1_success else '❌ 失败'}")
    print(f"   审核方法模型使用: {'✅ 通过' if test2_success else '❌ 失败'}")
    print(f"   内容生成模型使用: {'✅ 通过' if test3_success else '❌ 失败'}")
    
    if all_tests_passed:
        print("\n🎉 所有测试通过！")
        print("✅ 模型分工BUG已修复")
        print("✅ 统筹任务强制使用gemini-2.5-pro")
        print("✅ 执行任务使用gemini-2.5-flash")
    else:
        print("\n⚠️ 部分测试失败")
    
    print("\n📋 修复说明:")
    print("1. ✅ 添加了allow_fallback参数控制是否允许降级")
    print("2. ✅ 审核任务强制使用统筹模型，不允许降级")
    print("3. ✅ 优化任务强制使用统筹模型，不允许降级")
    print("4. ✅ 框架生成任务强制使用统筹模型，不允许降级")
    print("5. ✅ 内容生成任务使用执行模型")
    print("6. ✅ 保持了原有的降级机制作为备用")
    
    print("\n🎯 修复效果:")
    print("修复前：统筹任务被错误降级到gemini-2.5-flash")
    print("修复后：统筹任务强制使用gemini-2.5-pro，执行任务使用gemini-2.5-flash")
