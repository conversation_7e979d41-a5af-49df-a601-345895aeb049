"""
API管理器模块 - 包含所有API相关的管理类
"""
import os
import sys
import time
import json
import threading
import asyncio
import math
import re
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple
from tqdm import tqdm

import google.generativeai as genai

# 特定异常类型导入
from json import JSONDecodeError
import socket
from urllib.error import URLError, HTTPError

from .config import API_KEYS, MODEL_NAMES, MAX_CONSECUTIVE_CLEANUP_COUNT, GeminiModelConfig

# 精确的API限制配置
class GeminiAPILimits:
    """Gemini API精确限制配置"""

    # 根据用户提供的实际限制
    LIMITS = {
        "gemini-2.5-pro": {
            "rpm": 5,      # 每分钟5次请求
            "tpm": 250000, # 每分钟250K tokens
            "rpd": 1000     # 每天100次请求
        },
        "gemini-2.5-flash": {
            "rpm": 10,     # 每分钟10次请求
            "tpm": 250000, # 每分钟250K tokens
            "rpd": 2500     # 每天250次请求
        }
    }

    @staticmethod
    def get_model_limits(model_name: str) -> dict:
        """获取指定模型的限制"""
        if "pro" in model_name.lower():
            return GeminiAPILimits.LIMITS["gemini-2.5-pro"]
        else:
            return GeminiAPILimits.LIMITS["gemini-2.5-flash"]

    @staticmethod
    def calculate_safe_rpm(model_name: str, api_count: int) -> int:
        """计算安全的每分钟请求数"""
        limits = GeminiAPILimits.get_model_limits(model_name)
        base_rpm = limits["rpm"]

        # 保守策略：使用80%的限制，并在多个API间分配
        safe_rpm = int(base_rpm * 0.9)
        per_api_rpm = max(1, safe_rpm // api_count) if api_count > 0 else 1

        return per_api_rpm

# 动态配置参数
class AsyncConfig:
    """异步配置参数（基于实际API限制）"""

    @staticmethod
    def get_available_api_count():
        """获取可用的API密钥数量"""
        valid_keys = [key for key in API_KEYS if key and "YOUR_GEMINI_API_KEY" not in key and len(key) > 10]
        return len(valid_keys)

    @staticmethod
    def get_max_concurrent_requests():
        """获取最大并发请求数（等于API密钥数量）"""
        api_count = AsyncConfig.get_available_api_count()
        # 使用所有可用的API密钥进行并发
        return api_count

    @staticmethod
    def get_dynamic_concurrent_requests(api_manager=None):
        """获取动态并发请求数（强制使用最大API数量）"""
        if api_manager is None:
            return AsyncConfig.get_max_concurrent_requests()

        # 强制使用最大API数量，不受当前状态影响
        max_count = AsyncConfig.get_available_api_count()

        # 如果API管理器存在，重置所有API状态以确保最大并发
        if hasattr(api_manager, '_force_reset_all_apis_for_max_concurrency'):
            api_manager._force_reset_all_apis_for_max_concurrency()

        # 始终返回最大可用数量
        return max(1, max_count)

    @staticmethod
    def calculate_optimal_batch_size(total_tasks: int):
        """计算最优批次大小（基于API密钥数量）"""
        api_count = AsyncConfig.get_available_api_count()
        # 使用API密钥数量作为批次大小，但不超过总任务数
        return min(api_count, total_tasks)

    @staticmethod
    def estimate_total_api_calls():
        """估算总API调用次数"""
        # 基础调用：1次框架生成 + 8次内容生成
        base_calls = 1 + 8

        # 3轮迭代优化：每轮18次（8次章节审核 + 8次章节优化 + 1次整体审核 + 1次整体优化）
        iteration_calls = 3 * 18

        total_calls = base_calls + iteration_calls
        return total_calls

    @staticmethod
    def get_performance_info():
        """获取性能信息"""
        api_count = AsyncConfig.get_available_api_count()
        max_concurrent = AsyncConfig.get_max_concurrent_requests()
        total_calls = AsyncConfig.estimate_total_api_calls()

        # 基于实际RPM限制估算时间
        pro_rpm = GeminiAPILimits.LIMITS["gemini-2.5-pro"]["rpm"]
        flash_rpm = GeminiAPILimits.LIMITS["gemini-2.5-flash"]["rpm"]

        return {
            "available_api_keys": api_count,
            "max_concurrent_requests": max_concurrent,
            "estimated_total_api_calls": total_calls,
            "pro_rpm_limit": pro_rpm,
            "flash_rpm_limit": flash_rpm,
            "estimated_speedup": f"{min(api_count, 10)}x（智能并发）"
        }


class BaseGeminiAPIManager:
    """Gemini API管理器基类 - 包含通用方法"""

    def _extract_task_purpose(self, prompt: str, context: dict = None) -> str:
        """从prompt中提取详细的任务目的"""
        prompt_lower = prompt.lower()
        
        # 使用context信息增强任务目的提取
        if context:
            # 如果有上下文信息，可以更精确地判断任务类型
            task_type = context.get('task_type', '')
            section_info = context.get('section_info', {})
            
            if task_type == 'framework_generation':
                return "🎯 统筹模型生成报告框架结构"
            elif task_type == 'content_generation' and section_info:
                section_title = section_info.get('title', '未知章节')
                section_level = section_info.get('level', '未知级别')
                return f"⚡ 执行模型生成第{section_level}级节点: {section_title}"
            elif task_type == 'review' and section_info:
                section_title = section_info.get('title', '未知章节')
                return f"🔍 统筹模型审核章节: {section_title}"
            elif task_type == 'optimization' and section_info:
                section_title = section_info.get('title', '未知章节')
                return f"✨ 统筹模型优化章节: {section_title}"

        if "框架" in prompt and "json" in prompt_lower:
            return "🎯 统筹模型生成报告框架结构"
        elif "审核" in prompt and "章节" in prompt:
            # 提取章节标题
            try:
                title = self._extract_title_from_prompt(prompt)
            except:
                title = "未知章节"
            return f"🔍 统筹模型审核章节: {title}"
        elif "优化" in prompt and "章节" in prompt:
            try:
                title = self._extract_title_from_prompt(prompt)
            except:
                title = "未知章节"
            return f"✨ 统筹模型优化章节: {title}"
        elif "审核" in prompt and "整体" in prompt:
            return "📄 统筹模型审核整体文档质量"
        elif "优化" in prompt and "整体" in prompt:
            return "🔧 统筹模型优化整体文档结构"
        elif "ocr" in prompt_lower or "图片" in prompt or "pdf" in prompt_lower:
            return "🖼️ Gemini OCR处理PDF图片内容"
        elif "生成" in prompt and ("内容" in prompt or "详细" in prompt):
            # 提取节点标题和级别
            try:
                title = self._extract_title_from_prompt(prompt)
                level = self._extract_level_from_prompt(prompt)
                if title == "未知标题":
                    title = "未知节点"
            except:
                title = "未知节点"
                level = "未知级别"
            return f"⚡ 执行模型生成第{level}级节点: {title}"
        elif "第" in prompt and "级" in prompt:
            level_match = prompt.find("第") + 1
            if level_match < len(prompt):
                level_char = prompt[level_match]
                try:
                    title = self._extract_title_from_prompt(prompt)
                except:
                    title = "未知标题"
                return f"📝 执行模型生成第{level_char}级标题: {title}"
        else:
            return "🤖 AI模型执行任务"

    def _extract_title_from_prompt(self, prompt: str) -> str:
        """从prompt中提取标题"""
        # 查找引号中的标题
        import re

        # 匹配 "标题" 或 「标题」
        title_patterns = [
            r'"([^"]+)"',
            r'「([^」]+)」',
            r'《([^》]+)》',
            r'【([^】]+)】'
        ]

        for pattern in title_patterns:
            match = re.search(pattern, prompt)
            if match:
                return match.group(1)

        # 如果没有找到引号，尝试查找"章节标题："后的内容
        if "章节标题：" in prompt:
            start = prompt.find("章节标题：") + 5
            end = prompt.find("\n", start)
            if end == -1:
                end = start + 50
            return prompt[start:end].strip()

        return "未知标题"

    def _extract_level_from_prompt(self, prompt: str) -> str:
        """从prompt中提取级别"""
        import re

        # 查找"第X级"
        level_match = re.search(r'第(\d+)级', prompt)
        if level_match:
            return level_match.group(1)

        return "未知级别"


class GeminiAPIManager(BaseGeminiAPIManager):
    """API轮换管理器 - 支持参数配置"""

    def __init__(self, api_keys: List[str], model_names: List[str], model_config: GeminiModelConfig = None):
        self.api_configs = []

        for i, key in enumerate(api_keys):
            if key and "YOUR_GEMINI_API_KEY" not in key and len(key) > 10:
                self.api_configs.append({
                    "name": f"API Key {i+1}",
                    "key": key,
                    "models": model_names,
                    "current_model_index": 0
                })

        if not self.api_configs:
            raise ValueError("FATAL: No valid Google API keys are configured.")

        self.total_keys = len(self.api_configs)
        self.current_api_index = 0
        self.lock = threading.Lock()
        self.max_rotations = 10000  # 大幅增加轮换限制
        self.total_rotations_completed = 0
        self.usage_counts = {i: 0 for i in range(self.total_keys)}
        self.consecutive_cleanup_counts = {i: 0 for i in range(self.total_keys)}
        self.api_call_counts = {i: 0 for i in range(self.total_keys)}

        # 模型参数配置
        self.model_config = model_config or GeminiModelConfig()

        print(f"Gemini API Manager initialized with {self.total_keys} active keys.")
        print(f"Each key will rotate through {len(model_names)} models: {model_names}")

        if self.total_keys > 0:
            self._log_current_key_status()
    
    def _log_current_key_status(self):
        """记录当前密钥状态"""
        print(f"\n--- 所有API密钥状态 (已完成轮换: {self.total_rotations_completed}/{self.max_rotations}) ---")
        for i in range(self.total_keys):
            config = self.api_configs[i]
            status = "🔴 当前" if i == self.current_api_index else "⚪️ 待用"
            current_model = config['models'][config['current_model_index']]
            print(f" {status} {config['name']}: 模型='{current_model}', "
                  f"成功处理={self.usage_counts[i]}, "
                  f"API总调用={self.api_call_counts[i]}")
        print("-----------------------------------------------------------\n")
    
    def _get_current_config(self) -> Dict[str, Any]:
        """获取当前配置"""
        with self.lock:
            api_config = self.api_configs[self.current_api_index]
            model_name = api_config["models"][api_config["current_model_index"]]
            return {
                "api_index": self.current_api_index,
                "api_name": api_config["name"],
                "api_key": api_config["key"],
                "model_name": model_name
            }
    
    def _switch_to_next(self, reason: str = "未知原因"):
        """切换到下一个API密钥或模型"""
        print(f"!!!!!! 切换事件触发 !!!!!!")
        print(f"         切换原因: {reason}")
        
        current_api_config = self.api_configs[self.current_api_index]
        
        if current_api_config["current_model_index"] + 1 < len(current_api_config["models"]):
            current_api_config["current_model_index"] += 1
            print(f"         内部切换: {current_api_config['name']} -> 新模型: '{current_api_config['models'][current_api_config['current_model_index']]}'")
        else:
            print(f"         {current_api_config['name']} 的所有模型已尝试，切换到下一个API Key...")
            self.consecutive_cleanup_counts[self.current_api_index] = 0
            current_api_config["current_model_index"] = 0
            
            if self.current_api_index == self.total_keys - 1:
                self.total_rotations_completed += 1
                print(f"$$$ 完成一轮完整的API密钥轮换。总完成轮数: {self.total_rotations_completed}/{self.max_rotations} $$$")
            
            self.current_api_index = (self.current_api_index + 1) % self.total_keys
            print(f"         已切换到新的API密钥: {self.api_configs[self.current_api_index]['name']}")
        
        self._log_current_key_status()
    
    def record_successful_processing(self, key_index: int):
        """记录成功处理"""
        with self.lock:
            self.usage_counts[key_index] += 1
            self.consecutive_cleanup_counts[key_index] = 0
    
    def generate_content_with_model(self, prompt: str, model_name: str) -> Tuple[Any, int]:
        """使用指定模型生成内容"""
        with self.lock:
            if self.total_rotations_completed >= self.max_rotations:
                raise Exception(f"已完成 {self.max_rotations} 轮完整的API密钥轮换，程序终止。")
            
            if self.total_keys == 0:
                raise Exception("No configured API keys to use for generation.")
        
        max_attempts = self.total_keys + 1
        
        for _ in range(max_attempts):
            with self.lock:
                current_api_index = self.current_api_index
                api_config = self.api_configs[current_api_index]
                api_key = api_config["key"]
                api_name = api_config["name"]
                self.api_call_counts[current_api_index] += 1
            
            # 从prompt中提取任务信息
            try:
                task_purpose = self._extract_task_purpose(prompt)
            except Exception as e:
                task_purpose = f"任务信息提取失败: {str(e)[:50]}"
            print(f"\n[API调用] 使用: {api_name} | 模型: {model_name} | 调用数: {self.api_call_counts[current_api_index]}")
            print(f"[任务目的] {task_purpose}")

            # 显示prompt摘要（前100字符）
            prompt_summary = prompt.replace('\n', ' ')[:1000] + "..." if len(prompt) > 100 else prompt.replace('\n', ' ')
            print(f"[任务内容] {prompt_summary}")
            
            try:
                genai.configure(api_key=api_key)

                # 使用配置的模型参数
                generation_config = self.model_config.create_generation_config(model_name)

                model = genai.GenerativeModel(model_name, generation_config=generation_config)
                response = model.generate_content([prompt])

                return response, current_api_index
                
            except (ConnectionError, TimeoutError) as e:
                error_msg = str(e).lower()
                print(f"!!!!!! 网络错误 with {api_name} using model {model_name}: {error_msg} !!!!!!")
            except (ValueError, KeyError) as e:
                error_msg = str(e).lower()
                print(f"!!!!!! 参数错误 with {api_name} using model {model_name}: {error_msg} !!!!!!")
            except Exception as e:
                error_msg = str(e).lower()
                print(f"!!!!!! 未知错误 with {api_name} using model {model_name}: {error_msg} !!!!!!")
                
                if "quota" in error_msg or "limit" in error_msg or "permission" in error_msg or "resource_exhausted" in error_msg:
                    print(f"  -> 检测到配额/权限问题，切换到下一个API密钥...")
                    with self.lock:
                        self.current_api_index = (self.current_api_index + 1) % self.total_keys
                        # 移除重复的轮换计数，只在_switch_to_next中计数
                    time.sleep(1)
                    continue
                else:
                    print(f"  -> 非配额问题，切换到下一个API密钥。")
                    with self.lock:
                        self.current_api_index = (self.current_api_index + 1) % self.total_keys
                        # 移除重复的轮换计数，只在_switch_to_next中计数
                    time.sleep(1)
                    continue
        
        raise Exception(f"All API keys have failed for model {model_name}. Cannot proceed.")


# 由于AsyncGeminiAPIManager类非常大，我将在下一个文件中继续完成
# 这里先添加一个占位符，稍后会完成完整的实现
class AsyncGeminiAPIManager(BaseGeminiAPIManager):
    """异步API管理器 - 智能配额管理版本"""
    
    def __init__(self, api_keys: List[str], model_names: List[str], model_config: GeminiModelConfig = None):
        # 这个类的完整实现将在后续添加
        pass