"""
报告生成模块 - 完整的AI报告生成器
从complete_report_generator.py中提取的CompleteReportGenerator类
"""
import os
import sys
import time
import json
import asyncio
import math
import re
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple
from tqdm import tqdm

# 导入其他模块
from config import API_KEYS, MODEL_NAMES, GeminiModelConfig
from token_manager import TokenManager
from api_manager import GeminiAPIManager
from async_api_manager import AsyncGeminiAPIManager
from chart_generator import ChartGenerator
from content_generator import StructuredContentGenerator, RobustContentCleaner


class CompleteReportGenerator:
    """
    完整的AI报告生成器
    严格按照用户需求实现所有功能
    支持异步并行优化
    """

    def __init__(self, use_async: bool = True, max_tokens: int = 250000):
        self.use_async = use_async

        # 初始化模型参数配置
        self.model_config = GeminiModelConfig()

        if use_async:
            self.api_manager = AsyncGeminiAPIManager(API_KEYS, MODEL_NAMES, self.model_config)
        else:
            self.api_manager = GeminiAPIManager(API_KEYS, MODEL_NAMES, self.model_config)

        self.ORCHESTRATOR_MODEL = "gemini-2.5-pro"    # 统筹模型
        self.EXECUTOR_MODEL = "gemini-2.5-flash"      # 执行模型

        # 初始化Token管理器
        self.token_manager = TokenManager(max_tokens)

        # 报告配置（动态配置）
        self.report_config = {
            "title": "",
            "data_source": "",
            "output_dir": "output",
            "max_depth": 6,  # 最大层级深度（动态输入）
            "primary_sections": 8,  # 一级标题数量（动态输入）
            "target_words": 50000,  # 最终报告目标字数（动态输入）
            "reference_report": "",  # 参考报告路径（可选）
            "max_tokens": max_tokens,  # Token限制
            "enable_chart_generation": True,  # 启用图表生成
            "enable_image_embedding": True,  # 启用图片嵌入
            "enable_search_enhancement": True,  # 启用搜索增强
            "search_auto_confirm": True,  # 自动确认搜索（跳过用户输入）- 修复：默认启用
        }

        # 初始化checkpoint系统
        self.checkpoint_dir = Path("checkpoints")
        self.checkpoint_dir.mkdir(exist_ok=True)
        self.current_checkpoint_id = None
        self.checkpoint_data = {}

        # 初始化图表生成器
        self.chart_generator = ChartGenerator(Path(self.report_config["output_dir"]) / "charts")

        # 图片匹配器将在generate_report时初始化（需要data_sources）
        self.image_matcher = None

        # 初始化稳健的内容清理系统
        self.structured_generator = StructuredContentGenerator(self.api_manager)
        self.robust_cleaner = RobustContentCleaner()
        self.robust_cleaner.set_structured_generator(self.structured_generator)

        print(f"📋 模型配置:")
        print(f"   统筹模型: {self.ORCHESTRATOR_MODEL}")
        print(f"   执行模型: {self.EXECUTOR_MODEL}")
        print(f"   异步模式: {'启用' if use_async else '禁用'}")
        print(f"   Token限制: {max_tokens:,} tokens")
        print(f"   Checkpoint目录: {self.checkpoint_dir.absolute()}")
        print(f"📈 图表生成: {'启用' if self.report_config['enable_chart_generation'] else '禁用'}")
        print(f"🖼️ 图片嵌入: {'启用' if self.report_config['enable_image_embedding'] else '禁用'}")
        print(f"🔍 搜索增强: {'启用' if self.report_config['enable_search_enhancement'] else '禁用'}")

    # ==================== 模型参数配置接口 ====================

    def update_model_config(self, model_name: str, **kwargs):
        """更新指定模型的参数配置

        Args:
            model_name: 模型名称 ('gemini-2.5-pro' 或 'gemini-2.5-flash')
            **kwargs: 模型参数
                - temperature: 温度参数 (0.0-2.0)
                - top_p: 核采样参数 (0.0-1.0)
                - top_k: Top-K采样参数 (1-40)
                - max_output_tokens: 最大输出token数
                - candidate_count: 候选数量
                - stop_sequences: 停止序列
        """
        self.model_config.update_model_config(model_name, **kwargs)
        print(f"✅ 已更新 {model_name} 的参数配置")

    def get_model_config(self, model_name: str = None) -> dict:
        """获取模型参数配置

        Args:
            model_name: 模型名称，如果为None则返回所有模型配置

        Returns:
            dict: 模型配置信息
        """
        return self.model_config.get_model_config(model_name)

    def reset_model_config(self, model_name: str = None):
        """重置模型参数配置为默认值

        Args:
            model_name: 模型名称，如果为None则重置所有模型配置
        """
        self.model_config.reset_model_config(model_name)
        print(f"✅ 已重置 {model_name or '所有'} 模型的参数配置")

    # ==================== 报告配置管理 ====================

    def configure_report(self, **kwargs):
        """配置报告生成参数

        Args:
            title: 报告标题
            data_source: 数据源路径
            output_dir: 输出目录
            max_depth: 最大层级深度
            primary_sections: 一级标题数量
            target_words: 目标字数
            reference_report: 参考报告路径
            enable_chart_generation: 启用图表生成
            enable_image_embedding: 启用图片嵌入
            enable_search_enhancement: 启用搜索增强
            search_auto_confirm: 自动确认搜索
        """
        for key, value in kwargs.items():
            if key in self.report_config:
                self.report_config[key] = value
                print(f"✅ 已设置 {key}: {value}")
            else:
                print(f"⚠️ 未知配置项: {key}")

    def get_report_config(self) -> dict:
        """获取当前报告配置"""
        return self.report_config.copy()

    # ==================== Checkpoint管理 ====================

    def create_checkpoint(self, checkpoint_name: str, data: dict):
        """创建checkpoint"""
        checkpoint_id = f"{checkpoint_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        checkpoint_file = self.checkpoint_dir / f"{checkpoint_id}.json"

        checkpoint_data = {
            "id": checkpoint_id,
            "name": checkpoint_name,
            "timestamp": datetime.now().isoformat(),
            "config": self.report_config.copy(),
            "data": data
        }

        try:
            with open(checkpoint_file, 'w', encoding='utf-8') as f:
                json.dump(checkpoint_data, f, ensure_ascii=False, indent=2)

            self.current_checkpoint_id = checkpoint_id
            self.checkpoint_data = checkpoint_data
            print(f"✅ Checkpoint已保存: {checkpoint_id}")
            return checkpoint_id

        except Exception as e:
            print(f"❌ Checkpoint保存失败: {str(e)}")
            return None

    def load_checkpoint(self, checkpoint_id: str) -> dict:
        """加载checkpoint"""
        checkpoint_file = self.checkpoint_dir / f"{checkpoint_id}.json"

        if not checkpoint_file.exists():
            print(f"❌ Checkpoint不存在: {checkpoint_id}")
            return {}

        try:
            with open(checkpoint_file, 'r', encoding='utf-8') as f:
                checkpoint_data = json.load(f)

            self.current_checkpoint_id = checkpoint_id
            self.checkpoint_data = checkpoint_data
            self.report_config.update(checkpoint_data.get("config", {}))

            print(f"✅ Checkpoint已加载: {checkpoint_id}")
            return checkpoint_data.get("data", {})

        except Exception as e:
            print(f"❌ Checkpoint加载失败: {str(e)}")
            return {}

    def list_checkpoints(self) -> List[dict]:
        """列出所有checkpoint"""
        checkpoints = []

        for checkpoint_file in self.checkpoint_dir.glob("*.json"):
            try:
                with open(checkpoint_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    checkpoints.append({
                        "id": data.get("id", ""),
                        "name": data.get("name", ""),
                        "timestamp": data.get("timestamp", ""),
                        "file": str(checkpoint_file)
                    })
            except Exception as e:
                print(f"⚠️ 读取checkpoint失败 {checkpoint_file}: {str(e)}")

        return sorted(checkpoints, key=lambda x: x["timestamp"], reverse=True)

    # ==================== 主要生成方法 ====================

    def generate_report(self, topic: str, data_sources: List[str], framework_file: str = None) -> str:
        """生成完整报告的主入口方法

        Args:
            topic: 报告主题
            data_sources: 数据源列表
            framework_file: 框架文件路径（可选）

        Returns:
            str: 生成的报告文件路径
        """
        print(f"\n🚀 开始生成报告: {topic}")
        print(f"📊 数据源: {len(data_sources)} 个")
        print(f"📋 框架文件: {framework_file or '使用默认框架'}")

        # 更新报告配置
        self.report_config["title"] = topic
        self.report_config["data_source"] = str(data_sources)

        # 创建输出目录
        output_dir = Path(self.report_config["output_dir"])
        output_dir.mkdir(exist_ok=True)

        # 初始化图片匹配器（现在有了data_sources）
        if self.report_config["enable_image_embedding"]:
            from config import ImageMatcher  # 延迟导入避免循环依赖
            self.image_matcher = ImageMatcher(data_sources)

        # 创建初始checkpoint
        initial_data = {
            "topic": topic,
            "data_sources": data_sources,
            "framework_file": framework_file,
            "stage": "initialized"
        }
        checkpoint_id = self.create_checkpoint("report_start", initial_data)

        try:
            # 第一步：生成或加载框架
            if framework_file and Path(framework_file).exists():
                print(f"📖 加载框架文件: {framework_file}")
                with open(framework_file, 'r', encoding='utf-8') as f:
                    framework_content = f.read()
                framework = self._parse_framework_from_content(framework_content)
            else:
                print(f"🎯 生成报告框架...")
                framework = self._generate_framework(topic, data_sources)

            # 保存框架checkpoint
            framework_data = initial_data.copy()
            framework_data.update({
                "framework": framework,
                "stage": "framework_generated"
            })
            self.create_checkpoint("framework_generated", framework_data)

            # 第二步：生成内容
            print(f"📝 生成报告内容...")
            if self.use_async:
                content = self._generate_content_async(framework, topic, data_sources)
            else:
                content = self._generate_content_sync(framework, topic, data_sources)

            # 第三步：优化和清理内容
            print(f"✨ 优化报告内容...")
            optimized_content = self._optimize_content(content, topic)

            # 第四步：生成图表（如果启用）
            if self.report_config["enable_chart_generation"]:
                print(f"📊 生成图表...")
                optimized_content = self._generate_charts_for_content(optimized_content, topic)

            # 第五步：嵌入图片（如果启用）
            if self.report_config["enable_image_embedding"] and self.image_matcher:
                print(f"🖼️ 嵌入相关图片...")
                optimized_content = self.image_matcher.embed_relevant_images(optimized_content)

            # 第六步：最终清理和格式化
            print(f"🔧 最终格式化...")
            final_content = self.robust_cleaner.clean_content_robustly(optimized_content)

            # 保存最终报告
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_filename = f"{topic.replace(' ', '_')}_{timestamp}.md"
            report_path = output_dir / report_filename

            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(final_content)

            # 创建完成checkpoint
            final_data = framework_data.copy()
            final_data.update({
                "final_content": final_content,
                "report_path": str(report_path),
                "stage": "completed",
                "word_count": len(final_content),
                "completion_time": datetime.now().isoformat()
            })
            self.create_checkpoint("report_completed", final_data)

            print(f"\n✅ 报告生成完成!")
            print(f"📄 报告文件: {report_path}")
            print(f"📊 字数统计: {len(final_content):,} 字符")
            print(f"💾 Checkpoint: {checkpoint_id}")

            return str(report_path)

        except Exception as e:
            print(f"❌ 报告生成失败: {str(e)}")
            # 保存错误checkpoint
            error_data = initial_data.copy()
            error_data.update({
                "error": str(e),
                "stage": "error",
                "error_time": datetime.now().isoformat()
            })
            self.create_checkpoint("report_error", error_data)
            raise

    def _generate_framework(self, topic: str, data_sources: List[str]) -> dict:
        """生成报告框架"""
        # 这里应该包含框架生成的完整逻辑
        # 由于篇幅限制，这里提供一个简化版本
        framework_prompt = f"""
请为主题"{topic}"生成一个详细的报告框架。

要求：
1. 生成{self.report_config['primary_sections']}个一级标题
2. 每个一级标题下包含3-5个二级标题
3. 最大深度不超过{self.report_config['max_depth']}级
4. 目标总字数约{self.report_config['target_words']}字

请以JSON格式输出框架结构。
"""

        try:
            response, _ = self.api_manager.generate_content_with_model(
                framework_prompt, self.ORCHESTRATOR_MODEL
            )
            
            if hasattr(response, 'text'):
                response_text = response.text
            else:
                response_text = str(response)

            # 解析框架（简化版本）
            framework = {
                "title": topic,
                "sections": [
                    {
                        "title": f"第{i+1}章 章节标题",
                        "level": 1,
                        "children": [
                            {"title": f"第{i+1}.{j+1}节 子章节", "level": 2}
                            for j in range(3)
                        ]
                    }
                    for i in range(self.report_config['primary_sections'])
                ]
            }

            return framework

        except Exception as e:
            print(f"⚠️ 框架生成失败，使用默认框架: {str(e)}")
            return self._get_default_framework(topic)

    def _get_default_framework(self, topic: str) -> dict:
        """获取默认框架"""
        return {
            "title": topic,
            "sections": [
                {
                    "title": "概述与背景",
                    "level": 1,
                    "children": [
                        {"title": "研究背景", "level": 2},
                        {"title": "研究目的", "level": 2},
                        {"title": "研究方法", "level": 2}
                    ]
                },
                {
                    "title": "现状分析",
                    "level": 1,
                    "children": [
                        {"title": "发展现状", "level": 2},
                        {"title": "主要特征", "level": 2},
                        {"title": "存在问题", "level": 2}
                    ]
                },
                {
                    "title": "深入研究",
                    "level": 1,
                    "children": [
                        {"title": "技术发展", "level": 2},
                        {"title": "市场分析", "level": 2},
                        {"title": "竞争格局", "level": 2}
                    ]
                },
                {
                    "title": "结论与建议",
                    "level": 1,
                    "children": [
                        {"title": "主要结论", "level": 2},
                        {"title": "发展建议", "level": 2},
                        {"title": "未来展望", "level": 2}
                    ]
                }
            ]
        }

    def _parse_framework_from_content(self, content: str) -> dict:
        """从内容中解析框架"""
        # 简化的框架解析逻辑
        lines = content.split('\n')
        sections = []
        
        for line in lines:
            line = line.strip()
            if line.startswith('#'):
                level = len(line) - len(line.lstrip('#'))
                title = line.lstrip('#').strip()
                sections.append({
                    "title": title,
                    "level": level,
                    "children": []
                })
        
        return {
            "title": "解析的框架",
            "sections": sections
        }

    def _generate_content_sync(self, framework: dict, topic: str, data_sources: List[str]) -> str:
        """同步生成内容"""
        content_parts = []
        
        # 添加标题
        content_parts.append(f"# {framework['title']}\n")
        
        # 生成各章节内容
        for section in framework.get('sections', []):
            section_content = self._generate_section_content(section, topic, data_sources)
            content_parts.append(section_content)
        
        return '\n\n'.join(content_parts)

    def _generate_content_async(self, framework: dict, topic: str, data_sources: List[str]) -> str:
        """异步生成内容"""
        # 简化的异步实现
        return self._generate_content_sync(framework, topic, data_sources)

    def _generate_section_content(self, section: dict, topic: str, data_sources: List[str]) -> str:
        """生成章节内容"""
        level = section.get('level', 1)
        title = section.get('title', '')
        
        # 生成标题
        header = '#' * level + ' ' + title
        
        # 生成内容
        content_prompt = f"""
请为报告章节"{title}"生成详细内容。

主题: {topic}
章节级别: {level}级标题
要求: 生成500-1000字的专业内容

请直接输出章节内容，不要包含标题。
"""
        
        try:
            response, _ = self.api_manager.generate_content_with_model(
                content_prompt, self.EXECUTOR_MODEL
            )
            
            if hasattr(response, 'text'):
                content = response.text
            else:
                content = str(response)
            
            # 处理子章节
            children_content = []
            for child in section.get('children', []):
                child_content = self._generate_section_content(child, topic, data_sources)
                children_content.append(child_content)
            
            # 组合内容
            full_content = [header, content]
            if children_content:
                full_content.extend(children_content)
            
            return '\n\n'.join(full_content)
            
        except Exception as e:
            print(f"⚠️ 章节内容生成失败: {str(e)}")
            return f"{header}\n\n[内容生成失败: {str(e)}]"

    def _optimize_content(self, content: str, topic: str) -> str:
        """优化内容"""
        print("🔧 正在优化内容...")
        
        # 使用结构化生成器优化内容
        optimization_prompt = f"""
请优化以下报告内容，提高其专业性和可读性：

{content}

要求：
1. 保持原有结构和逻辑
2. 提高语言的专业性和准确性
3. 确保内容的连贯性和完整性
4. 修正任何语法或格式错误

请直接输出优化后的内容。
"""
        
        try:
            structured_result = self.structured_generator.generate_structured_content(
                optimization_prompt, "content_optimization"
            )
            
            optimized_content = structured_result.get("final_content", {}).get("content", content)
            return optimized_content
            
        except Exception as e:
            print(f"⚠️ 内容优化失败，返回原内容: {str(e)}")
            return content

    def _generate_charts_for_content(self, content: str, topic: str) -> str:
        """为内容生成图表"""
        print("📊 正在生成图表...")
        
        try:
            # 生成产业链图表
            chart_path = self.chart_generator.generate_industry_chain_chart(topic, {})
            if chart_path:
                content += f"\n\n![产业链图表]({chart_path})\n"
            
            # 生成市场规模图表
            chart_path = self.chart_generator.generate_market_size_chart(topic, {})
            if chart_path:
                content += f"\n\n![市场规模图表]({chart_path})\n"
            
            # 生成技术趋势图表
            chart_path = self.chart_generator.generate_technology_trend_chart(topic, {})
            if chart_path:
                content += f"\n\n![技术趋势图表]({chart_path})\n"
            
            return content
            
        except Exception as e:
            print(f"⚠️ 图表生成失败: {str(e)}")
            return content

    # ==================== 工具方法 ====================

    def get_generation_stats(self) -> dict:
        """获取生成统计信息"""
        if hasattr(self.api_manager, 'get_api_status_summary'):
            api_stats = self.api_manager.get_api_status_summary()
        else:
            api_stats = {"total": 0, "available": 0}
        
        return {
            "api_manager_type": "异步" if self.use_async else "同步",
            "api_stats": api_stats,
            "current_checkpoint": self.current_checkpoint_id,
            "report_config": self.report_config.copy(),
            "model_config": self.get_model_config()
        }

    def cleanup_resources(self):
        """清理资源"""
        print("🧹 清理资源...")
        
        # 清理临时文件
        temp_files = []
        for temp_file in temp_files:
            try:
                if Path(temp_file).exists():
                    Path(temp_file).unlink()
            except Exception as e:
                print(f"⚠️ 清理临时文件失败 {temp_file}: {str(e)}")
        
        print("✅ 资源清理完成")

    def __del__(self):
        """析构函数"""
        try:
            self.cleanup_resources()
        except:
            pass