#!/usr/bin/env python3
"""
测试10层深度智能框架系统
简化版测试，专门验证10层深度功能
"""

import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from complete_report_generator import CompleteReportGenerator

def test_10_layer_template():
    """测试10层深度模板生成"""
    print("🧪 测试10层深度模板生成")
    print("=" * 50)
    
    try:
        generator = CompleteReportGenerator(use_async=False)
        
        print("🏗️ 生成10层深度智能框架模板...")
        template = generator._get_smart_framework_template(max_depth=10)
        
        print("✅ 模板生成成功！")
        
        # 统计模板结构
        sections = template.get("sections", [])
        print(f"📊 模板统计:")
        print(f"   • 一级标题: {len(sections)} 个")
        
        # 检查第一个section的深度
        if sections:
            first_section = sections[0]
            depth = count_max_depth(first_section)
            print(f"   • 最大深度: {depth} 层")
            
            # 显示第一个section的结构
            print(f"   • 第一个section结构:")
            print_section_structure(first_section, indent="     ")
        
        return template
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def count_max_depth(section):
    """计算section的最大深度"""
    if not section.get("children"):
        return section.get("level", 1)
    
    max_child_depth = 0
    for child in section["children"]:
        child_depth = count_max_depth(child)
        max_child_depth = max(max_child_depth, child_depth)
    
    return max_child_depth

def print_section_structure(section, indent="", max_items=3):
    """打印section结构（限制显示数量）"""
    title = section.get("title", "")
    level = section.get("level", 1)
    active = section.get("active", False)
    status = "✅" if active else "⚪"
    
    print(f"{indent}{status} L{level}: {title}")
    
    children = section.get("children", [])
    if children:
        # 只显示前几个子节点
        for i, child in enumerate(children[:max_items]):
            print_section_structure(child, indent + "  ", max_items)
        
        if len(children) > max_items:
            print(f"{indent}  ... (还有 {len(children) - max_items} 个子节点)")

def test_ai_matching_with_depth():
    """测试AI框架匹配（包含深度结构）"""
    print("\n🧪 测试AI框架匹配（包含深度结构）")
    print("=" * 50)
    
    try:
        generator = CompleteReportGenerator(use_async=False)
        
        # 创建包含5层深度的AI框架
        ai_framework = {
            "sections": [
                {
                    "title": "1. 核电产业概览",
                    "level": 1,
                    "children": [
                        {
                            "title": "1.1. 技术基础",
                            "level": 2,
                            "children": [
                                {
                                    "title": "1.1.1. 核裂变原理",
                                    "level": 3,
                                    "children": [
                                        {
                                            "title": "1.1.1.1. 原子核结构",
                                            "level": 4,
                                            "children": [
                                                {
                                                    "title": "1.1.1.1.1. 质子与中子",
                                                    "level": 5,
                                                    "children": []
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                }
            ]
        }
        
        print("🔄 使用智能框架系统处理5层深度AI框架...")
        smart_framework = generator.create_smart_framework_from_ai_response(
            ai_framework, 
            "核电产业研究报告"
        )
        
        print("✅ AI框架匹配成功！")
        return smart_framework
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """主测试函数"""
    print("🧠 10层深度智能框架系统测试")
    print("=" * 60)
    print("🎯 目标: 验证完整的10层深度结构支持")
    print("🔧 功能: 智能模板生成 + AI框架匹配")
    print("=" * 60)
    
    # 测试1: 10层深度模板生成
    template = test_10_layer_template()
    
    # 测试2: AI框架匹配（包含深度）
    if template:
        smart_framework = test_ai_matching_with_depth()
    
    print("\n" + "=" * 60)
    if template:
        print("✅ 10层深度智能框架系统测试完成！")
        print("💡 系统已成功支持完整的10层深度结构")
        print("🎯 可以处理复杂的多层嵌套报告框架")
    else:
        print("❌ 测试未完全通过，请检查代码")

if __name__ == "__main__":
    main()
