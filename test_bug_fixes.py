#!/usr/bin/env python3
"""
测试BUG修复
"""

import sys
import os
import asyncio
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 强制重新加载模块
import importlib
if 'complete_report_generator' in sys.modules:
    importlib.reload(sys.modules['complete_report_generator'])

from complete_report_generator import CompleteReportGenerator

def test_class_reference_fix():
    """测试类引用修复"""
    print("🔧 测试类引用修复")
    print("=" * 50)
    
    try:
        # 尝试创建CompleteReportGenerator实例
        generator = CompleteReportGenerator(use_async=False)
        print("✅ CompleteReportGenerator类正常工作")
        
        # 检查是否有test_search_functionality方法
        if hasattr(generator, 'test_search_functionality'):
            print("✅ test_search_functionality方法存在")
        else:
            print("❌ test_search_functionality方法不存在")
        
        return True
        
    except NameError as e:
        if "AIReportGenerator" in str(e):
            print(f"❌ 仍然存在AIReportGenerator引用错误: {e}")
            return False
        else:
            print(f"❌ 其他NameError: {e}")
            return False
    except Exception as e:
        print(f"❌ 其他异常: {e}")
        return False

def test_instruction_generation_coverage():
    """测试指导生成覆盖率修复"""
    print("📝 测试指导生成覆盖率修复")
    print("=" * 50)
    
    # 创建模拟的章节节点
    mock_chapter_nodes = [
        {
            "node": {"title": "技术发展现状", "level": 1},
            "global_id": "节点001",
            "section_idx": 0
        },
        {
            "node": {"title": "市场规模分析", "level": 2},
            "global_id": "节点002", 
            "section_idx": 0
        },
        {
            "node": {"title": "竞争格局", "level": 2},
            "global_id": "节点003",
            "section_idx": 0
        },
        {
            "node": {"title": "主要厂商", "level": 3},
            "global_id": "节点004",
            "section_idx": 0
        },
        {
            "node": {"title": "市场份额", "level": 3},
            "global_id": "节点005",
            "section_idx": 0
        }
    ]
    
    # 模拟AI返回的部分指导（只覆盖部分节点）
    mock_batch_data = {
        "节点001": {
            "title": "技术发展现状",
            "content_requirements": "全面分析技术发展现状",
            "word_count": "3000-5000字"
        },
        "节点002": {
            "title": "市场规模分析", 
            "content_requirements": "详细分析市场规模",
            "word_count": "2000-3000字"
        },
        "节点003": {
            "title": "竞争格局",
            "content_requirements": "分析竞争格局",
            "word_count": "2000-3000字"
        }
        # 注意：节点004和节点005没有指导
    }
    
    print(f"模拟场景:")
    print(f"  总节点数: {len(mock_chapter_nodes)}")
    print(f"  AI返回指导数: {len(mock_batch_data)}")
    print(f"  覆盖率: {len(mock_batch_data)/len(mock_chapter_nodes)*100:.1f}%")
    
    # 测试覆盖率检查逻辑
    expected_count = len(mock_chapter_nodes)
    covered_nodes = 0
    
    for item in mock_chapter_nodes:
        global_id = item["global_id"]
        title = item["node"].get("title", "无标题")
        if global_id in mock_batch_data or title in mock_batch_data:
            covered_nodes += 1
    
    coverage_rate = covered_nodes / expected_count
    min_coverage_rate = 0.7
    min_instructions = max(1, expected_count // 2)
    
    print(f"\n覆盖率检查:")
    print(f"  实际覆盖: {covered_nodes}/{expected_count} ({coverage_rate*100:.1f}%)")
    print(f"  最低要求: {min_coverage_rate*100:.1f}% 或至少 {min_instructions} 个指导")
    
    # 检查是否满足条件
    if coverage_rate >= min_coverage_rate or len(mock_batch_data) >= min_instructions:
        print(f"  ✅ 满足覆盖率要求")
        
        # 模拟补充默认指导
        supplemented_count = 0
        for item in mock_chapter_nodes:
            global_id = item["global_id"]
            title = item["node"].get("title", "无标题")
            if global_id not in mock_batch_data and title not in mock_batch_data:
                # 添加默认指导
                level = item["node"].get("level", 1)
                if level == 1:
                    word_count = "3000-5000字"
                elif level == 2:
                    word_count = "2000-3000字"
                else:
                    word_count = "1000-2000字"
                
                mock_batch_data[global_id] = {
                    "title": title,
                    "content_requirements": "全面分析相关内容，确保专业性和准确性",
                    "word_count": word_count
                }
                supplemented_count += 1
        
        print(f"  📝 补充了 {supplemented_count} 个默认指导")
        print(f"  📊 最终指导数: {len(mock_batch_data)}/{expected_count} (100%)")
        
        return True
    else:
        print(f"  ❌ 不满足覆盖率要求")
        return False

def test_coverage_scenarios():
    """测试不同覆盖率场景"""
    print("🎯 测试不同覆盖率场景")
    print("=" * 50)
    
    scenarios = [
        {"name": "高覆盖率", "nodes": 10, "instructions": 9, "expected": True},
        {"name": "中等覆盖率", "nodes": 10, "instructions": 7, "expected": True},
        {"name": "低覆盖率但满足最小数量", "nodes": 10, "instructions": 5, "expected": True},
        {"name": "极低覆盖率", "nodes": 10, "instructions": 2, "expected": False},
        {"name": "单节点场景", "nodes": 1, "instructions": 1, "expected": True},
        {"name": "单节点无指导", "nodes": 1, "instructions": 0, "expected": False},
    ]
    
    for scenario in scenarios:
        nodes_count = scenario["nodes"]
        instructions_count = scenario["instructions"]
        expected_result = scenario["expected"]
        
        coverage_rate = instructions_count / nodes_count
        min_coverage_rate = 0.7
        min_instructions = max(1, nodes_count // 2)
        
        actual_result = coverage_rate >= min_coverage_rate or instructions_count >= min_instructions
        
        status = "✅" if actual_result == expected_result else "❌"
        print(f"{status} {scenario['name']}: {instructions_count}/{nodes_count} ({coverage_rate*100:.1f}%) -> {actual_result}")
    
    print("✅ 覆盖率场景测试完成")

async def test_async_functionality():
    """测试异步功能"""
    print("⚡ 测试异步功能")
    print("=" * 50)
    
    try:
        # 创建异步生成器
        generator = CompleteReportGenerator(use_async=True)
        print("✅ 异步CompleteReportGenerator创建成功")
        
        # 检查异步API管理器
        if hasattr(generator, 'api_manager'):
            print("✅ API管理器存在")
            if hasattr(generator.api_manager, 'generate_content_with_model_async'):
                print("✅ 异步API方法存在")
            else:
                print("⚠️ 异步API方法不存在，将回退到同步方法")
        
        return True
        
    except Exception as e:
        print(f"❌ 异步功能测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🎯 BUG修复测试")
    print("=" * 80)
    print()
    
    # 测试1: 类引用修复
    result1 = test_class_reference_fix()
    print()
    
    # 测试2: 指导生成覆盖率修复
    result2 = test_instruction_generation_coverage()
    print()
    
    # 测试3: 不同覆盖率场景
    test_coverage_scenarios()
    print()
    
    # 测试4: 异步功能
    result4 = asyncio.run(test_async_functionality())
    print()
    
    # 总结
    print("🎉 测试总结")
    print("=" * 80)
    
    results = [
        ("类引用修复", result1),
        ("指导生成覆盖率修复", result2),
        ("异步功能", result4)
    ]
    
    success_count = sum(1 for _, result in results if result)
    total_count = len(results)
    
    for name, result in results:
        status = "✅" if result else "❌"
        print(f"{status} {name}")
    
    print(f"\n📊 总体结果: {success_count}/{total_count} 项测试通过")
    
    if success_count == total_count:
        print("🎉 所有BUG修复都成功！")
        print("📋 现在可以重新运行报告生成，应该不会再出现这些问题")
    else:
        print("⚠️ 部分修复可能需要进一步检查")
    
    print("\n🚀 BUG修复测试完成！")
