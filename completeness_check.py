#!/usr/bin/env python3
"""
代码完整性检查脚本
验证重构后的模块是否包含原文件的所有功能
"""

import os
from pathlib import Path

def check_module_completeness():
    """检查模块完整性"""
    print("🔍 代码完整性检查")
    print("=" * 60)
    
    # 检查原文件
    original_file = Path("complete_report_generator.py")
    if not original_file.exists():
        print("❌ 原文件不存在")
        return False
    
    # 读取原文件统计信息
    with open(original_file, 'r', encoding='utf-8') as f:
        original_content = f.read()
        original_lines = len(original_content.split('\n'))
        original_chars = len(original_content)
    
    print(f"📄 原文件统计:")
    print(f"   文件: {original_file}")
    print(f"   行数: {original_lines:,}")
    print(f"   字符数: {original_chars:,}")
    
    # 检查重构后的模块
    modules_dir = Path("complete_report_generator_modules_final")
    if not modules_dir.exists():
        print("❌ 重构模块目录不存在")
        return False
    
    print(f"\n📁 重构模块统计:")
    total_lines = 0
    total_chars = 0
    module_files = []
    
    for py_file in modules_dir.glob("*.py"):
        if py_file.name == "complete_report_generator_backup.py":
            continue  # 跳过备份文件
            
        try:
            with open(py_file, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = len(content.split('\n'))
                chars = len(content)
                
                total_lines += lines
                total_chars += chars
                
                module_files.append({
                    'name': py_file.name,
                    'lines': lines,
                    'chars': chars
                })
                
                print(f"   {py_file.name:<25} {lines:>6,} 行  {chars:>8,} 字符")
                
        except Exception as e:
            print(f"   ❌ 读取失败 {py_file.name}: {str(e)}")
    
    print(f"   {'-'*50}")
    print(f"   {'总计':<25} {total_lines:>6,} 行  {total_chars:>8,} 字符")
    
    # 比较统计
    print(f"\n📊 完整性对比:")
    line_coverage = (total_lines / original_lines) * 100 if original_lines > 0 else 0
    char_coverage = (total_chars / original_chars) * 100 if original_chars > 0 else 0
    
    print(f"   行数覆盖率: {line_coverage:.1f}% ({total_lines:,} / {original_lines:,})")
    print(f"   字符覆盖率: {char_coverage:.1f}% ({total_chars:,} / {original_chars:,})")
    
    # 检查关键类是否存在
    print(f"\n🔍 关键类检查:")
    key_classes = [
        "GeminiModelConfig",
        "ImageMatcher", 
        "ChartGenerator",
        "TokenManager",
        "GeminiAPILimits",
        "AsyncConfig",
        "BaseGeminiAPIManager",
        "GeminiAPIManager", 
        "AsyncGeminiAPIManager",
        "CompleteReportGenerator",
        "SearchTrigger",
        "SearchManager",
        "SearchToolManager",
        "StructuredContentGenerator",
        "RobustContentCleaner"
    ]
    
    found_classes = []
    for py_file in modules_dir.glob("*.py"):
        if py_file.name == "complete_report_generator_backup.py":
            continue
            
        try:
            with open(py_file, 'r', encoding='utf-8') as f:
                content = f.read()
                for class_name in key_classes:
                    if f"class {class_name}" in content:
                        found_classes.append(class_name)
                        print(f"   ✅ {class_name:<25} 在 {py_file.name}")
        except Exception as e:
            print(f"   ❌ 检查失败 {py_file.name}: {str(e)}")
    
    missing_classes = set(key_classes) - set(found_classes)
    if missing_classes:
        print(f"\n❌ 缺失的类:")
        for class_name in missing_classes:
            print(f"   - {class_name}")
    else:
        print(f"\n✅ 所有关键类都已找到")
    
    # 检查关键函数
    print(f"\n🔍 关键函数检查:")
    key_functions = [
        "create_directories",
        "create_framework_file", 
        "create_sample_data",
        "get_user_inputs",
        "validate_data_sources",
        "main",
        "manage_checkpoints"
    ]
    
    found_functions = []
    for py_file in modules_dir.glob("*.py"):
        if py_file.name == "complete_report_generator_backup.py":
            continue
            
        try:
            with open(py_file, 'r', encoding='utf-8') as f:
                content = f.read()
                for func_name in key_functions:
                    if f"def {func_name}" in content:
                        found_functions.append(func_name)
                        print(f"   ✅ {func_name:<25} 在 {py_file.name}")
        except Exception as e:
            print(f"   ❌ 检查失败 {py_file.name}: {str(e)}")
    
    missing_functions = set(key_functions) - set(found_functions)
    if missing_functions:
        print(f"\n❌ 缺失的函数:")
        for func_name in missing_functions:
            print(f"   - {func_name}")
    else:
        print(f"\n✅ 所有关键函数都已找到")
    
    # 检查导入兼容性
    print(f"\n🔍 导入兼容性检查:")
    init_file = modules_dir / "__init__.py"
    if init_file.exists():
        try:
            with open(init_file, 'r', encoding='utf-8') as f:
                init_content = f.read()
                
            exported_items = []
            if "__all__" in init_content:
                # 提取__all__列表中的项目
                import re
                all_match = re.search(r'__all__\s*=\s*\[(.*?)\]', init_content, re.DOTALL)
                if all_match:
                    all_content = all_match.group(1)
                    # 提取引号中的内容
                    items = re.findall(r'[\'"]([^\'"]+)[\'"]', all_content)
                    exported_items = items
            
            print(f"   导出项目数量: {len(exported_items)}")
            print(f"   主要导出项目:")
            for item in exported_items[:10]:  # 显示前10个
                print(f"     - {item}")
            if len(exported_items) > 10:
                print(f"     ... 还有 {len(exported_items) - 10} 个项目")
                
        except Exception as e:
            print(f"   ❌ 检查__init__.py失败: {str(e)}")
    else:
        print(f"   ❌ __init__.py文件不存在")
    
    # 总结
    print(f"\n📋 完整性检查总结:")
    
    if line_coverage >= 80 and len(missing_classes) == 0 and len(missing_functions) == 0:
        print(f"   ✅ 代码重构完整性: 优秀")
        print(f"   ✅ 所有关键组件都已正确提取")
        print(f"   ✅ 模块化拆分成功")
        return True
    elif line_coverage >= 60:
        print(f"   ⚠️ 代码重构完整性: 良好")
        print(f"   ⚠️ 部分组件可能需要补充")
        return True
    else:
        print(f"   ❌ 代码重构完整性: 需要改进")
        print(f"   ❌ 重要组件缺失，需要进一步完善")
        return False

if __name__ == "__main__":
    check_module_completeness()