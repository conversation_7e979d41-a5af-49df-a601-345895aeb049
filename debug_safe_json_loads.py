#!/usr/bin/env python3
"""
调试safe_json_loads函数
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from complete_report_generator import clean_json_string, fix_empty_string_values
import json
import re

def debug_safe_json_loads(json_str: str) -> dict:
    """调试版本的safe_json_loads"""
    
    print("🔍 调试safe_json_loads处理过程")
    print("=" * 60)
    
    original_str = json_str
    print(f"📄 原始输入: {repr(json_str[:100])}...")
    print()
    
    # 第三次尝试：深度清理
    print("🔧 第三次尝试：深度清理")
    
    # 移除markdown代码块标记
    if '```json' in json_str:
        start = json_str.find('```json') + 7
        end = json_str.find('```', start)
        if end != -1:
            json_str = json_str[start:end]
            print(f"   移除markdown后: {repr(json_str[:100])}...")
    elif '```' in json_str:
        start = json_str.find('```') + 3
        end = json_str.find('```', start)
        if end != -1:
            json_str = json_str[start:end]
            print(f"   移除代码块后: {repr(json_str[:100])}...")
    
    # 提取JSON对象
    json_str = json_str.strip()
    if not json_str.startswith('{'):
        # 尝试找到第一个{
        start_idx = json_str.find('{')
        if start_idx != -1:
            json_str = json_str[start_idx:]
            print(f"   提取JSON对象后: {repr(json_str[:100])}...")

    if not json_str.endswith('}'):
        # 尝试找到最后一个}
        end_idx = json_str.rfind('}')
        if end_idx != -1:
            json_str = json_str[:end_idx + 1]
            print(f"   修正结尾后: {repr(json_str[:100])}...")
    
    # 修复常见的JSON格式问题
    json_str = re.sub(r',\s*}', '}', json_str)  # 移除尾随逗号
    json_str = re.sub(r',\s*]', ']', json_str)  # 移除数组尾随逗号
    print(f"   移除尾随逗号后: {repr(json_str[:100])}...")
    
    # 修复空字符串值问题
    print("   🔧 调用fix_empty_string_values...")
    json_str = fix_empty_string_values(json_str)
    print(f"   修复空值后: {repr(json_str[:100])}...")
    
    # 最后的逗号修复
    json_str = json_str.replace(
        '"word_count": "80-100字"\n        },',
        '"word_count": "80-100字",\n        },'
    )
    json_str = json_str.replace(
        '"word_count": "100-150字"\n        }',
        '"word_count": "100-150字"\n        }'
    )
    print(f"   最后逗号修复后: {repr(json_str[:100])}...")
    
    print()
    print("📋 最终JSON内容:")
    print(json_str)
    print()
    
    try:
        result = json.loads(json_str)
        print("✅ 解析成功!")
        return result
    except json.JSONDecodeError as e:
        print(f"❌ 解析失败: {e}")
        print(f"错误位置: 行{e.lineno}, 列{e.colno}")
        return {}

def test_debug():
    """测试调试函数"""
    
    problem_json = '''{
    "instructions": {
        "节点001": {
            "title": "1. 总论：核电产业概览与研究界定",
            "content_requirements": "
            "word_count": "80-100字"
        },
        "节点024": {
            "title": "2. 核电技术发展现状",
            "content_requirements": "详细分析核电技术发展",
            "word_count": "100-150字"
        }
    }
}'''
    
    result = debug_safe_json_loads(problem_json)
    if result:
        instructions = result.get("instructions", {})
        print(f"🎯 成功解析，指导数量: {len(instructions)}")

if __name__ == "__main__":
    test_debug()
