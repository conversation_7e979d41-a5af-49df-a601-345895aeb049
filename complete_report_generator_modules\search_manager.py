"""
搜索管理器模块 - 处理各种搜索API和搜索需求分析
"""
import os
import json
import time
from typing import Dict, Any, List, Optional
from pathlib import Path

class SearchTrigger:
    """搜索需求识别系统"""

    def __init__(self, generator):
        self.generator = generator

    def analyze_content_gaps(self, generated_content, topic):
        """分析内容缺口，确定搜索需求"""
        gaps = []

        # 时效性检查
        if self.needs_latest_data(generated_content, topic):
            gaps.append({
                'type': 'latest_data',
                'query': f'{topic} 最新数据 2024 2025',
                'priority': 'high',
                'reason': '内容可能缺乏最新的市场数据和发展动态'
            })

        # 市场数据检查
        if self.lacks_market_data(generated_content, topic):
            gaps.append({
                'type': 'market_data',
                'query': f'{topic} 市场规模 竞争格局 行业分析',
                'priority': 'medium',
                'reason': '缺少详细的市场分析和竞争格局信息'
            })

        # 技术发展检查
        if self.needs_tech_updates(generated_content, topic):
            gaps.append({
                'type': 'technology',
                'query': f'{topic} 技术发展 创新 突破',
                'priority': 'medium',
                'reason': '需要补充最新的技术发展和创新信息'
            })

        # 政策法规检查
        if self.needs_policy_info(generated_content, topic):
            gaps.append({
                'type': 'policy',
                'query': f'{topic} 政策 法规 标准 监管',
                'priority': 'medium',
                'reason': '缺少相关政策法规和行业标准信息'
            })

        # 案例研究检查
        if self.needs_case_studies(generated_content, topic):
            gaps.append({
                'type': 'cases',
                'query': f'{topic} 案例 项目 应用 实践',
                'priority': 'low',
                'reason': '需要补充实际案例和应用实践'
            })

        return gaps

    def needs_latest_data(self, content, topic):
        """检查是否需要最新数据"""
        # topic参数用于上下文分析
        # 检查内容中是否缺少2024年的数据
        current_year_mentions = content.count('2024') + content.count('2025')
        data_keywords = ['数据', '统计', '报告', '调研', '市场规模']
        data_mentions = sum(content.count(keyword) for keyword in data_keywords)

        # 如果数据关键词多但缺少最新年份，则需要最新数据
        return data_mentions > 5 and current_year_mentions < 3

    def lacks_market_data(self, content, topic):
        """检查是否缺少市场数据"""
        # topic参数用于市场相关性判断
        market_keywords = ['市场', '竞争', '份额', '规模', '增长率', '预测']
        market_score = sum(content.count(keyword) for keyword in market_keywords)

        # 如果市场关键词提及较少，可能需要补充
        return market_score < 10

    def needs_tech_updates(self, content, topic):
        """检查是否需要技术更新"""
        tech_keywords = ['技术', '创新', '突破', '发展', '趋势', '前沿']
        tech_score = sum(content.count(keyword) for keyword in tech_keywords)

        # 技术类主题通常需要最新的技术信息
        tech_topics = ['AI', '人工智能', '区块链', '物联网', '5G', '云计算', '大数据']
        is_tech_topic = any(keyword in topic for keyword in tech_topics)

        return is_tech_topic and tech_score < 15

    def needs_policy_info(self, content, topic):
        """检查是否需要政策信息"""
        # topic参数用于政策相关性判断
        policy_keywords = ['政策', '法规', '标准', '监管', '规范', '指导']
        policy_score = sum(content.count(keyword) for keyword in policy_keywords)

        # 如果政策关键词较少，可能需要补充
        return policy_score < 5

    def needs_case_studies(self, content, topic):
        """检查是否需要案例研究"""
        # topic参数用于案例相关性判断
        case_keywords = ['案例', '项目', '应用', '实践', '示例', '成功']
        case_score = sum(content.count(keyword) for keyword in case_keywords)

        # 如果案例关键词较少，可能需要补充
        return case_score < 8


class SearchManager:
    """搜索API管理器"""

    def __init__(self, generator):
        self.generator = generator
        self.search_apis = {}
        self.init_search_apis()

    def init_search_apis(self):
        """初始化搜索API"""
        # Metaso Search API (优先使用，使用固定密钥)
        try:
            # 使用您提供的固定API密钥
            metaso_api_key = 'mk-988A8E4DC50C53312E3D1A8729687F4C'
            self.search_apis['metaso'] = {
                'api_key': metaso_api_key,
                'enabled': True
            }
            print("✅ Metaso Search API 已配置 (使用固定密钥)")
            print("   🌐 支持网页搜索模式")
            print("   🎓 支持学术搜索模式")
        except Exception as e:
            print(f"⚠️ Metaso Search API 配置失败: {str(e)}")

        # Google Custom Search API
        try:
            google_api_key = os.getenv('GOOGLE_SEARCH_API_KEY')
            google_cx = os.getenv('GOOGLE_SEARCH_CX')
            if google_api_key and google_cx:
                self.search_apis['google'] = {
                    'api_key': google_api_key,
                    'cx': google_cx,
                    'enabled': True
                }
                print("✅ Google Search API 已配置")
            else:
                print("⚠️ Google Search API 未配置")
        except Exception as e:
            print(f"⚠️ Google Search API 配置失败: {str(e)}")

        # Bing Search API
        try:
            bing_api_key = os.getenv('BING_SEARCH_API_KEY')
            if bing_api_key:
                self.search_apis['bing'] = {
                    'api_key': bing_api_key,
                    'enabled': True
                }
                print("✅ Bing Search API 已配置")
            else:
                print("⚠️ Bing Search API 未配置")
        except Exception as e:
            print(f"⚠️ Bing Search API 配置失败: {str(e)}")

    def search_google(self, query, num_results=5):
        """使用Google Custom Search API搜索"""
        if 'google' not in self.search_apis or not self.search_apis['google']['enabled']:
            return []

        try:
            import requests

            api_key = self.search_apis['google']['api_key']
            cx = self.search_apis['google']['cx']

            url = "https://www.googleapis.com/customsearch/v1"
            params = {
                'key': api_key,
                'cx': cx,
                'q': query,
                'num': min(num_results, 10),
                'dateRestrict': 'y1'  # 限制在一年内的结果
            }

            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()

            data = response.json()
            results = []

            for item in data.get('items', []):
                results.append({
                    'title': item.get('title', ''),
                    'url': item.get('link', ''),
                    'snippet': item.get('snippet', ''),
                    'source': 'google',
                    'date': item.get('pagemap', {}).get('metatags', [{}])[0].get('article:published_time', '')
                })

            return results

        except Exception as e:
            print(f"⚠️ Google搜索失败: {str(e)}")
            return []

    def search_bing(self, query, num_results=5):
        """使用Bing Search API搜索"""
        if 'bing' not in self.search_apis or not self.search_apis['bing']['enabled']:
            return []

        try:
            import requests

            api_key = self.search_apis['bing']['api_key']

            url = "https://api.bing.microsoft.com/v7.0/search"
            headers = {
                'Ocp-Apim-Subscription-Key': api_key
            }
            params = {
                'q': query,
                'count': min(num_results, 10),
                'freshness': 'Year'  # 限制在一年内的结果
            }

            response = requests.get(url, headers=headers, params=params, timeout=10)
            response.raise_for_status()

            data = response.json()
            results = []

            for item in data.get('webPages', {}).get('value', []):
                results.append({
                    'title': item.get('name', ''),
                    'url': item.get('url', ''),
                    'snippet': item.get('snippet', ''),
                    'source': 'bing',
                    'date': item.get('dateLastCrawled', '')
                })

            return results

        except Exception as e:
            print(f"⚠️ Bing搜索失败: {str(e)}")
            return []

    def search_metaso(self, query, scope='webpage', num_results=5):
        """使用Metaso Search API搜索"""
        try:
            import http.client
            import json

            # 使用固定的API密钥
            api_key = 'mk-988A8E4DC50C53312E3D1A8729687F4C'

            conn = http.client.HTTPSConnection("metaso.cn")

            payload = json.dumps({
                "model": "metaso-search",
                "messages": [
                    {
                        "role": "user",
                        "content": query
                    }
                ],
                "search_scope": scope,  # 'webpage' 或 'academic'
                "max_results": min(num_results, 10)
            })

            headers = {
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json'
            }

            conn.request("POST", "/v1/chat/completions", payload, headers)
            res = conn.getresponse()
            data = res.read()

            response_data = json.loads(data.decode("utf-8"))

            # 解析搜索结果
            results = []
            if 'choices' in response_data and response_data['choices']:
                content = response_data['choices'][0]['message']['content']
                
                # 尝试从响应中提取结构化信息
                # Metaso的响应通常包含搜索结果的总结
                results.append({
                    'title': f'Metaso搜索结果: {query}',
                    'url': '',
                    'snippet': content,
                    'source': 'metaso',
                    'date': time.strftime('%Y-%m-%d')
                })

            return results

        except Exception as e:
            print(f"⚠️ Metaso搜索失败: {str(e)}")
            return []

    def comprehensive_search(self, query, max_results_per_api=3):
        """综合搜索 - 使用多个搜索API"""
        all_results = []

        print(f"🔍 开始综合搜索: {query}")

        # 优先使用Metaso搜索（网页模式）
        print("   📱 Metaso网页搜索...")
        metaso_web_results = self.search_metaso(query, 'webpage', max_results_per_api)
        all_results.extend(metaso_web_results)

        # Metaso学术搜索
        print("   🎓 Metaso学术搜索...")
        metaso_academic_results = self.search_metaso(query, 'academic', max_results_per_api)
        all_results.extend(metaso_academic_results)

        # Google搜索
        print("   🌐 Google搜索...")
        google_results = self.search_google(query, max_results_per_api)
        all_results.extend(google_results)

        # Bing搜索
        print("   🔍 Bing搜索...")
        bing_results = self.search_bing(query, max_results_per_api)
        all_results.extend(bing_results)

        print(f"   ✅ 搜索完成，共获得 {len(all_results)} 个结果")

        return all_results

    def format_search_results(self, results):
        """格式化搜索结果为文本"""
        if not results:
            return "未找到相关搜索结果。"

        formatted_text = "## 🔍 搜索结果补充信息\n\n"

        for i, result in enumerate(results, 1):
            formatted_text += f"### {i}. {result.get('title', '无标题')}\n"
            if result.get('url'):
                formatted_text += f"**来源**: {result['url']}\n"
            formatted_text += f"**搜索引擎**: {result.get('source', '未知')}\n"
            if result.get('date'):
                formatted_text += f"**日期**: {result['date']}\n"
            formatted_text += f"**摘要**: {result.get('snippet', '无摘要')}\n\n"

        return formatted_text