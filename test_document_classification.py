#!/usr/bin/env python3
"""
测试智能文档分类功能
验证AI驱动的文档分类是否正常工作
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from complete_report_generator import CompleteReportGenerator

def create_test_documents():
    """创建测试文档"""
    print("🔧 创建测试文档...")
    
    test_dir = Path("test_classification_docs")
    test_dir.mkdir(exist_ok=True)
    
    # 创建不同主题的文档
    documents = [
        {
            "name": "industry_background.md",
            "content": """# 行业背景分析

## 行业概述
人工智能行业是当前最具发展潜力的技术领域之一。

## 发展历程
从1956年达特茅斯会议开始，人工智能经历了多次发展浪潮。

## 市场现状
目前全球AI市场规模已达到数千亿美元。
"""
        },
        {
            "name": "technical_analysis.txt",
            "content": """技术分析报告

核心技术：
1. 机器学习算法
2. 深度神经网络
3. 自然语言处理
4. 计算机视觉

技术趋势：
- 大模型技术快速发展
- 多模态AI成为热点
- 边缘计算与AI结合
"""
        },
        {
            "name": "market_research.csv",
            "content": """年份,市场规模(亿美元),增长率(%),主要应用
2020,1500,15.2,语音识别
2021,1800,20.0,图像识别
2022,2200,22.2,自然语言处理
2023,2800,27.3,生成式AI
2024,3500,25.0,多模态AI
"""
        },
        {
            "name": "future_trends.docx",
            "content": """未来发展趋势

1. 技术发展方向
   - AGI通用人工智能
   - 量子计算与AI结合
   - 脑机接口技术

2. 应用前景
   - 智能制造
   - 智慧医疗
   - 自动驾驶

3. 挑战与机遇
   - 数据隐私保护
   - 算法公平性
   - 人机协作模式
"""
        },
        {
            "name": "company_profiles.json",
            "content": """{
  "companies": [
    {
      "name": "OpenAI",
      "focus": "大语言模型",
      "products": ["GPT-4", "ChatGPT", "DALL-E"]
    },
    {
      "name": "Google",
      "focus": "AI基础设施",
      "products": ["Gemini", "TensorFlow", "Cloud AI"]
    },
    {
      "name": "Microsoft",
      "focus": "AI应用集成",
      "products": ["Copilot", "Azure AI", "Bing Chat"]
    }
  ]
}"""
        }
    ]
    
    created_files = []
    for doc in documents:
        file_path = test_dir / doc["name"]
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(doc["content"])
        created_files.append(str(file_path))
        print(f"   📄 创建: {doc['name']}")
    
    print(f"✅ 创建了 {len(created_files)} 个测试文档")
    return test_dir, created_files

def create_test_framework():
    """创建测试报告框架"""
    framework_sections = [
        {
            "title": "行业背景",
            "subsections": [
                {"title": "行业概述"},
                {"title": "发展历程"},
                {"title": "市场现状"}
            ]
        },
        {
            "title": "技术分析", 
            "subsections": [
                {"title": "核心技术"},
                {"title": "技术对比"},
                {"title": "技术趋势"}
            ]
        },
        {
            "title": "市场研究",
            "subsections": [
                {"title": "市场规模"},
                {"title": "竞争格局"},
                {"title": "用户需求"}
            ]
        },
        {
            "title": "发展前景",
            "subsections": [
                {"title": "未来趋势"},
                {"title": "机遇挑战"},
                {"title": "发展建议"}
            ]
        }
    ]
    
    return framework_sections

def test_document_classification():
    """测试文档分类功能"""
    print("\n🧠 测试智能文档分类功能")
    print("=" * 60)
    
    try:
        # 创建测试文档
        test_dir, created_files = create_test_documents()
        
        # 创建测试框架
        framework_sections = create_test_framework()
        
        # 创建报告生成器
        generator = CompleteReportGenerator(use_async=False)
        
        print(f"\n📁 测试数据源: {test_dir}")
        print(f"📄 文档数量: {len(created_files)}")
        print(f"📋 框架章节: {len(framework_sections)}")
        
        # 显示框架结构
        print(f"\n📋 报告框架结构:")
        for i, section in enumerate(framework_sections, 1):
            print(f"   {i}. {section['title']}")
            for j, subsection in enumerate(section.get('subsections', []), 1):
                print(f"      {i}.{j} {subsection['title']}")
        
        # 执行智能文档分类
        print(f"\n🧠 开始智能文档分类...")
        classified_path = generator.classify_documents_by_framework(str(test_dir), framework_sections)
        
        print(f"\n✅ 分类完成!")
        print(f"📁 分类后路径: {classified_path}")
        
        # 检查分类结果
        classified_dir = Path(classified_path)
        if classified_dir.exists():
            print(f"\n📂 分类结果检查:")
            
            for section in framework_sections:
                title = section['title']
                section_folder = classified_dir / generator._sanitize_folder_name(title)
                
                if section_folder.exists():
                    files_in_section = list(section_folder.glob("*"))
                    print(f"   📁 {title}: {len(files_in_section)} 个文件")
                    
                    for file_path in files_in_section:
                        print(f"      📄 {file_path.name}")
                else:
                    print(f"   📁 {title}: 文件夹不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 文档分类测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 清理测试文件
        try:
            import shutil
            if test_dir.exists():
                shutil.rmtree(test_dir)
                print(f"\n🗑️ 清理测试目录: {test_dir}")
            
            # 清理分类后的目录
            classified_dir = Path(f"{test_dir}_classified")
            if classified_dir.exists():
                shutil.rmtree(classified_dir)
                print(f"🗑️ 清理分类目录: {classified_dir}")
        except:
            pass

def test_tokens_calculation():
    """测试tokens计算功能"""
    print("\n🧮 测试tokens计算功能")
    print("=" * 60)
    
    try:
        # 创建测试文档
        test_dir, created_files = create_test_documents()
        
        # 创建报告生成器
        generator = CompleteReportGenerator(use_async=False)
        token_manager = generator.token_manager
        
        print(f"\n📊 文件tokens估算:")
        total_tokens = 0
        
        for file_path in created_files:
            file_info = token_manager.estimate_file_tokens(file_path, include_images=True)
            file_name = Path(file_path).name
            tokens = file_info.get("estimated_tokens", 0)
            file_type = file_info.get("file_type", "unknown")
            
            total_tokens += tokens
            print(f"   📄 {file_name}: {tokens:,} tokens ({file_type})")
        
        print(f"\n📊 总计tokens: {total_tokens:,}")
        print(f"📊 Token限制: {token_manager.max_tokens:,}")
        print(f"📊 是否超过限制: {'是' if total_tokens > token_manager.max_tokens else '否'}")
        
        # 测试读取策略
        print(f"\n🎯 测试读取策略:")
        strategy = token_manager.get_optimal_reading_strategy(created_files)
        
        print(f"   策略类型: {strategy['strategy']}")
        print(f"   包含图片: {'是' if strategy['include_images'] else '否'}")
        print(f"   预计tokens: {strategy['estimated_tokens']:,}")
        print(f"   读取文件数: {len(strategy['files_to_read'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ tokens计算测试失败: {str(e)}")
        return False
    finally:
        # 清理测试文件
        try:
            import shutil
            if test_dir.exists():
                shutil.rmtree(test_dir)
                print(f"\n🗑️ 清理测试目录: {test_dir}")
        except:
            pass

if __name__ == "__main__":
    print("🚀 智能文档分类功能测试")
    print("=" * 80)
    
    print("\n📋 测试内容:")
    print("1. 智能文档分类：根据报告框架自动分类文档")
    print("2. Tokens计算：验证文档tokens估算和读取策略")
    print("3. AI分类验证：检查AI分类结果的准确性")
    
    # 测试文档分类
    classification_success = test_document_classification()
    
    # 测试tokens计算
    tokens_success = test_tokens_calculation()
    
    if classification_success and tokens_success:
        print("\n🎉 所有测试通过！")
        print("✅ 智能文档分类功能正常工作")
        print("✅ Tokens计算和管理功能正常")
    else:
        print("\n⚠️ 部分测试失败")
        if not classification_success:
            print("❌ 文档分类功能存在问题")
        if not tokens_success:
            print("❌ Tokens计算功能存在问题")
    
    print("\n📋 功能总结:")
    print("1. ✅ 根据报告框架智能分类文档")
    print("2. ✅ AI理解文档内容与章节的匹配度")
    print("3. ✅ 支持一个文档匹配多个章节")
    print("4. ✅ 自动处理文件命名冲突")
    print("5. ✅ 完整的fallback机制")
    print("6. ✅ 与现有缓存系统无缝集成")
