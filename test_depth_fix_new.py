#!/usr/bin/env python3
"""
测试深度修复功能
验证系统是否按照用户输入的深度生成对应层级的标题
"""

import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from complete_report_generator import CompleteReportGenerator

def test_5_layer_depth():
    """测试5层深度配置"""
    print("🧪 测试5层深度配置")
    print("=" * 60)
    
    # 创建生成器并设置用户配置
    generator = CompleteReportGenerator(use_async=False)
    
    # 模拟用户输入的配置：6个一级标题，5层深度
    generator.report_config.update({
        "primary_sections": 6,  # 一级标题数量
        "max_depth": 5,         # 最大层级深度
        "target_words": 70000   # 目标字数
    })
    
    print(f"📊 用户配置:")
    print(f"   • 一级标题数量: {generator.report_config['primary_sections']}")
    print(f"   • 最大层级深度: {generator.report_config['max_depth']}")
    print(f"   • 目标字数: {generator.report_config['target_words']:,} 字")
    
    # 模拟AI生成的5层深度框架
    ai_framework = {
        "sections": [
            {
                "title": "1. 总论：核电产业概览与研究界定",
                "level": 1,
                "children": [
                    {
                        "title": "1.1. 产业核心概念界定",
                        "level": 2,
                        "children": [
                            {
                                "title": "1.1.1. 核能基本原理与分类",
                                "level": 3,
                                "children": [
                                    {
                                        "title": "1.1.1.1. 核裂变反应机制",
                                        "level": 4,
                                        "children": [
                                            {
                                                "title": "1.1.1.1.1. 链式反应控制原理",
                                                "level": 5,
                                                "children": []
                                            },
                                            {
                                                "title": "1.1.1.1.2. 临界质量计算方法",
                                                "level": 5,
                                                "children": []
                                            }
                                        ]
                                    }
                                ]
                            }
                        ]
                    }
                ]
            }
        ]
    }
    
    topic = "核电产业研究报告"
    
    print(f"\n📋 模拟AI生成的5层深度框架 (主题: {topic}):")
    print("   L1: 1. 总论：核电产业概览与研究界定")
    print("     L2: 1.1. 产业核心概念界定")
    print("       L3: 1.1.1. 核能基本原理与分类")
    print("         L4: 1.1.1.1. 核裂变反应机制")
    print("           L5: 1.1.1.1.1. 链式反应控制原理")
    print("           L5: 1.1.1.1.2. 临界质量计算方法")
    
    print("\n🔄 使用智能框架系统处理...")
    smart_framework = generator.create_smart_framework_from_ai_response(ai_framework, topic)
    
    print("\n✅ 5层深度框架处理完成！")
    
    # 验证生成的框架是否包含5层深度
    max_depth_found = find_max_depth(smart_framework["sections"])
    print(f"\n📊 验证结果:")
    print(f"   • 用户配置深度: 5层")
    print(f"   • 实际生成深度: {max_depth_found}层")
    print(f"   • 深度匹配: {'✅ 正确' if max_depth_found == 5 else '❌ 错误'}")
    
    return smart_framework

def find_max_depth(sections):
    """递归查找框架的最大深度"""
    if not sections:
        return 0
    
    max_depth = 0
    for section in sections:
        current_level = section.get("level", 1)
        max_depth = max(max_depth, current_level)
        
        children = section.get("children", [])
        if children:
            child_max_depth = find_max_depth(children)
            max_depth = max(max_depth, child_max_depth)
    
    return max_depth

def main():
    """主测试函数"""
    print("🎯 深度修复功能测试")
    print("=" * 80)
    print("📋 验证系统是否按照用户输入的深度生成对应层级")
    print("🔧 测试场景: 6个一级标题，5层深度，70000字")
    print("=" * 80)
    
    try:
        # 测试5层深度配置
        smart_framework = test_5_layer_depth()
        
        print("\n" + "=" * 80)
        print("✅ 深度修复功能测试完成！")
        print("💡 系统现在应该按照用户输入的深度生成对应层级")
        print("🎯 不再有硬编码的深度限制")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
