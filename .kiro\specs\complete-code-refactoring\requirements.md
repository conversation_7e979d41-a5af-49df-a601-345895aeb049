# 完整代码重构需求文档

## 介绍

用户要求将 `complete_report_generator.py` 文件（18,322行）进行完整的模块化拆分，严格按照以下要求：
1. 禁止修改任何代码内容（包括具体数字、API密钥等）
2. 禁止增加、删除等修改行为
3. 只能做拆分工作
4. 保持所有功能完整性

## 需求

### 需求1：完整代码拆分

**用户故事**: 作为开发者，我希望将大型单文件代码拆分为多个模块，以便更好地维护代码，同时保持所有原有功能不变。

#### 验收标准

1. WHEN 拆分代码时 THEN 系统应保留原文件中的每一行代码
2. WHEN 拆分代码时 THEN 系统不应修改任何变量名、函数名、类名
3. WHEN 拆分代码时 THEN 系统不应修改任何数值、字符串、API密钥
4. WHEN 拆分代码时 THEN 系统不应删除任何注释或空行
5. WHEN 拆分代码时 THEN 系统应确保所有类的完整定义都被包含

### 需求2：AsyncGeminiAPIManager完整实现

**用户故事**: 作为开发者，我需要AsyncGeminiAPIManager类的完整实现，而不是空的占位符。

#### 验收标准

1. WHEN 拆分AsyncGeminiAPIManager类时 THEN 系统应包含原文件中的所有方法
2. WHEN 拆分AsyncGeminiAPIManager类时 THEN 系统应包含所有异步方法的完整实现
3. WHEN 拆分AsyncGeminiAPIManager类时 THEN 系统应保留所有配额管理逻辑
4. WHEN 拆分AsyncGeminiAPIManager类时 THEN 系统应保留所有错误处理机制

### 需求3：CompleteReportGenerator完整实现

**用户故事**: 作为开发者，我需要CompleteReportGenerator类的完整实现，包含所有原有方法。

#### 验收标准

1. WHEN 拆分CompleteReportGenerator类时 THEN 系统应包含所有报告生成方法
2. WHEN 拆分CompleteReportGenerator类时 THEN 系统应包含所有配置管理方法
3. WHEN 拆分CompleteReportGenerator类时 THEN 系统应包含所有异步处理逻辑
4. WHEN 拆分CompleteReportGenerator类时 THEN 系统应保留所有checkpoint功能

### 需求4：缺失类的完整实现

**用户故事**: 作为开发者，我需要确保原文件中的所有类都被完整拆分，不能有任何遗漏。

#### 验收标准

1. WHEN 检查拆分结果时 THEN 系统应包含StructuredContentGenerator的完整实现
2. WHEN 检查拆分结果时 THEN 系统应包含RobustContentCleaner的完整实现
3. WHEN 检查拆分结果时 THEN 系统应包含SearchToolManager的完整实现
4. WHEN 检查拆分结果时 THEN 系统应包含所有辅助函数和工具方法

### 需求5：主函数和示例代码

**用户故事**: 作为开发者，我需要保留原文件中的main函数和所有示例代码。

#### 验收标准

1. WHEN 拆分代码时 THEN 系统应保留main()函数的完整实现
2. WHEN 拆分代码时 THEN 系统应保留所有示例代码和使用说明
3. WHEN 拆分代码时 THEN 系统应保留所有命令行参数处理逻辑
4. WHEN 拆分代码时 THEN 系统应保留checkpoint管理功能

### 需求6：验证完整性

**用户故事**: 作为开发者，我需要验证拆分后的代码与原文件功能完全一致。

#### 验收标准

1. WHEN 运行拆分后的代码时 THEN 系统应提供与原文件相同的所有功能
2. WHEN 导入拆分后的模块时 THEN 系统应能成功导入所有类和函数
3. WHEN 测试拆分后的代码时 THEN 系统应通过所有功能测试
4. WHEN 比较代码行数时 THEN 拆分后的总行数应与原文件相等或接近

## 技术约束

1. 必须保持原有的导入语句和依赖关系
2. 必须保持原有的错误处理逻辑
3. 必须保持原有的配置参数和默认值
4. 必须保持原有的API密钥和模型配置
5. 不允许重构或优化代码逻辑
6. 不允许修改变量命名或代码风格