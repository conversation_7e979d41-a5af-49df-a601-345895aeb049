#!/usr/bin/env python3
"""
模拟真实的错误情况
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from complete_report_generator import safe_json_loads, fix_empty_string_values
import json

def test_real_error_simulation():
    """模拟真实的错误情况"""
    
    print("🎯 模拟真实的错误情况")
    print("=" * 60)
    
    # 根据错误信息，构造可能的真实JSON
    # 错误位置：行17, 列81
    # 清理后内容显示："word_count": "30-50字"},
    
    # 可能的情况1：多个节点，某个节点缺少逗号
    real_error_json_1 = '''{
    "instructions": {
        "节点001": {
            "title": "技术发展趋势",
            "content_requirements": "",
            "word_count": "30-50字"
        },
        "节点002": {
            "title": "市场分析",
            "content_requirements": "",
            "word_count": "50-80字"
        },
        "节点003": {
            "title": "政策环境",
            "content_requirements": "",
            "word_count": "80-100字"
        },
        "节点004": {
            "title": "投资机会",
            "content_requirements": "",
            "word_count": "100-150字"
        },
        "节点005": {
            "title": "风险评估",
            "content_requirements": "",
            "word_count": "150-200字"
        }
    }
}'''
    
    print("📝 测试情况1: 多节点JSON")
    test_json_parsing("情况1", real_error_json_1)
    
    # 可能的情况2：包含空的content_requirements
    real_error_json_2 = '''{
    "instructions": {
        "节点001": {
            "title": "技术发展趋势",
            "content_requirements": "
            "word_count": "30-50字"
        },
        "节点005": {
            "title": "要点分析",
            "content_requirements": "详细分析要点",
            "word_count": "50-80字"
        }
    }
}'''
    
    print("📝 测试情况2: 包含空content_requirements")
    test_json_parsing("情况2", real_error_json_2)
    
    # 可能的情况3：复杂的嵌套和格式问题
    real_error_json_3 = '''{
    "instructions": {
        "节点001": {
            "title": "技术发展趋势",
            "content_requirements": "",
            "word_count": "30-50字"},
        "节点002": {
            "title": "市场分析",
            "content_requirements": "",
            "word_count": "50-80字"
        },
        "节点003": {
            "title": "政策环境",
            "content_requirements": "",
            "word_count": "80-100字"
        }
    }
}'''
    
    print("📝 测试情况3: 混合格式问题")
    test_json_parsing("情况3", real_error_json_3)

def test_json_parsing(case_name: str, json_str: str):
    """测试JSON解析"""
    
    print(f"\n🔍 {case_name}:")
    
    # 显示JSON结构
    lines = json_str.split('\n')
    print("JSON结构:")
    for i, line in enumerate(lines, 1):
        if i <= 20:  # 只显示前20行
            print(f"{i:2d}: {line}")
        elif i == 21:
            print("    ...")
    print()
    
    # 标准解析
    print("❌ 标准json.loads解析:")
    try:
        result = json.loads(json_str)
        print(f"✅ 标准解析成功: {len(result.get('instructions', {}))} 个指导")
    except json.JSONDecodeError as e:
        print(f"💥 标准解析失败: {e}")
        print(f"   错误位置: 行{e.lineno}, 列{e.colno}")
        
        # 显示错误位置
        if e.lineno <= len(lines):
            error_line = lines[e.lineno - 1]
            print(f"   错误行: {repr(error_line)}")
            if e.colno <= len(error_line):
                print(f"   错误字符: '{error_line[e.colno-1:e.colno+5]}'")
    
    # 修复解析
    print("✅ safe_json_loads解析:")
    try:
        result = safe_json_loads(json_str)
        if result:
            print("🎯 修复解析成功!")
            instructions = result.get("instructions", {})
            print(f"   📊 指导数量: {len(instructions)}")
            for node_id, instruction in instructions.items():
                title = instruction.get("title", "无标题")
                word_count = instruction.get("word_count", "")
                print(f"   📋 {node_id}: {title} ({word_count})")
        else:
            print("❌ 修复解析失败")
    except Exception as e:
        print(f"❌ 修复解析异常: {e}")
    
    print("-" * 40)

def test_fix_empty_string_values_directly():
    """直接测试fix_empty_string_values函数"""
    
    print("\n🔧 直接测试fix_empty_string_values函数")
    print("=" * 60)
    
    # 测试包含空content_requirements的情况
    test_json = '''{
    "instructions": {
        "节点001": {
            "title": "技术发展趋势",
            "content_requirements": "
            "word_count": "30-50字"
        }
    }
}'''
    
    print("原始JSON:")
    print(repr(test_json))
    print()
    
    print("调用fix_empty_string_values后:")
    fixed = fix_empty_string_values(test_json)
    print(repr(fixed))
    print()
    
    print("修复后的可读格式:")
    print(fixed)
    print()
    
    # 测试解析
    try:
        result = json.loads(fixed)
        print("✅ 修复后解析成功!")
        print(f"指导数量: {len(result.get('instructions', {}))}")
    except Exception as e:
        print(f"❌ 修复后解析失败: {e}")

if __name__ == "__main__":
    test_real_error_simulation()
    test_fix_empty_string_values_directly()
    
    print("\n🎉 测试完成！")
    print("📋 如果所有测试都通过，说明修复逻辑是正确的")
    print("📋 如果实际运行时还有问题，可能需要查看具体的API响应内容")
