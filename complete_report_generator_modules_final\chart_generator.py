"""
图表生成模块 - 专业图表生成器
从complete_report_generator.py中提取的完整ChartGenerator类
"""
import os
import platform
import warnings
from pathlib import Path
from typing import Dict, Any

# 图表生成相关导入
try:
    import matplotlib
    matplotlib.use('Agg')  # 使用非交互式后端
    import matplotlib.pyplot as plt
    import matplotlib.font_manager as fm
    import numpy as np
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("⚠️ matplotlib未安装，图表生成功能将被禁用")

try:
    import plotly.graph_objects as go
    import plotly.express as px
    from plotly.subplots import make_subplots
    import plotly.io as pio
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False
    print("⚠️ plotly未安装，交互式图表功能将被禁用")


class ChartGenerator:
    """专业图表生成器 - 为产业研究报告生成高质量图表"""

    def __init__(self, output_dir: Path = None):
        self.output_dir = output_dir or Path("charts")
        self.output_dir.mkdir(exist_ok=True)

        # 设置中文字体
        self._setup_chinese_fonts()

    def _setup_chinese_fonts(self):
        """设置中文字体支持"""
        if MATPLOTLIB_AVAILABLE:
            try:
                import platform
                import os
                import warnings

                # 禁用字体相关警告
                warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib.font_manager')
                warnings.filterwarnings('ignore', message='.*Glyph.*missing from current font.*')

                # 获取系统类型
                system = platform.system()

                # 根据系统设置合适的中文字体
                if system == "Windows":
                    # Windows系统常见中文字体
                    fonts = ['Microsoft YaHei', 'SimHei', 'SimSun', 'KaiTi', 'FangSong']
                elif system == "Darwin":  # macOS
                    fonts = ['PingFang SC', 'Hiragino Sans GB', 'STHeiti', 'Arial Unicode MS']
                else:  # Linux
                    fonts = ['WenQuanYi Micro Hei', 'WenQuanYi Zen Hei', 'Noto Sans CJK SC', 'DejaVu Sans']

                # 检查可用字体
                available_fonts = [f.name for f in fm.fontManager.ttflist]
                chinese_font = None

                for font in fonts:
                    if font in available_fonts:
                        chinese_font = font
                        break

                if chinese_font:
                    plt.rcParams['font.sans-serif'] = [chinese_font] + fonts
                    plt.rcParams['font.family'] = 'sans-serif'
                    self.chinese_font_available = True
                    print(f"✅ 成功设置中文字体: {chinese_font}")
                else:
                    # 尝试下载中文字体
                    print("⚠️ 未找到系统中文字体，尝试下载...")
                    if self._download_chinese_font():
                        self.chinese_font_available = True
                    else:
                        print("⚠️ 中文字体下载失败，将使用英文标题")
                        self.chinese_font_available = False
                        self._use_fallback_font()

                # 解决负号显示问题
                plt.rcParams['axes.unicode_minus'] = False

                # 设置字体大小
                plt.rcParams['font.size'] = 10
                plt.rcParams['axes.titlesize'] = 14
                plt.rcParams['axes.labelsize'] = 12
                plt.rcParams['xtick.labelsize'] = 10
                plt.rcParams['ytick.labelsize'] = 10
                plt.rcParams['legend.fontsize'] = 10

            except (ImportError, OSError, RuntimeError) as e:
                print(f"⚠️ 中文字体设置失败: {str(e)}")
                self.chinese_font_available = False
                self._use_fallback_font()
            except Exception as e:
                print(f"⚠️ 字体设置未知错误: {str(e)}")
                self.chinese_font_available = False
                self._use_fallback_font()
        else:
            self.chinese_font_available = False

    def _download_chinese_font(self):
        """下载并设置中文字体"""
        try:
            import urllib.request
            import shutil

            # 创建字体目录
            font_dir = Path.home() / ".matplotlib" / "fonts"
            font_dir.mkdir(parents=True, exist_ok=True)

            # 使用内置的中文字体文件路径（如果存在）
            font_file = font_dir / "NotoSansCJK-Regular.ttc"

            if not font_file.exists():
                print("📥 正在下载中文字体...")
                # 使用Google Noto字体（开源免费）
                font_url = "https://github.com/googlefonts/noto-cjk/raw/main/Sans/OTC/NotoSansCJK-Regular.ttc"

                try:
                    with urllib.request.urlopen(font_url, timeout=30) as response:
                        with open(font_file, 'wb') as f:
                            shutil.copyfileobj(response, f)
                    print("✅ 中文字体下载成功")
                except Exception as download_error:
                    print(f"⚠️ 字体下载失败: {download_error}")
                    # 使用备用方案
                    return False

            # 重新加载字体缓存
            try:
                fm._rebuild()
            except:
                pass

            # 设置字体
            plt.rcParams['font.sans-serif'] = ['Noto Sans CJK SC', 'SimHei', 'Microsoft YaHei']
            plt.rcParams['axes.unicode_minus'] = False
            print("✅ 中文字体设置完成")
            return True

        except Exception as e:
            print(f"⚠️ 字体下载设置失败: {str(e)}")
            return False

    def _use_fallback_font(self):
        """使用备用字体方案"""
        try:
            # 使用matplotlib内置的字体，并设置为支持Unicode
            plt.rcParams['font.family'] = 'sans-serif'
            plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'Liberation Sans']
            plt.rcParams['axes.unicode_minus'] = False

            # 设置字体编码
            import matplotlib
            matplotlib.rcParams['font.size'] = 12

            # 禁用字体警告
            import warnings
            warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')

            print("✅ 使用备用字体方案（已禁用中文字体警告）")
        except Exception as e:
            print(f"⚠️ 备用字体设置失败: {str(e)}")

    def _ensure_chinese_font(self):
        """确保中文字体可用"""
        try:
            # 如果已经有中文字体可用，直接返回
            if hasattr(self, 'chinese_font_available') and self.chinese_font_available:
                return True

            # 禁用字体警告
            import warnings
            warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib.font_manager')
            warnings.filterwarnings('ignore', message='.*Glyph.*missing from current font.*')

            # 如果没有中文字体，使用英文标题的备用方案
            if not hasattr(self, 'chinese_font_available') or not self.chinese_font_available:
                print("⚠️ 中文字体不可用，将使用英文标题")
                self._use_fallback_font()
                return False

            return True

        except Exception as e:
            print(f"⚠️ 中文字体设置失败: {str(e)}")
            # 使用最基本的字体设置
            try:
                plt.rcParams['font.family'] = 'sans-serif'
                plt.rcParams['axes.unicode_minus'] = False
                # 禁用字体警告
                import warnings
                warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')
                return False
            except:
                return False

    def generate_industry_chain_chart(self, topic: str, chain_data: dict) -> str:
        """生成产业链图表"""
        if not MATPLOTLIB_AVAILABLE:
            return ""

        try:
            # 确保中文字体设置
            has_chinese_font = self._ensure_chinese_font()

            fig, ax = plt.subplots(figsize=(12, 8))

            # 根据字体可用性选择标签
            if has_chinese_font:
                # 使用中文标签
                stages = ['上游勘探', '中游开发', '下游应用']
                companies = {
                    '上游勘探': ['地质勘探公司', '钻井服务商', '设备制造商'],
                    '中游开发': ['地热电站开发商', '工程承包商', '运营维护商'],
                    '下游应用': ['电网公司', '工业用户', '居民用户']
                }
                title = f'{topic} - 产业链结构图'
                chart_filename = f"{topic}_产业链图.png"
            else:
                # 使用英文标签
                stages = ['Upstream', 'Midstream', 'Downstream']
                companies = {
                    'Upstream': ['Exploration Co.', 'Drilling Service', 'Equipment Mfg.'],
                    'Midstream': ['Power Plant Dev.', 'EPC Contractor', 'O&M Service'],
                    'Downstream': ['Grid Company', 'Industrial Users', 'Residential Users']
                }
                title = f'{topic} - Industry Chain Structure'
                chart_filename = f"{topic}_industry_chain.png"

            # 绘制产业链流程图
            y_positions = [2, 1, 0]
            colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']

            for i, (stage, companies_list) in enumerate(companies.items()):
                # 绘制主要阶段
                rect = plt.Rectangle((i*3, y_positions[i]), 2.5, 0.8,
                                   facecolor=colors[i], alpha=0.7, edgecolor='black')
                ax.add_patch(rect)
                ax.text(i*3 + 1.25, y_positions[i] + 0.4, stage,
                       ha='center', va='center', fontsize=12, fontweight='bold')

                # 绘制子公司
                for j, company in enumerate(companies_list):
                    ax.text(i*3 + 1.25, y_positions[i] - 0.3 - j*0.2, company,
                           ha='center', va='center', fontsize=9)

            # 绘制箭头连接
            for i in range(len(stages)-1):
                ax.arrow(i*3 + 2.5, y_positions[i] + 0.4, 0.4, 0,
                        head_width=0.1, head_length=0.1, fc='black', ec='black')

            ax.set_xlim(-0.5, 8)
            ax.set_ylim(-1, 3)
            ax.set_title(title, fontsize=16, fontweight='bold', pad=20)
            ax.axis('off')

            # 保存图表
            chart_path = self.output_dir / chart_filename
            plt.tight_layout()
            plt.savefig(chart_path, dpi=300, bbox_inches='tight')
            plt.close()

            print(f"✅ 产业链图表生成成功: {chart_path}")
            return str(chart_path)

        except Exception as e:
            print(f"⚠️ 产业链图表生成失败: {str(e)}")
            return ""

    def generate_market_size_chart(self, topic: str, market_data: dict) -> str:
        """生成市场规模图表"""
        if not MATPLOTLIB_AVAILABLE:
            return ""

        try:
            # 确保中文字体设置
            has_chinese_font = self._ensure_chinese_font()

            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

            # 示例数据：地热发电市场规模
            years = [2020, 2021, 2022, 2023, 2024, 2025, 2026, 2027, 2028, 2029, 2030]
            market_size = [150, 165, 180, 200, 225, 255, 290, 330, 375, 425, 480]  # 亿美元
            growth_rate = [8.5, 10.0, 9.1, 11.1, 12.5, 13.3, 13.7, 13.8, 13.6, 13.3, 12.9]  # %

            # 根据字体可用性选择标签
            if has_chinese_font:
                title1 = '全球地热发电市场规模'
                title2 = '年增长率'
                xlabel = '年份'
                ylabel1 = '市场规模 (亿美元)'
                ylabel2 = '增长率 (%)'
                chart_filename = f"{topic}_市场规模图.png"
            else:
                title1 = 'Global Geothermal Market Size'
                title2 = 'Annual Growth Rate'
                xlabel = 'Year'
                ylabel1 = 'Market Size (Billion USD)'
                ylabel2 = 'Growth Rate (%)'
                chart_filename = f"{topic}_market_size.png"

            # 左图：市场规模趋势
            ax1.plot(years, market_size, marker='o', linewidth=3, markersize=8, color='#2E86AB')
            ax1.fill_between(years, market_size, alpha=0.3, color='#2E86AB')
            ax1.set_title(title1, fontsize=14, fontweight='bold')
            ax1.set_xlabel(xlabel, fontsize=12)
            ax1.set_ylabel(ylabel1, fontsize=12)
            ax1.grid(True, alpha=0.3)

            # 添加数值标签
            for i, v in enumerate(market_size):
                ax1.annotate(f'{v}', (years[i], v), textcoords="offset points",
                           xytext=(0,10), ha='center', fontsize=9)

            # 右图：增长率
            bars = ax2.bar(years, growth_rate, color='#F18F01', alpha=0.8)
            ax2.set_title(title2, fontsize=14, fontweight='bold')
            ax2.set_xlabel(xlabel, fontsize=12)
            ax2.set_ylabel(ylabel2, fontsize=12)
            ax2.grid(True, alpha=0.3, axis='y')

            # 添加数值标签
            for bar, rate in zip(bars, growth_rate):
                height = bar.get_height()
                ax2.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                        f'{rate}%', ha='center', va='bottom', fontsize=9)

            plt.tight_layout()

            # 保存图表
            chart_path = self.output_dir / chart_filename
            plt.savefig(chart_path, dpi=300, bbox_inches='tight')
            plt.close()

            print(f"✅ 市场规模图表生成成功: {chart_path}")
            return str(chart_path)

        except Exception as e:
            print(f"⚠️ 市场规模图表生成失败: {str(e)}")
            return ""

    def generate_technology_trend_chart(self, topic: str, tech_data: dict) -> str:
        """生成技术发展趋势图表"""
        if not MATPLOTLIB_AVAILABLE:
            return ""

        try:
            # 确保中文字体设置
            has_chinese_font = self._ensure_chinese_font()

            fig, ax = plt.subplots(figsize=(12, 8))

            # 根据字体可用性选择标签
            if has_chinese_font:
                technologies = ['传统地热', '增强地热系统(EGS)', '闭环地热', '深层地热']
                xlabel = '技术成熟度 (%)'
                ylabel = '市场潜力 (%)'
                title = f'{topic} - 技术发展趋势分析'
                quadrant_labels = [
                    '高潜力\n低成熟度', '高潜力\n高成熟度',
                    '低潜力\n低成熟度', '低潜力\n高成熟度'
                ]
                chart_filename = f"{topic}_技术趋势图.png"
            else:
                technologies = ['Traditional', 'EGS', 'Closed-loop', 'Deep Geothermal']
                xlabel = 'Technology Maturity (%)'
                ylabel = 'Market Potential (%)'
                title = f'{topic} - Technology Trend Analysis'
                quadrant_labels = [
                    'High Potential\nLow Maturity', 'High Potential\nHigh Maturity',
                    'Low Potential\nLow Maturity', 'Low Potential\nHigh Maturity'
                ]
                chart_filename = f"{topic}_tech_trend.png"

            maturity = [90, 60, 30, 15]  # 技术成熟度 %
            potential = [20, 70, 85, 95]  # 市场潜力 %
            colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']

            # 创建气泡图
            for i, (tech, mat, pot, color) in enumerate(zip(technologies, maturity, potential, colors)):
                # 气泡大小代表投资热度
                bubble_size = (mat + pot) * 5
                ax.scatter(mat, pot, s=bubble_size, c=color, alpha=0.7, edgecolors='black', linewidth=2)
                ax.annotate(tech, (mat, pot), xytext=(5, 5), textcoords='offset points',
                           fontsize=11, fontweight='bold')

            # 添加象限分割线
            ax.axhline(y=50, color='gray', linestyle='--', alpha=0.5)
            ax.axvline(x=50, color='gray', linestyle='--', alpha=0.5)

            # 添加象限标签
            positions = [(25, 75), (75, 75), (25, 25), (75, 25)]
            colors_quad = ['lightblue', 'lightgreen', 'lightcoral', 'lightyellow']

            for (x, y), label, color in zip(positions, quadrant_labels, colors_quad):
                ax.text(x, y, label, ha='center', va='center',
                       bbox=dict(boxstyle="round,pad=0.3", facecolor=color, alpha=0.5))

            ax.set_xlabel(xlabel, fontsize=12)
            ax.set_ylabel(ylabel, fontsize=12)
            ax.set_title(title, fontsize=16, fontweight='bold', pad=20)
            ax.grid(True, alpha=0.3)
            ax.set_xlim(0, 100)
            ax.set_ylim(0, 100)

            # 保存图表
            chart_path = self.output_dir / chart_filename
            plt.tight_layout()
            plt.savefig(chart_path, dpi=300, bbox_inches='tight')
            plt.close()

            print(f"✅ 技术趋势图表生成成功: {chart_path}")
            return str(chart_path)

        except Exception as e:
            print(f"⚠️ 技术趋势图表生成失败: {str(e)}")
            return ""