#!/usr/bin/env python3
"""
调试字符串匹配问题
"""

def debug_string_match():
    """调试字符串匹配问题"""
    
    # 从test_final_fix.py中复制的JSON
    problem_json = '''{
    "instructions": {
        "节点001": {
            "title": "1. 总论：核电产业概览与研究界定",
            "content_requirements": "
            "word_count": "80-100字"
        },
        "节点024": {
            "title": "2. 核电技术发展现状",
            "content_requirements": "详细分析核电技术发展",
            "word_count": "100-150字"
        }
    }
}'''
    
    print("调试字符串匹配问题")
    print("=" * 50)
    
    # 查找问题字符串
    target_str = '"content_requirements": "\n            "word_count":'
    
    print(f"目标字符串: {repr(target_str)}")
    print()
    
    if target_str in problem_json:
        print("✅ 在JSON中找到目标字符串")
        
        # 找到位置
        pos = problem_json.find(target_str)
        print(f"位置: {pos}")
        
        # 显示周围的内容
        start = max(0, pos - 20)
        end = min(len(problem_json), pos + len(target_str) + 20)
        context = problem_json[start:end]
        print(f"上下文: {repr(context)}")
        
    else:
        print("❌ 在JSON中未找到目标字符串")
        
        # 查找相似的字符串
        lines = problem_json.split('\n')
        for i, line in enumerate(lines):
            if 'content_requirements' in line and 'word_count' not in line:
                print(f"第{i+1}行: {repr(line)}")
                if i + 1 < len(lines):
                    next_line = lines[i + 1]
                    print(f"第{i+2}行: {repr(next_line)}")
                    
                    # 组合两行
                    combined = line + '\n' + next_line
                    print(f"组合: {repr(combined)}")
                    
                    if 'word_count' in next_line:
                        print("🔍 找到跨行的模式!")
                        
                        # 测试替换
                        fixed = problem_json.replace(combined, 
                                                   line + '\n            "content_requirements": "",\n' + next_line)
                        print("替换测试:")
                        print(repr(fixed[:200]))

if __name__ == "__main__":
    debug_string_match()
