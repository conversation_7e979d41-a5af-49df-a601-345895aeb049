#!/usr/bin/env python3
"""
测试clean_json_string函数
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from complete_report_generator import clean_json_string, fix_empty_string_values

def test_clean_function():
    """测试clean_json_string函数"""
    
    test_json = '''{
    "instructions": {
        "节点001": {
            "title": "1. 总论：核电产业概览与研究界定",
            "content_requirements": "",
            "word_count": "80-100字"
        }
    }
}'''
    
    print("测试clean_json_string函数:")
    print("原始JSON:")
    print(repr(test_json))
    print()
    
    cleaned = clean_json_string(test_json)
    print("清理后JSON:")
    print(repr(cleaned))
    print()
    
    # 测试解析
    import json
    try:
        result = json.loads(cleaned)
        print("✅ 清理后JSON解析成功!")
    except Exception as e:
        print(f"❌ 清理后JSON解析失败: {e}")

def test_fix_function():
    """测试fix_empty_string_values函数"""
    
    problem_json = '''{
    "instructions": {
        "节点001": {
            "title": "1. 总论：核电产业概览与研究界定",
            "content_requirements": "
            "word_count": "80-100字"
        }
    }
}'''
    
    print("\n测试fix_empty_string_values函数:")
    print("原始JSON:")
    print(repr(problem_json))
    print()
    
    fixed = fix_empty_string_values(problem_json)
    print("修复后JSON:")
    print(repr(fixed))
    print()
    
    # 测试解析
    import json
    try:
        result = json.loads(fixed)
        print("✅ 修复后JSON解析成功!")
        print(f"指导数量: {len(result.get('instructions', {}))}")
    except Exception as e:
        print(f"❌ 修复后JSON解析失败: {e}")

if __name__ == "__main__":
    test_clean_function()
    test_fix_function()
