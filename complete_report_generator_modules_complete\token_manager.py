"""
Token管理器模块 - 处理token限制和分批处理
从原始文件完整提取，不做任何修改
"""
import math
from typing import Dict, Any, List

class TokenManager:
    """Token管理器 - 处理token限制和分批处理"""

    def __init__(self, max_tokens: int = 250000):
        self.max_tokens = max_tokens
        self.token_estimation_ratio = 4  # 估算：1个token约等于4个字符

    def estimate_tokens(self, text: str) -> int:
        """估算文本的token数量"""
        # 简单估算：中文字符约1.5个token，英文单词约1个token
        chinese_chars = len([c for c in text if '\u4e00' <= c <= '\u9fff'])
        other_chars = len(text) - chinese_chars

        # 中文字符按1.5个token计算，其他字符按0.25个token计算
        estimated_tokens = int(chinese_chars * 1.5 + other_chars * 0.25)
        return estimated_tokens

    def needs_splitting(self, text: str) -> bool:
        """检查是否需要分批处理"""
        return self.estimate_tokens(text) > self.max_tokens

    def calculate_batches(self, text: str) -> int:
        """计算需要的批次数量"""
        estimated_tokens = self.estimate_tokens(text)
        if estimated_tokens <= self.max_tokens:
            return 1

        # 向上取整
        batches = math.ceil(estimated_tokens / self.max_tokens)
        return batches

    def split_text_by_tokens(self, text: str) -> List[str]:
        """按token限制分割文本"""
        if not self.needs_splitting(text):
            return [text]

        batches_needed = self.calculate_batches(text)

        # 按字符数平均分割（简单方法）
        text_length = len(text)
        chunk_size = text_length // batches_needed

        chunks = []
        for i in range(batches_needed):
            start = i * chunk_size
            if i == batches_needed - 1:
                # 最后一块包含剩余所有内容
                end = text_length
            else:
                end = (i + 1) * chunk_size
                # 尝试在句号或换行处分割
                while end < text_length and text[end] not in '.。\n':
                    end += 1
                    if end - start > chunk_size * 1.2:  # 避免块过大
                        break

            chunk = text[start:end]
            if chunk.strip():
                chunks.append(chunk.strip())

        return chunks

    def get_token_info(self, text: str) -> Dict[str, Any]:
        """获取文本的token信息"""
        estimated_tokens = self.estimate_tokens(text)
        needs_split = self.needs_splitting(text)
        batches = self.calculate_batches(text) if needs_split else 1

        return {
            "estimated_tokens": estimated_tokens,
            "max_tokens": self.max_tokens,
            "needs_splitting": needs_split,
            "batches_needed": batches,
            "text_length": len(text)
        }