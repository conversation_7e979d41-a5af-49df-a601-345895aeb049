#!/usr/bin/env python3
"""
测试JSON修复功能
专门测试处理AI生成的不完整JSON结构
"""

import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from complete_report_generator import safe_json_loads, fix_empty_string_values, aggressive_json_fix

def test_incomplete_title_fix():
    """测试修复不完整的title字段"""
    print("🧪 测试不完整title字段修复")
    print("=" * 50)
    
    # 模拟问题JSON（类似debug文件中的问题）
    problematic_json = '''
    {
        "sections": [
            {
                "title": "1. 总论：核电产业概览与研究界定",
                "level": 1,
                "children": [
                    {
                        "title": "1.1.1.2.1. 
                        "level": 4,
                        "children": []
                    }
                ]
            }
        ]
    }
    '''
    
    print("原始问题JSON:")
    print(problematic_json[:200] + "...")
    
    print("\n🔧 尝试修复...")
    result = safe_json_loads(problematic_json)
    
    if result:
        print("✅ 修复成功！")
        print(f"解析结果: {len(result.get('sections', []))} 个sections")
        
        # 检查修复后的title
        if result.get('sections'):
            first_section = result['sections'][0]
            if first_section.get('children'):
                problematic_item = first_section['children'][0]
                print(f"修复后的title: {problematic_item.get('title', 'N/A')}")
    else:
        print("❌ 修复失败")

def test_real_debug_file():
    """测试真实的debug文件"""
    print("\n🧪 测试真实debug文件")
    print("=" * 50)
    
    debug_file = "debug_json_error_1754709816.txt"
    if not Path(debug_file).exists():
        print(f"⚠️ Debug文件不存在: {debug_file}")
        return
    
    try:
        with open(debug_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 提取原始JSON部分
        start_marker = "=== 原始JSON内容 ==="
        end_marker = "=== 清理后JSON内容 ==="
        
        start_idx = content.find(start_marker)
        end_idx = content.find(end_marker)
        
        if start_idx != -1 and end_idx != -1:
            json_content = content[start_idx + len(start_marker):end_idx].strip()
            
            print("🔧 尝试修复真实debug文件中的JSON...")
            result = safe_json_loads(json_content)
            
            if result:
                print("✅ 真实文件修复成功！")
                sections = result.get('sections', [])
                print(f"解析结果: {len(sections)} 个sections")
                
                # 统计各级标题数量
                def count_titles(sections, level=1):
                    count = 0
                    for section in sections:
                        if section.get('level') == level:
                            count += 1
                        if section.get('children'):
                            count += count_titles(section['children'], level)
                    return count
                
                for level in range(1, 6):
                    count = count_titles(sections, level)
                    if count > 0:
                        print(f"  {level}级标题: {count} 个")
            else:
                print("❌ 真实文件修复失败")
        else:
            print("⚠️ 无法找到JSON内容标记")
            
    except Exception as e:
        print(f"❌ 读取debug文件失败: {str(e)}")

def test_various_json_problems():
    """测试各种JSON问题"""
    print("\n🧪 测试各种JSON问题")
    print("=" * 50)
    
    test_cases = [
        {
            "name": "不完整的title字段",
            "json": '{"title": "1.1.1.2.1. \n"level": 4}',
            "expected": True
        },
        {
            "name": "缺少逗号",
            "json": '{"title": "test"\n"level": 1}',
            "expected": True
        },
        {
            "name": "多余的逗号",
            "json": '{"title": "test", "level": 1,}',
            "expected": True
        },
        {
            "name": "正常JSON",
            "json": '{"title": "test", "level": 1}',
            "expected": True
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n{i}. 测试: {case['name']}")
        print(f"   输入: {case['json']}")
        
        result = safe_json_loads(case['json'])
        success = bool(result)
        
        if success == case['expected']:
            print(f"   ✅ 通过 - 解析{'成功' if success else '失败'}")
            if result:
                print(f"   结果: {result}")
        else:
            print(f"   ❌ 失败 - 期望{'成功' if case['expected'] else '失败'}，实际{'成功' if success else '失败'}")

if __name__ == "__main__":
    test_incomplete_title_fix()
    test_real_debug_file()
    test_various_json_problems()
    
    print("\n" + "="*50)
    print("✅ JSON修复功能测试完成！")
    print("💡 如果还有JSON解析问题，可以:")
    print("   1. 查看生成的debug文件")
    print("   2. 运行此测试脚本进行诊断")
    print("   3. 根据具体错误模式改进修复函数")
