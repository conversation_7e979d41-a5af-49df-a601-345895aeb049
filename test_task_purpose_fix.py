#!/usr/bin/env python3
"""
测试任务目的识别修复
验证统筹任务和执行任务的正确识别
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_task_purpose_identification():
    """测试任务目的识别"""
    print("🧪 测试任务目的识别")
    print("=" * 50)
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        # 创建报告生成器
        generator = CompleteReportGenerator(use_async=False)
        
        # 测试用例：不同类型的prompt
        test_cases = [
            {
                "name": "任务指导制定",
                "prompt": """作为报告统筹模型，请为以下节点制定简要的任务指导。

节点列表：
第1级标题: 1. 总论：核电产业概览与研究界定 (数据源1)
第2级标题: 1.1. 产业核心概念界定 (数据源1)
第3级标题: 1.1.1. 核电技术基础概念

请为每个节点制定简要指导，包括：
1. 核心内容要求
2. 建议字数""",
                "expected_type": "统筹模型",
                "expected_task": "制定任务指导"
            },
            {
                "name": "章节审核",
                "prompt": """作为专业的报告统筹模型，请对以下章节进行深度审核：

章节标题：市场概览与现状分析
章节内容：
这是一个测试章节的内容...

请从以下维度进行严格审核：
1. 内容完整性
2. 逻辑严谨性""",
                "expected_type": "统筹模型",
                "expected_task": "审核章节"
            },
            {
                "name": "内容生成",
                "prompt": """您正在为一份严肃的产业研究报告撰写"市场概览与现状分析"（第1级标题）部分的内容。这是一份面向投资者、政策制定者和行业专家的专业报告，要求具备高度的严肃性、全面性和权威性。

请根据以下任务指导生成内容：
- 核心内容要求：全面分析市场现状
- 建议字数：3000-3500字""",
                "expected_type": "执行模型",
                "expected_task": "生成内容"
            },
            {
                "name": "框架生成",
                "prompt": """请生成一个关于"人工智能产业发展报告"的详细框架结构，包含8个一级标题。

请以JSON格式返回框架结构：
{
  "framework": [
    {
      "title": "标题",
      "subsections": []
    }
  ]
}""",
                "expected_type": "统筹模型",
                "expected_task": "生成框架"
            },
            {
                "name": "章节优化",
                "prompt": """作为报告统筹模型，请优化以下章节的内容质量：

章节标题：技术发展趋势
当前内容：
这是需要优化的内容...

请从以下方面进行优化：
1. 提升专业性
2. 增强逻辑性""",
                "expected_type": "统筹模型",
                "expected_task": "优化章节"
            }
        ]
        
        print(f"📋 测试用例数量: {len(test_cases)}")
        
        success_count = 0
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n🔍 测试用例 {i}: {test_case['name']}")
            
            # 提取任务目的
            task_purpose = generator.api_manager._extract_task_purpose(test_case['prompt'])
            
            print(f"   识别结果: {task_purpose}")
            print(f"   期望类型: {test_case['expected_type']}")
            print(f"   期望任务: {test_case['expected_task']}")
            
            # 验证识别结果
            is_correct = False
            if test_case['expected_type'] == "统筹模型":
                if "统筹模型" in task_purpose:
                    if test_case['expected_task'] == "制定任务指导" and "制定任务指导" in task_purpose:
                        is_correct = True
                    elif test_case['expected_task'] == "审核章节" and "审核章节" in task_purpose:
                        is_correct = True
                    elif test_case['expected_task'] == "生成框架" and "生成报告框架" in task_purpose:
                        is_correct = True
                    elif test_case['expected_task'] == "优化章节" and "优化章节" in task_purpose:
                        is_correct = True
            elif test_case['expected_type'] == "执行模型":
                if "执行模型" in task_purpose and "生成" in task_purpose:
                    is_correct = True
            
            if is_correct:
                print(f"   ✅ 识别正确")
                success_count += 1
            else:
                print(f"   ❌ 识别错误")
        
        print(f"\n📊 测试结果:")
        print(f"   总测试数: {len(test_cases)}")
        print(f"   成功数: {success_count}")
        print(f"   成功率: {success_count/len(test_cases)*100:.1f}%")
        
        return success_count == len(test_cases)
        
    except Exception as e:
        print(f"❌ 任务目的识别测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_specific_task_instruction_case():
    """测试具体的任务指导案例"""
    print("\n🧪 测试具体的任务指导案例")
    print("=" * 50)
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        # 创建报告生成器
        generator = CompleteReportGenerator(use_async=False)
        
        # 这是从日志中提取的实际prompt
        actual_prompt = """作为报告统筹模型，请为以下节点制定简要的任务指导。

节点列表：
第1级标题: 1. 总论：核电产业概览与研究界定 (数据源1)
第2级标题: 1.1. 产业核心概念界定 (数据源1)
第3级标题: 1.1.1. 核电技术基础概念 (数据源1)
第4级标题: 1.1.1.1. 核反应堆类型与原理 (数据源1)
第4级标题: 1.1.1.2. 核燃料循环体系 (数据源1)

请为每个节点制定简要指导，包括：
1. 核心内容要求
2. 建议字数

请以JSON格式返回：
{
    "instructions": {
        "节点标题": {
            "content_requirements": "核心内容要求",
            "word_count": "建议字数"
        }
    }
}"""
        
        print(f"📋 测试实际的任务指导prompt:")
        print(f"   Prompt长度: {len(actual_prompt)} 字符")
        
        # 提取任务目的
        task_purpose = generator.api_manager._extract_task_purpose(actual_prompt)
        
        print(f"   识别结果: {task_purpose}")
        
        # 验证结果
        if "统筹模型" in task_purpose and "制定任务指导" in task_purpose:
            print(f"   ✅ 正确识别为统筹模型的任务指导制定任务")
            return True
        else:
            print(f"   ❌ 识别错误，应该是统筹模型的任务指导制定任务")
            return False
        
    except Exception as e:
        print(f"❌ 具体任务指导案例测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_model_assignment_workflow():
    """测试模型分工工作流程"""
    print("\n🧪 测试模型分工工作流程")
    print("=" * 50)
    
    print("🎯 正确的工作流程应该是:")
    print("   阶段1: 制定任务指导 (统筹模型 - gemini-2.5-pro)")
    print("   ├── 分析节点结构")
    print("   ├── 制定指导内容")
    print("   ├── 设计生成策略")
    print("   └── 输出任务指导")
    print("")
    print("   阶段2: 执行内容生成 (执行模型 - gemini-2.5-flash)")
    print("   ├── 读取任务指导")
    print("   ├── 加载数据源")
    print("   ├── 生成章节内容")
    print("   └── 输出最终内容")
    
    # 模拟工作流程
    workflow_tests = [
        {
            "stage": "制定任务指导",
            "prompt": "作为报告统筹模型，请为以下节点制定简要的任务指导。",
            "expected_model": "统筹模型"
        },
        {
            "stage": "执行内容生成",
            "prompt": "您正在为一份严肃的产业研究报告撰写\"市场分析\"（第1级标题）部分的内容。",
            "expected_model": "执行模型"
        }
    ]
    
    try:
        from complete_report_generator import CompleteReportGenerator
        generator = CompleteReportGenerator(use_async=False)
        
        success_count = 0
        for test in workflow_tests:
            task_purpose = generator.api_manager._extract_task_purpose(test['prompt'])
            
            print(f"\n📋 {test['stage']}:")
            print(f"   识别结果: {task_purpose}")
            print(f"   期望模型: {test['expected_model']}")
            
            if test['expected_model'] == "统筹模型" and "统筹模型" in task_purpose:
                print(f"   ✅ 正确识别")
                success_count += 1
            elif test['expected_model'] == "执行模型" and "执行模型" in task_purpose:
                print(f"   ✅ 正确识别")
                success_count += 1
            else:
                print(f"   ❌ 识别错误")
        
        return success_count == len(workflow_tests)
        
    except Exception as e:
        print(f"❌ 工作流程测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 任务目的识别修复测试")
    print("=" * 80)
    
    print("\n📋 测试内容:")
    print("1. 任务目的识别测试")
    print("2. 具体任务指导案例测试")
    print("3. 模型分工工作流程测试")
    
    # 测试1：任务目的识别
    test1_success = test_task_purpose_identification()
    
    # 测试2：具体任务指导案例
    test2_success = test_specific_task_instruction_case()
    
    # 测试3：模型分工工作流程
    test3_success = test_model_assignment_workflow()
    
    # 总结
    all_tests_passed = all([test1_success, test2_success, test3_success])
    
    print(f"\n📊 测试结果总结:")
    print(f"   任务目的识别: {'✅ 通过' if test1_success else '❌ 失败'}")
    print(f"   具体任务指导案例: {'✅ 通过' if test2_success else '❌ 失败'}")
    print(f"   模型分工工作流程: {'✅ 通过' if test3_success else '❌ 失败'}")
    
    if all_tests_passed:
        print("\n🎉 所有测试通过！")
        print("✅ 任务目的识别BUG已修复")
        print("✅ 统筹任务正确识别为使用gemini-2.5-pro")
        print("✅ 执行任务正确识别为使用gemini-2.5-flash")
    else:
        print("\n⚠️ 部分测试失败")
    
    print("\n📋 修复说明:")
    print("1. ✅ 添加了对'作为报告统筹模型'的优先识别")
    print("2. ✅ 添加了对'制定任务指导'的专门识别")
    print("3. ✅ 修复了'第X级'误判为执行模型的问题")
    print("4. ✅ 确保统筹任务优先级高于通用识别")
    print("5. ✅ 保持了原有的执行任务识别逻辑")
    
    print("\n🎯 修复效果:")
    print("修复前：'制定任务指导'被错误识别为'📝 执行模型生成第X级标题'")
    print("修复后：'制定任务指导'正确识别为'📋 统筹模型制定任务指导'")
