#!/usr/bin/env python3
"""
测试最终修复效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from complete_report_generator import safe_json_loads

def test_final_fix():
    """测试最终修复效果"""
    
    print("🎯 测试最终JSON修复效果")
    print("=" * 60)
    
    # 测试用例：根据实际错误构造的问题JSON
    problem_json = '''{
    "instructions": {
        "节点001": {
            "title": "1. 总论：核电产业概览与研究界定",
            "content_requirements": "
            "word_count": "80-100字"
        },
        "节点024": {
            "title": "2. 核电技术发展现状",
            "content_requirements": "详细分析核电技术发展",
            "word_count": "100-150字"
        }
    }
}'''
    
    print("📝 测试JSON（包含空值问题）:")
    print(problem_json[:200] + "...")
    print()
    
    print("🔧 使用safe_json_loads解析:")
    try:
        result = safe_json_loads(problem_json)
        if result:
            print("✅ 解析成功!")
            instructions = result.get("instructions", {})
            print(f"   📊 指导数量: {len(instructions)}")
            
            for node_id, instruction in instructions.items():
                title = instruction.get("title", "无标题")
                content_req = instruction.get("content_requirements", "")
                word_count = instruction.get("word_count", "")
                
                print(f"   📋 {node_id}:")
                print(f"      标题: {title}")
                print(f"      内容要求: {content_req[:30]}{'...' if len(content_req) > 30 else ''}")
                print(f"      字数要求: {word_count}")
        else:
            print("❌ 解析失败，返回空结果")
    except Exception as e:
        print(f"❌ 解析异常: {e}")
    
    print()
    print("🎉 测试完成!")

if __name__ == "__main__":
    test_final_fix()
