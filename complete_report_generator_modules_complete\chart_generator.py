"""
图表生成器模块 - 为产业研究报告生成高质量图表
从原始文件完整提取，不做任何修改
"""
from pathlib import Path

# 图表生成相关导入
try:
    import matplotlib
    matplotlib.use('Agg')  # 使用非交互式后端
    import matplotlib.pyplot as plt
    import matplotlib.font_manager as fm
    import numpy as np
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("⚠️ matplotlib未安装，图表生成功能将被禁用")

try:
    import plotly.graph_objects as go
    import plotly.express as px
    from plotly.subplots import make_subplots
    import plotly.io as pio
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False
    print("⚠️ plotly未安装，交互式图表功能将被禁用")

class ChartGenerator:
    """专业图表生成器 - 为产业研究报告生成高质量图表"""

    def __init__(self, output_dir: Path = None):
        self.output_dir = output_dir or Path("charts")
        self.output_dir.mkdir(exist_ok=True)

        # 设置中文字体
        self._setup_chinese_fonts()

    def _setup_chinese_fonts(self):
        """设置中文字体支持"""
        if MATPLOTLIB_AVAILABLE:
            try:
                import platform
                import os
                import warnings

                # 禁用字体相关警告
                warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib.font_manager')
                warnings.filterwarnings('ignore', message='.*Glyph.*missing from current font.*')

                # 获取系统类型
                system = platform.system()

                # 根据系统设置合适的中文字体
                if system == "Windows":
                    # Windows系统常见中文字体
                    fonts = ['Microsoft YaHei', 'SimHei', 'SimSun', 'KaiTi', 'FangSong']
                elif system == "Darwin":  # macOS
                    fonts = ['PingFang SC', 'Hiragino Sans GB', 'STHeiti', 'Arial Unicode MS']
                else:  # Linux
                    fonts = ['WenQuanYi Micro Hei', 'WenQuanYi Zen Hei', 'Noto Sans CJK SC', 'DejaVu Sans']

                # 检查可用字体
                available_fonts = [f.name for f in fm.fontManager.ttflist]
                chinese_font = None

                for font in fonts:
                    if font in available_fonts:
                        chinese_font = font
                        break

                if chinese_font:
                    plt.rcParams['font.sans-serif'] = [chinese_font] + fonts
                    plt.rcParams['font.family'] = 'sans-serif'
                    self.chinese_font_available = True
                    print(f"✅ 成功设置中文字体: {chinese_font}")
                else:
                    # 尝试下载中文字体
                    print("⚠️ 未找到系统中文字体，尝试下载...")
                    if self._download_chinese_font():
                        self.chinese_font_available = True
                    else:
                        print("⚠️ 中文字体下载失败，将使用英文标题")
                        self.chinese_font_available = False
                        self._use_fallback_font()

                # 解决负号显示问题
                plt.rcParams['axes.unicode_minus'] = False

                # 设置字体大小
                plt.rcParams['font.size'] = 10
                plt.rcParams['axes.titlesize'] = 14
                plt.rcParams['axes.labelsize'] = 12
                plt.rcParams['xtick.labelsize'] = 10
                plt.rcParams['ytick.labelsize'] = 10
                plt.rcParams['legend.fontsize'] = 10

            except (ImportError, OSError, RuntimeError) as e:
                print(f"⚠️ 中文字体设置失败: {str(e)}")
                self.chinese_font_available = False
                self._use_fallback_font()
            except Exception as e:
                print(f"⚠️ 字体设置未知错误: {str(e)}")
                self.chinese_font_available = False
                self._use_fallback_font()
        else:
            self.chinese_font_available = False

    def _download_chinese_font(self):
        """下载并设置中文字体"""
        try:
            import urllib.request
            import shutil

            # 创建字体目录
            font_dir = Path.home() / ".matplotlib" / "fonts"
            font_dir.mkdir(parents=True, exist_ok=True)

            # 使用内置的中文字体文件路径（如果存在）
            font_file = font_dir / "NotoSansCJK-Regular.ttc"

            if not font_file.exists():
                print("📥 正在下载中文字体...")
                # 使用Google Noto字体（开源免费）
                font_url = "https://github.com/googlefonts/noto-cjk/raw/main/Sans/OTC/NotoSansCJK-Regular.ttc"

                try:
                    with urllib.request.urlopen(font_url, timeout=30) as response:
                        with open(font_file, 'wb') as f:
                            shutil.copyfileobj(response, f)
                    print("✅ 中文字体下载成功")
                except Exception as download_error:
                    print(f"⚠️ 字体下载失败: {download_error}")
                    # 使用备用方案
                    return False

            # 重新加载字体缓存
            try:
                fm._rebuild()
            except:
                pass

            # 设置字体
            plt.rcParams['font.sans-serif'] = ['Noto Sans CJK SC', 'SimHei', 'Microsoft YaHei']
            plt.rcParams['axes.unicode_minus'] = False
            print("✅ 中文字体设置完成")
            return True

        except Exception as e:
            print(f"⚠️ 字体下载设置失败: {str(e)}")
            return False

    def _use_fallback_font(self):
        """使用备用字体方案"""
        try:
            # 使用matplotlib内置的字体，并设置为支持Unicode
            plt.rcParams['font.family'] = 'sans-serif'
            plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'Liberation Sans']
            plt.rcParams['axes.unicode_minus'] = False

            # 设置字体编码
            import matplotlib
            matplotlib.rcParams['font.size'] = 12

            # 禁用字体警告
            import warnings
            warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')

            print("✅ 使用备用字体方案（已禁用中文字体警告）")
        except Exception as e:
            print(f"⚠️ 备用字体设置失败: {str(e)}")

    def _ensure_chinese_font(self):
        """确保中文字体可用"""
        try:
            # 如果已经有中文字体可用，直接返回
            if hasattr(self, 'chinese_font_available') and self.chinese_font_available:
                return True

            # 禁用字体警告
            import warnings
            warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib.font_manager')
            warnings.filterwarnings('ignore', message='.*Glyph.*missing from current font.*')

            # 如果没有中文字体，使用英文标题的备用方案
            if not hasattr(self, 'chinese_font_available') or not self.chinese_font_available:
                print("⚠️ 中文字体不可用，将使用英文标题")
                self._use_fallback_font()
                return False

            return True

        except Exception as e:
            print(f"⚠️ 中文字体设置失败: {str(e)}")
            # 使用最基本的字体设置
            try:
                plt.rcParams['font.family'] = 'sans-serif'
                plt.rcParams['axes.unicode_minus'] = False
                # 禁用字体警告
                import warnings
                warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')
                return False
            except:
                return False