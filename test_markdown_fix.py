#!/usr/bin/env python3
"""
测试Markdown修复效果
"""

from pathlib import Path
from complete_report_generator import CompleteReportGenerator

def test_markdown_fix():
    """测试Markdown生成修复"""
    print("📝 测试Markdown生成修复")
    print("=" * 50)
    
    generator = CompleteReportGenerator(use_async=False)
    
    # 创建测试框架，包含问题标题
    test_framework = {
        "sections": [
            {
                "title": "产业概览",  # 正常标题
                "level": 1,
                "content": "这是第一章的内容，包含了产业的基本概况。",
                "children": [
                    {
                        "title": "2级标题1",  # 问题标题（包含"级标题"）
                        "level": 2,
                        "content": "这应该被跳过",
                        "children": []
                    },
                    {
                        "title": "市场规模",  # 正常标题
                        "level": 2,
                        "content": "这是关于市场规模的详细分析。",
                        "children": []
                    }
                ]
            },
            {
                "title": "3级标题2",  # 问题标题
                "level": 1,
                "content": "这个章节应该被跳过",
                "children": []
            },
            {
                "title": "技术分析",  # 正常标题
                "level": 1,
                "content": "这是第二章的内容，分析了核心技术。",
                "children": [
                    {
                        "title": "核心技术",
                        "level": 2,
                        "content": "",  # 空内容
                        "children": [
                            {
                                "title": "技术原理",
                                "level": 3,
                                "content": "这是技术原理的详细说明。",
                                "children": []
                            }
                        ]
                    }
                ]
            }
        ]
    }
    
    # 测试Markdown生成
    output_dir = Path("test_output")
    output_dir.mkdir(exist_ok=True)
    
    md_path = generator._generate_markdown(
        "修复测试报告", 
        test_framework, 
        output_dir, 
        "fix_test"
    )
    
    if md_path and Path(md_path).exists():
        print(f"✅ Markdown文档生成成功: {md_path}")
        
        # 检查生成的内容
        with open(md_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        print("\n📄 生成的内容:")
        print("-" * 40)
        print(content)
        print("-" * 40)
        
        # 验证修复效果
        print("\n🔍 验证修复效果:")
        
        # 检查是否包含问题标题
        if "级标题" in content:
            print("❌ 仍然包含问题标题（包含'级标题'）")
            return False
        else:
            print("✅ 已过滤掉问题标题")
        
        # 检查是否有有效内容
        lines = content.split('\n')
        content_lines = [line for line in lines if line.strip() and not line.startswith('#')]
        if len(content_lines) > 0:
            print(f"✅ 包含 {len(content_lines)} 行有效内容")
        else:
            print("⚠️ 没有有效内容")
        
        # 检查标题层级
        title_lines = [line for line in lines if line.startswith('#')]
        print(f"✅ 包含 {len(title_lines)} 个标题")
        
        return True
    else:
        print("❌ Markdown文档生成失败")
        return False

def test_subtitle_generation():
    """测试子标题生成"""
    print("\n🏷️ 测试有意义子标题生成")
    print("=" * 50)
    
    generator = CompleteReportGenerator(use_async=False)
    
    # 测试_get_meaningful_subtitle方法
    print("测试子标题生成:")
    
    for level in [3, 4, 5, 6]:
        for index in [1, 2, 3]:
            subtitle = generator._get_meaningful_subtitle(1, level, index)
            print(f"  {level}级标题{index}: {subtitle}")
    
    print("\n✅ 子标题生成功能正常")
    return True

if __name__ == "__main__":
    print("⚡ 测试Markdown修复效果")
    print("=" * 60)
    
    # 测试1: 子标题生成
    success1 = test_subtitle_generation()
    
    # 测试2: Markdown生成
    success2 = test_markdown_fix()
    
    print("\n" + "=" * 60)
    print("📋 测试结果:")
    print(f"   子标题生成: {'✅ 通过' if success1 else '❌ 失败'}")
    print(f"   Markdown生成: {'✅ 通过' if success2 else '❌ 失败'}")
    
    if success1 and success2:
        print("\n🎉 Markdown修复测试全部通过！")
    else:
        print("\n⚠️ 部分功能需要进一步检查")
