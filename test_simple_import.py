"""
简单的导入测试
"""

def test_chart_generator():
    """测试图表生成器导入"""
    try:
        # 先测试模块是否可以导入
        import complete_report_generator_modules.chart_generator as cg_module
        print("✅ 模块导入成功")
        
        # 检查模块中是否有ChartGenerator类
        if hasattr(cg_module, 'ChartGenerator'):
            print("✅ ChartGenerator类存在")
            
            # 尝试创建实例
            chart_gen = cg_module.ChartGenerator()
            print("✅ ChartGenerator实例创建成功")
            return True
        else:
            print("❌ ChartGenerator类不存在")
            print(f"模块中的属性: {dir(cg_module)}")
            return False
            
    except Exception as e:
        print(f"❌ 导入失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_chart_generator()