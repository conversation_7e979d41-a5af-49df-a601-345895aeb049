{"checkpoint_id": "task_instructions_generated_1754402312", "stage": "task_instructions_generated", "timestamp": 1754402312, "created_time": "2025-08-05 21:58:32", "report_config": {"title": "地热发电产业研究报告", "data_source": "C:/Users/<USER>/Desktop/深海机器人/地热/报告", "output_dir": "output", "max_depth": 3, "primary_sections": 4, "target_words": 50000, "reference_report": "D:/new/11.30copy/桌面/产业研究院2024年工作成果汇总/2024年工作成果—殷黎/01 产业研究/12 钛合金中间合金", "max_tokens": 250000, "enable_chart_generation": true, "enable_image_embedding": true, "enable_search_enhancement": true, "search_auto_confirm": true, "predefined_framework": null}, "data": {"framework": {"sections": [{"title": "市场概览与现状分析", "level": 1, "children": [{"title": "市场概览与现状分析_子章节1", "level": 2, "children": [{"title": "市场概览与现状分析_子章节1_子章节1", "level": 3, "children": [], "task_instruction": {"content_requirements": "全面分析相关内容，确保专业性和准确性", "word_count": "3000-3500字"}}, {"title": "市场概览与现状分析_子章节1_子章节2", "level": 3, "children": [], "task_instruction": {"content_requirements": "全面分析相关内容，确保专业性和准确性", "word_count": "3000-3500字"}}], "task_instruction": {"content_requirements": "全面分析相关内容，确保专业性和准确性", "word_count": "3000-3500字"}}, {"title": "市场概览与现状分析_子章节2", "level": 2, "children": [{"title": "市场概览与现状分析_子章节2_子章节1", "level": 3, "children": [], "task_instruction": {"content_requirements": "全面分析相关内容，确保专业性和准确性", "word_count": "3000-3500字"}}, {"title": "市场概览与现状分析_子章节2_子章节2", "level": 3, "children": [], "task_instruction": {"content_requirements": "全面分析相关内容，确保专业性和准确性", "word_count": "3000-3500字"}}], "task_instruction": {"content_requirements": "全面分析相关内容，确保专业性和准确性", "word_count": "3000-3500字"}}], "task_instruction": {"content_requirements": "全面分析相关内容，确保专业性和准确性", "word_count": "3000-3500字"}}, {"title": "技术发展趋势", "level": 1, "children": [{"title": "技术发展趋势_子章节1", "level": 2, "children": [{"title": "技术发展趋势_子章节1_子章节1", "level": 3, "children": [], "task_instruction": {"content_requirements": "全面分析相关内容，确保专业性和准确性", "word_count": "3000-3500字"}}, {"title": "技术发展趋势_子章节1_子章节2", "level": 3, "children": [], "task_instruction": {"content_requirements": "全面分析相关内容，确保专业性和准确性", "word_count": "3000-3500字"}}], "task_instruction": {"content_requirements": "全面分析相关内容，确保专业性和准确性", "word_count": "3000-3500字"}}, {"title": "技术发展趋势_子章节2", "level": 2, "children": [{"title": "技术发展趋势_子章节2_子章节1", "level": 3, "children": [], "task_instruction": {"content_requirements": "全面分析相关内容，确保专业性和准确性", "word_count": "3000-3500字"}}, {"title": "技术发展趋势_子章节2_子章节2", "level": 3, "children": [], "task_instruction": {"content_requirements": "全面分析相关内容，确保专业性和准确性", "word_count": "3000-3500字"}}], "task_instruction": {"content_requirements": "全面分析相关内容，确保专业性和准确性", "word_count": "3000-3500字"}}], "task_instruction": {"content_requirements": "全面分析相关内容，确保专业性和准确性", "word_count": "3000-3500字"}}, {"title": "竞争格局分析", "level": 1, "children": [{"title": "竞争格局分析_子章节1", "level": 2, "children": [{"title": "竞争格局分析_子章节1_子章节1", "level": 3, "children": [], "task_instruction": {"content_requirements": "全面分析相关内容，确保专业性和准确性", "word_count": "3000-3500字"}}, {"title": "竞争格局分析_子章节1_子章节2", "level": 3, "children": [], "task_instruction": {"content_requirements": "全面分析相关内容，确保专业性和准确性", "word_count": "3000-3500字"}}], "task_instruction": {"content_requirements": "全面分析相关内容，确保专业性和准确性", "word_count": "3000-3500字"}}, {"title": "竞争格局分析_子章节2", "level": 2, "children": [{"title": "竞争格局分析_子章节2_子章节1", "level": 3, "children": [], "task_instruction": {"content_requirements": "全面分析相关内容，确保专业性和准确性", "word_count": "3000-3500字"}}, {"title": "竞争格局分析_子章节2_子章节2", "level": 3, "children": [], "task_instruction": {"content_requirements": "全面分析相关内容，确保专业性和准确性", "word_count": "3000-3500字"}}], "task_instruction": {"content_requirements": "全面分析相关内容，确保专业性和准确性", "word_count": "3000-3500字"}}], "task_instruction": {"content_requirements": "全面分析相关内容，确保专业性和准确性", "word_count": "3000-3500字"}}, {"title": "政策环境分析", "level": 1, "children": [{"title": "政策环境分析_子章节1", "level": 2, "children": [{"title": "政策环境分析_子章节1_子章节1", "level": 3, "children": [], "task_instruction": {"content_requirements": "全面分析相关内容，确保专业性和准确性", "word_count": "3000-3500字"}}, {"title": "政策环境分析_子章节1_子章节2", "level": 3, "children": [], "task_instruction": {"content_requirements": "全面分析相关内容，确保专业性和准确性", "word_count": "3000-3500字"}}], "task_instruction": {"content_requirements": "全面分析相关内容，确保专业性和准确性", "word_count": "3000-3500字"}}, {"title": "政策环境分析_子章节2", "level": 2, "children": [{"title": "政策环境分析_子章节2_子章节1", "level": 3, "children": [], "task_instruction": {"content_requirements": "全面分析相关内容，确保专业性和准确性", "word_count": "3000-3500字"}}, {"title": "政策环境分析_子章节2_子章节2", "level": 3, "children": [], "task_instruction": {"content_requirements": "全面分析相关内容，确保专业性和准确性", "word_count": "3000-3500字"}}], "task_instruction": {"content_requirements": "全面分析相关内容，确保专业性和准确性", "word_count": "3000-3500字"}}], "task_instruction": {"content_requirements": "全面分析相关内容，确保专业性和准确性", "word_count": "3000-3500字"}}]}, "topic": "地热发电产业研究报告", "data_sources": ["C:/Users/<USER>/Desktop/深海机器人/地热/报告", "C:/Users/<USER>/Desktop/深海机器人/地热/报告", "C:/Users/<USER>/Desktop/深海机器人/地热/报告", "C:/Users/<USER>/Desktop/深海机器人/地热/报告"], "framework_file_path": "C:/Users/<USER>/Desktop/深海机器人/地热/框架/固态电池产业研究报告框架.md", "total_nodes": 28}, "version": "1.0"}