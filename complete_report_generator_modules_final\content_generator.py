"""
内容生成模块 - 结构化内容生成器和内容清理器
从complete_report_generator.py中提取的完整内容生成相关类
"""
import json
import re
import asyncio
from typing import Dict, Any


class StructuredContentGenerator:
    """结构化内容生成器 - 确保AI输出格式一致性，分离思考过程和最终内容"""

    def __init__(self, api_manager):
        self.api_manager = api_manager

    def generate_structured_content(self, prompt: str, content_type: str = "general") -> dict:
        """生成结构化内容，分离思考过程和最终内容"""

        # 构建结构化prompt
        structured_prompt = self._build_structured_prompt(prompt, content_type)

        # 调用API生成内容
        if hasattr(self.api_manager, 'generate_content_with_model_async'):
            # 异步版本
            import asyncio
            if asyncio.iscoroutinefunction(self.api_manager.generate_content_with_model_async):
                response = asyncio.run(self.api_manager.generate_content_with_model_async(
                    structured_prompt, "gemini-2.5-pro"
                ))
            else:
                response = self.api_manager.generate_content_with_model_async(
                    structured_prompt, "gemini-2.5-pro"
                )
        else:
            # 同步版本
            response = self.api_manager.generate_content_with_model(
                structured_prompt, "gemini-2.5-pro"
            )

        # 处理不同类型的返回值
        if isinstance(response, tuple):
            response = response[0] if response else ""

        # 处理GenerateContentResponse对象
        if hasattr(response, 'text'):
            response = response.text
        elif hasattr(response, 'content'):
            response = response.content

        # 确保response是字符串
        if not isinstance(response, str):
            response = str(response)

        # 解析结构化响应
        return self._parse_structured_response(response)

    def _build_structured_prompt(self, original_prompt: str, content_type: str) -> str:
        """构建结构化prompt，要求AI以JSON格式输出"""

        base_instruction = f"""
请严格按照以下JSON格式输出，将思考过程和最终内容完全分离：

```json
{{
    "thinking_process": {{
        "analysis": "你的分析思考过程",
        "optimization_notes": "优化说明和改进思路",
        "structure_design": "结构设计考虑",
        "style_considerations": "写作风格考虑"
    }},
    "final_content": {{
        "title": "内容标题",
        "content": "最终的纯净内容，不包含任何思考过程或优化说明",
        "metadata": {{
            "word_count": "字数统计",
            "content_type": "{content_type}"
        }}
    }}
}}
```

重要要求：
1. thinking_process部分包含所有分析、优化说明、思考过程
2. final_content部分只包含最终的纯净内容
3. 绝对不要在final_content中包含任何优化说明、思考过程或分析
4. 严格遵循JSON格式，确保可以被程序解析

原始任务：
{original_prompt}
"""

        return base_instruction

    def _parse_structured_response(self, response: str) -> dict:
        """解析结构化响应，提取思考过程和最终内容"""
        import json
        import re

        try:
            # 尝试直接解析JSON
            if response.strip().startswith('{'):
                return json.loads(response)

            # 提取JSON代码块
            json_match = re.search(r'```json\s*\n(.*?)\n```', response, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
                return json.loads(json_str)

            # 提取普通代码块
            code_match = re.search(r'```\s*\n(.*?)\n```', response, re.DOTALL)
            if code_match:
                json_str = code_match.group(1)
                return json.loads(json_str)

            # 如果无法解析JSON，使用智能分离
            return self._intelligent_content_separation(response)

        except json.JSONDecodeError:
            # JSON解析失败，使用智能分离
            return self._intelligent_content_separation(response)

    def _intelligent_content_separation(self, content: str) -> dict:
        """智能分离思考过程和最终内容（备用方案）"""

        # 识别思考过程的关键标识
        thinking_indicators = [
            "优化后的内容", "具体体现在", "重构章节结构", "强化问题导入",
            "内容结构", "写作风格", "格式规范", "专业标准",
            "标题概括性", "引用规范", "善用类比", "语言精炼",
            "图文表结合", "段落清晰", "分析主体", "漏斗式逻辑",
            "我将严格遵循", "原始内容是", "因此我将", "创建符合",
            "优化分析", "这种结构", "通过这种方式", "这种安排"
        ]

        lines = content.split('\n')
        final_content_lines = []
        thinking_process_lines = []

        current_section = "content"  # 默认为内容

        for line in lines:
            line_lower = line.lower()

            # 检查是否包含思考过程标识
            is_thinking = any(indicator in line for indicator in thinking_indicators)

            if is_thinking:
                current_section = "thinking"
                thinking_process_lines.append(line)
            else:
                # 如果是标题或正常内容，归类为最终内容
                if line.strip().startswith('#') or (line.strip() and not is_thinking):
                    current_section = "content"
                    final_content_lines.append(line)
                elif current_section == "thinking":
                    thinking_process_lines.append(line)
                else:
                    final_content_lines.append(line)

        return {
            "thinking_process": {
                "analysis": '\n'.join(thinking_process_lines),
                "optimization_notes": "智能分离提取",
                "structure_design": "自动识别",
                "style_considerations": "基于关键词匹配"
            },
            "final_content": {
                "title": "智能分离内容",
                "content": '\n'.join(final_content_lines).strip(),
                "metadata": {
                    "word_count": len(''.join(final_content_lines)),
                    "content_type": "intelligent_separation"
                }
            }
        }


class RobustContentCleaner:
    """稳健的内容清理器 - 替代脆弱的正则表达式方法"""

    def __init__(self):
        self.structured_generator = None

    def set_structured_generator(self, structured_generator):
        """设置结构化生成器"""
        self.structured_generator = structured_generator

    def clean_content_robustly(self, content: str, use_structured: bool = True) -> str:
        """稳健地清理内容，优先使用结构化方法"""

        if use_structured and self.structured_generator:
            # 使用结构化方法重新生成纯净内容
            return self._restructure_content_cleanly(content)
        else:
            # 使用改进的智能清理方法
            return self._intelligent_clean_content(content)

    def _restructure_content_cleanly(self, content: str) -> str:
        """使用结构化方法重新生成纯净内容"""

        # 提取原始内容的核心信息
        core_info = self._extract_core_information(content)

        # 构建清理prompt
        clean_prompt = f"""
请将以下内容重新整理为纯净的报告内容，完全移除所有思考过程、优化说明和分析：

原始内容：
{content}

要求：
1. 只保留实质性的报告内容
2. 移除所有"优化后的"、"具体体现在"等分析性语言
3. 移除所有标记为思考过程的内容
4. 保持原有的结构和逻辑
5. 确保内容的完整性和连贯性

请直接输出清理后的纯净内容，不要包含任何说明或分析。
"""

        try:
            # 使用结构化生成器生成纯净内容
            structured_result = self.structured_generator.generate_structured_content(
                clean_prompt, "content_cleaning"
            )

            # 返回最终内容
            return structured_result.get("final_content", {}).get("content", content)

        except Exception as e:
            print(f"⚠️ 结构化清理失败，使用备用方法: {str(e)}")
            return self._intelligent_clean_content(content)

    def _extract_core_information(self, content: str) -> dict:
        """提取内容的核心信息"""

        lines = content.split('\n')

        # 提取标题
        titles = [line for line in lines if line.strip().startswith('#')]

        # 提取段落（排除明显的思考过程）
        paragraphs = []
        for line in lines:
            if line.strip() and not line.strip().startswith('#'):
                # 简单过滤明显的思考过程
                if not any(keyword in line.lower() for keyword in [
                    "优化后的", "具体体现", "重构章节", "写作风格", "格式规范"
                ]):
                    paragraphs.append(line)

        return {
            "titles": titles,
            "paragraphs": paragraphs,
            "total_lines": len(lines)
        }

    def _intelligent_clean_content(self, content: str) -> str:
        """改进的智能内容清理方法"""

        # 基于语义而非正则表达式的清理
        lines = content.split('\n')
        cleaned_lines = []

        skip_next_lines = 0

        for i, line in enumerate(lines):
            if skip_next_lines > 0:
                skip_next_lines -= 1
                continue

            line_stripped = line.strip()

            # 跳过空行
            if not line_stripped:
                cleaned_lines.append(line)
                continue

            # 保留标题
            if line_stripped.startswith('#'):
                # 检查是否是优化相关的标题
                if any(keyword in line_stripped.lower() for keyword in [
                    "优化后的", "优化分析", "优化记录", "质量评分", "生成信息", "审核记录"
                ]):
                    # 跳过这个标题及其后续内容直到下一个同级或更高级标题
                    skip_next_lines = self._count_lines_to_skip(lines, i)
                    continue
                else:
                    cleaned_lines.append(line)
                    continue

            # 检查是否是思考过程内容
            if self._is_thinking_process_line(line_stripped):
                continue

            # 保留其他内容
            cleaned_lines.append(line)

        return '\n'.join(cleaned_lines).strip()

    def _count_lines_to_skip(self, lines: list, start_index: int) -> int:
        """计算需要跳过的行数（直到下一个同级或更高级标题）"""

        if start_index >= len(lines):
            return 0

        start_line = lines[start_index].strip()
        start_level = len(start_line) - len(start_line.lstrip('#'))

        skip_count = 0
        for i in range(start_index + 1, len(lines)):
            line = lines[i].strip()

            if line.startswith('#'):
                current_level = len(line) - len(line.lstrip('#'))
                if current_level <= start_level:
                    break

            skip_count += 1

        return skip_count

    def _is_thinking_process_line(self, line: str) -> bool:
        """判断是否是思考过程行（基于语义特征）"""

        # 思考过程的语义特征
        thinking_features = [
            # 优化相关
            ("优化", ["后的", "说明", "分析", "记录"]),
            ("具体", ["体现在", "表现为"]),
            ("重构", ["章节", "结构"]),

            # 分析相关
            ("分析", ["主体", "过程", "思路"]),
            ("考虑", ["因素", "方面"]),
            ("设计", ["思路", "理念"]),

            # 风格相关
            ("写作", ["风格", "特点"]),
            ("格式", ["规范", "要求"]),
            ("专业", ["标准", "要求"]),

            # 元描述
            ("这种", ["结构", "方式", "安排"]),
            ("通过", ["这种", "上述"]),
            ("从而", ["凸显", "增强"]),
        ]

        line_lower = line.lower()

        for main_keyword, sub_keywords in thinking_features:
            if main_keyword in line_lower:
                if any(sub_keyword in line_lower for sub_keyword in sub_keywords):
                    return True

        return False

    def _intelligent_content_separation(self, content: str) -> dict:
        """智能分离思考过程和最终内容（备用方案）"""

        # 识别思考过程的关键标识
        thinking_indicators = [
            "优化后的内容", "具体体现在", "重构章节结构", "强化问题导入",
            "内容结构", "写作风格", "格式规范", "专业标准",
            "标题概括性", "引用规范", "善用类比", "语言精炼",
            "图文表结合", "段落清晰", "分析主体", "漏斗式逻辑",
            "我将严格遵循", "原始内容是", "因此我将", "创建符合",
            "优化分析", "这种结构", "通过这种方式", "这种安排"
        ]

        lines = content.split('\n')
        final_content_lines = []
        thinking_process_lines = []

        current_section = "content"  # 默认为内容

        for line in lines:
            # 检查是否包含思考过程标识
            is_thinking = any(indicator in line for indicator in thinking_indicators)

            if is_thinking:
                current_section = "thinking"
                thinking_process_lines.append(line)
            else:
                # 如果是标题或正常内容，归类为最终内容
                if line.strip().startswith('#') or (line.strip() and not is_thinking):
                    current_section = "content"
                    final_content_lines.append(line)
                elif current_section == "thinking":
                    thinking_process_lines.append(line)
                else:
                    final_content_lines.append(line)

        return {
            "thinking_process": {
                "analysis": '\n'.join(thinking_process_lines),
                "optimization_notes": "智能分离提取",
                "structure_design": "自动识别",
                "style_considerations": "基于关键词匹配"
            },
            "final_content": {
                "title": "智能分离内容",
                "content": '\n'.join(final_content_lines).strip(),
                "metadata": {
                    "word_count": len(''.join(final_content_lines)),
                    "content_type": "intelligent_separation"
                }
            }
        }