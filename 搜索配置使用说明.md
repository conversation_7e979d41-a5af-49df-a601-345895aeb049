# 联网搜索配置使用说明

## 概述

AI报告生成器现在支持联网搜索功能的用户确认机制。默认情况下，系统会在需要进行联网搜索时询问用户是否同意，用户可以根据需要修改为自动搜索模式。

## 默认行为

- **默认设置**: 需要用户确认
- **搜索时机**: 报告生成完成后，系统检测到可以通过搜索增强的内容时
- **用户体验**: 系统会显示搜索提示，等待用户输入 `y/n` 来确认是否进行搜索

## 配置方法

### 1. 查看当前搜索配置

```python
from complete_report_generator import CompleteReportGenerator

# 创建生成器实例
generator = CompleteReportGenerator()

# 查看当前搜索配置
generator.print_search_config()
```

输出示例：
```
🔍 当前搜索配置:
========================================
搜索增强启用: 是
自动确认搜索: 否
💡 提示: 使用 set_search_auto_confirm(True) 可启用自动搜索
========================================
```

### 2. 启用自动搜索模式

```python
# 启用自动搜索（跳过用户确认）
generator.set_search_auto_confirm(True)
```

### 3. 恢复用户确认模式

```python
# 恢复需要用户确认
generator.set_search_auto_confirm(False)
```

### 4. 获取配置详情

```python
# 获取详细配置信息
config = generator.get_search_config()
print(config)
# 输出: {'enable_search_enhancement': True, 'search_auto_confirm': False}
```

## 完整使用示例

```python
from complete_report_generator import CompleteReportGenerator

def main():
    # 创建生成器
    generator = CompleteReportGenerator()
    
    # 方案1: 使用默认设置（需要用户确认）
    print("=== 方案1: 默认设置 ===")
    generator.print_search_config()
    
    # 方案2: 启用自动搜索
    print("\n=== 方案2: 自动搜索 ===")
    generator.set_search_auto_confirm(True)
    generator.print_search_config()
    
    # 生成报告（搜索将自动进行，无需用户确认）
    output_path = generator.generate_report(
        topic="地热发电产业研究",
        data_sources=["data_sources"],
        framework_file_path="frameworks/default_framework.txt"
    )
    
    print(f"报告生成完成: {output_path}")

if __name__ == "__main__":
    main()
```

## 搜索流程说明

### 用户确认模式（默认）
1. 报告生成完成
2. 系统分析报告内容，识别可以通过搜索增强的部分
3. 显示搜索提示信息
4. **等待用户输入**: `y/yes/是` 确认搜索，`n/no/否` 跳过搜索
5. 根据用户选择执行或跳过搜索

### 自动搜索模式
1. 报告生成完成
2. 系统分析报告内容，识别可以通过搜索增强的部分
3. **自动开始搜索**，无需用户确认
4. 搜索完成后自动整合到报告中

## 注意事项

1. **网络要求**: 联网搜索需要稳定的网络连接
2. **API配额**: 搜索功能会消耗额外的API调用次数
3. **时间成本**: 搜索和内容整合需要额外时间
4. **质量提升**: 搜索功能可以显著提升报告的时效性和准确性

## 推荐使用场景

### 建议启用自动搜索的场景：
- 批量生成报告
- 对最新信息要求较高的报告
- 无人值守的自动化流程

### 建议保持用户确认的场景：
- 首次使用系统
- 对搜索内容有特殊要求
- 需要控制API使用量的场景

## 故障排除

如果遇到搜索相关问题：

1. **检查网络连接**
2. **验证API密钥状态**
3. **查看搜索配置**: `generator.print_search_config()`
4. **测试搜索功能**: 运行 `test_search_config.py`

## 更新日志

- **v1.0**: 实现联网搜索功能
- **v1.1**: 添加用户确认机制，默认需要用户确认
- **v1.2**: 提供配置管理接口，支持自动搜索模式切换
