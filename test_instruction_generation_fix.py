#!/usr/bin/env python3
"""
测试任务指导生成修复
"""

import sys
import os
import asyncio
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 强制重新加载模块
import importlib
if 'complete_report_generator' in sys.modules:
    importlib.reload(sys.modules['complete_report_generator'])

from complete_report_generator import CompleteReportGenerator

def create_test_sections():
    """创建测试用的章节结构"""
    
    sections = [
        {
            "title": "第一章：技术发展现状",
            "level": 1,
            "children": [
                {
                    "title": "1.1 技术概述",
                    "level": 2,
                    "children": [
                        {"title": "1.1.1 基础技术", "level": 3, "children": []},
                        {"title": "1.1.2 核心技术", "level": 3, "children": []}
                    ]
                },
                {
                    "title": "1.2 发展历程",
                    "level": 2,
                    "children": [
                        {"title": "1.2.1 早期发展", "level": 3, "children": []},
                        {"title": "1.2.2 现代发展", "level": 3, "children": []}
                    ]
                }
            ]
        },
        {
            "title": "第二章：市场分析",
            "level": 1,
            "children": [
                {
                    "title": "2.1 市场规模",
                    "level": 2,
                    "children": [
                        {"title": "2.1.1 全球市场", "level": 3, "children": []},
                        {"title": "2.1.2 中国市场", "level": 3, "children": []}
                    ]
                },
                {
                    "title": "2.2 竞争格局",
                    "level": 2,
                    "children": [
                        {"title": "2.2.1 主要厂商", "level": 3, "children": []},
                        {"title": "2.2.2 市场份额", "level": 3, "children": []}
                    ]
                }
            ]
        },
        {
            "title": "第三章：未来展望",
            "level": 1,
            "children": [
                {
                    "title": "3.1 发展趋势",
                    "level": 2,
                    "children": [
                        {"title": "3.1.1 技术趋势", "level": 3, "children": []},
                        {"title": "3.1.2 市场趋势", "level": 3, "children": []}
                    ]
                }
            ]
        }
    ]
    
    return sections

def count_all_nodes(sections):
    """计算所有节点的数量"""
    total = 0
    
    def count_recursive(node):
        nonlocal total
        total += 1
        if "children" in node and node["children"]:
            for child in node["children"]:
                count_recursive(child)
    
    for section in sections:
        count_recursive(section)
    
    return total

async def test_instruction_generation():
    """测试任务指导生成"""
    
    print("🎯 测试任务指导生成修复")
    print("=" * 80)
    
    # 创建测试数据
    sections = create_test_sections()
    data_sources = [
        "技术发展报告",
        "市场研究报告", 
        "行业分析报告",
        "政策文件",
        "专家访谈",
        "公开资料"
    ]
    
    total_nodes = count_all_nodes(sections)
    print(f"📊 测试数据:")
    print(f"   章节数: {len(sections)}")
    print(f"   总节点数: {total_nodes}")
    
    # 显示节点结构
    print(f"\n📋 节点结构:")
    node_counter = 1
    for section in sections:
        def print_structure(node, indent=0):
            nonlocal node_counter
            prefix = "  " * indent
            title = node.get("title", "无标题")
            level = node.get("level", 1)
            print(f"{prefix}节点{node_counter:03d} - 第{level}级: {title}")
            node_counter += 1
            
            if "children" in node and node["children"]:
                for child in node["children"]:
                    print_structure(child, indent + 1)
        
        print_structure(section)
    
    print(f"\n🔧 开始测试任务指导生成...")
    
    try:
        # 创建生成器实例
        generator = CompleteReportGenerator()
        
        # 测试任务指导生成
        await generator._generate_task_instructions_async(sections, data_sources)
        
        # 验证结果
        nodes_with_instructions = 0
        nodes_without_instructions = []
        
        def check_instructions(node, node_id):
            nonlocal nodes_with_instructions
            if "task_instruction" in node and node["task_instruction"]:
                nodes_with_instructions += 1
                instruction = node["task_instruction"]
                content_req = instruction.get("content_requirements", "")
                word_count = instruction.get("word_count", "")
                print(f"   ✅ {node_id}: {node.get('title', '无标题')}")
                print(f"      内容要求: {content_req[:50]}...")
                print(f"      字数要求: {word_count}")
            else:
                nodes_without_instructions.append(node_id)
                print(f"   ❌ {node_id}: {node.get('title', '无标题')} - 缺少指导")
        
        # 检查所有节点
        print(f"\n📊 指导应用结果:")
        node_counter = 1
        for section in sections:
            def check_recursive(node):
                nonlocal node_counter
                node_id = f"节点{node_counter:03d}"
                check_instructions(node, node_id)
                node_counter += 1
                
                if "children" in node and node["children"]:
                    for child in node["children"]:
                        check_recursive(child)
            
            check_recursive(section)
        
        # 统计结果
        success_rate = nodes_with_instructions / total_nodes * 100
        
        print(f"\n🎉 测试结果:")
        print(f"   预期节点数: {total_nodes}")
        print(f"   成功指导数: {nodes_with_instructions}")
        print(f"   成功率: {success_rate:.1f}%")
        
        if success_rate == 100:
            print(f"   ✅ 完美！所有节点都有任务指导")
        elif success_rate >= 90:
            print(f"   ✅ 优秀！大部分节点都有任务指导")
        elif success_rate >= 70:
            print(f"   ⚠️ 良好，但还有改进空间")
        else:
            print(f"   ❌ 需要进一步修复")
        
        if nodes_without_instructions:
            print(f"   📝 缺少指导的节点: {nodes_without_instructions}")
        
        return success_rate == 100
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    
    print("🚀 开始任务指导生成修复测试")
    print("时间:", __import__('time').strftime("%Y-%m-%d %H:%M:%S"))
    print()
    
    success = await test_instruction_generation()
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 测试成功！任务指导生成修复完成")
        print("📋 现在每个节点都应该有对应的任务指导")
        print("📋 可以重新运行报告生成，应该不会再出现指导数量不足的问题")
    else:
        print("❌ 测试失败，需要进一步检查和修复")
    
    print("🚀 测试完成！")

if __name__ == "__main__":
    asyncio.run(main())
