#!/usr/bin/env python3
"""
全面的JSON修复测试
测试所有可能的JSON格式问题
"""

import sys
import os
import time
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 强制重新加载模块
import importlib
if 'complete_report_generator' in sys.modules:
    importlib.reload(sys.modules['complete_report_generator'])

from complete_report_generator import safe_json_loads, fix_empty_string_values, SafetyFilterException
import json

def test_all_json_problems():
    """测试所有已知的JSON问题"""
    
    print("🎯 全面JSON修复测试")
    print("=" * 80)
    
    test_cases = [
        {
            "name": "控制字符问题",
            "json": '''{
    "instructions": {
        "节点001": {
            "content_requirements": "全面分析地热发电技术的发展现状
重点关注以下几个方面：
1. 技术原理与分类
2. 全球发展趋势	
3. 主要技术挑战",
            "word_count": "2000-3000字"
        }
    }
}''',
            "expected_error": "Invalid control character"
        },
        {
            "name": "空值格式问题",
            "json": '''{
    "instructions": {
        "节点001": {
            "title": "技术发展趋势",
            "content_requirements": "
            "word_count": "30-50字"
        }
    }
}''',
            "expected_error": "Invalid control character"
        },
        {
            "name": "逗号缺失问题",
            "json": '''{
    "instructions": {
        "节点001": {
            "title": "技术发展趋势",
            "content_requirements": "",
            "word_count": "30-50字"
        },
        "节点002": {
            "title": "市场分析",
            "content_requirements": "",
            "word_count": "50-80字"
        }
    }
}''',
            "expected_error": "Expecting ',' delimiter"
        },
        {
            "name": "复杂嵌套问题",
            "json": '''{
    "instructions": {
        "节点001": {
            "title": "技术发展趋势",
            "content_requirements": "
            "word_count": "30-50字"
        },
        "节点002": {
            "title": "市场分析",
            "content_requirements": "",
            "word_count": "50-80字"
        },
        "节点003": {
            "title": "政策环境",
            "content_requirements": "",
            "word_count": "80-100字"
        }
    }
}''',
            "expected_error": "Multiple issues"
        },
        {
            "name": "Markdown代码块包装",
            "json": '''```json
{
    "instructions": {
        "节点001": {
            "title": "技术发展趋势",
            "content_requirements": "",
            "word_count": "30-50字"
        }
    }
}
```''',
            "expected_error": "None"
        }
    ]
    
    success_count = 0
    total_count = len(test_cases)
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n🔍 测试用例 {i}: {case['name']}")
        print("-" * 60)
        
        # 标准解析测试
        print("❌ 标准json.loads解析:")
        try:
            result = json.loads(case['json'])
            print(f"✅ 标准解析成功: {len(result.get('instructions', {}))} 个指导")
            standard_success = True
        except json.JSONDecodeError as e:
            print(f"💥 标准解析失败: {e}")
            print(f"   错误位置: 行{e.lineno}, 列{e.colno}")
            standard_success = False
        
        # 修复解析测试
        print("\n✅ safe_json_loads解析:")
        try:
            result = safe_json_loads(case['json'])
            if result and isinstance(result, dict):
                instructions = result.get("instructions", {})
                print(f"🎯 修复解析成功!")
                print(f"   📊 指导数量: {len(instructions)}")
                
                # 显示解析结果
                for node_id, instruction in list(instructions.items())[:3]:  # 只显示前3个
                    title = instruction.get("title", "无标题")
                    word_count = instruction.get("word_count", "")
                    content_req = instruction.get("content_requirements", "")
                    print(f"   📋 {node_id}: {title}")
                    print(f"      字数: {word_count}")
                    print(f"      要求: {content_req[:30]}{'...' if len(content_req) > 30 else ''}")
                
                if len(instructions) > 3:
                    print(f"   ... 还有 {len(instructions) - 3} 个指导")
                
                success_count += 1
                print(f"   ✅ 测试通过")
            else:
                print(f"❌ 修复解析失败，返回: {type(result)}")
        except Exception as e:
            print(f"❌ 修复解析异常: {e}")
    
    print(f"\n🎉 测试完成!")
    print(f"📊 成功率: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
    
    if success_count == total_count:
        print("✅ 所有测试用例都通过了！JSON修复功能正常工作")
    else:
        print("⚠️ 部分测试用例失败，需要进一步检查")

def test_fix_empty_string_values_function():
    """专门测试fix_empty_string_values函数"""
    
    print("\n🔧 测试fix_empty_string_values函数")
    print("=" * 80)
    
    test_cases = [
        {
            "name": "基本空值问题",
            "input": '"content_requirements": "\n            "word_count":',
            "expected": '"content_requirements": "",\n            "word_count":'
        },
        {
            "name": "复杂空值问题",
            "input": '''{
    "field1": "
    "field2": "value"
}''',
            "expected_contains": '"field1": "",'
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n🔍 测试 {i}: {case['name']}")
        print(f"输入: {repr(case['input'])}")
        
        result = fix_empty_string_values(case['input'])
        print(f"输出: {repr(result)}")
        
        if 'expected' in case:
            if case['expected'] in result:
                print("✅ 修复正确")
            else:
                print("❌ 修复不正确")
        elif 'expected_contains' in case:
            if case['expected_contains'] in result:
                print("✅ 修复正确")
            else:
                print("❌ 修复不正确")

def test_safety_filter_exception():
    """测试安全过滤器异常"""
    
    print("\n🛡️ 测试安全过滤器异常")
    print("=" * 80)
    
    try:
        raise SafetyFilterException("测试安全过滤器异常")
    except SafetyFilterException as e:
        print(f"✅ SafetyFilterException正常工作: {e}")
    except Exception as e:
        print(f"❌ SafetyFilterException异常: {e}")

def test_real_world_scenario():
    """测试真实世界场景"""
    
    print("\n🌍 测试真实世界场景")
    print("=" * 80)
    
    # 模拟实际API响应
    real_response = '''```json
{
    "instructions": {
        "节点001": {
            "title": "1. 总论：核电产业概览与研究界定",
            "content_requirements": "
            "word_count": "80-100字"
        },
        "节点024": {
            "title": "2. 核电技术发展现状",
            "content_requirements": "详细分析核电技术发展现状，包括技术路线、发展水平、主要厂商等",
            "word_count": "100-150字"
        },
        "节点045": {
            "title": "3. 市场规模与前景分析",
            "content_requirements": "",
            "word_count": "150-200字"
        }
    }
}
```'''
    
    print("📝 真实API响应模拟:")
    print(f"长度: {len(real_response)} 字符")
    print(f"前100字符: {repr(real_response[:100])}...")
    
    try:
        result = safe_json_loads(real_response)
        if result:
            instructions = result.get("instructions", {})
            print(f"✅ 解析成功!")
            print(f"   📊 指导数量: {len(instructions)}")
            
            for node_id, instruction in instructions.items():
                title = instruction.get("title", "无标题")
                word_count = instruction.get("word_count", "")
                print(f"   📋 {node_id}: {title[:50]}... ({word_count})")
        else:
            print("❌ 解析失败")
    except Exception as e:
        print(f"❌ 解析异常: {e}")

if __name__ == "__main__":
    print("🚀 开始全面JSON修复测试")
    print("时间:", time.strftime("%Y-%m-%d %H:%M:%S"))
    print()
    
    test_all_json_problems()
    test_fix_empty_string_values_function()
    test_safety_filter_exception()
    test_real_world_scenario()
    
    print("\n" + "=" * 80)
    print("🎉 全面测试完成!")
    print("📋 如果所有测试都通过，说明JSON修复功能已经完全正常")
    print("📋 现在可以重新运行报告生成，应该不会再出现JSON解析问题")
