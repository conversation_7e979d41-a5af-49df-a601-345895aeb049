"""
工具函数模块 - 包含各种辅助函数和工具类
"""
import os
import json
import time
from pathlib import Path
from typing import Dict, Any, List, Optional


def manage_checkpoints():
    """管理checkpoint的命令行工具"""
    print("📂 Checkpoint管理工具")
    print("=" * 40)
    
    checkpoint_dir = Path("checkpoints")
    if not checkpoint_dir.exists():
        print("❌ 未找到checkpoints目录")
        return
    
    # 列出所有checkpoint
    checkpoints = []
    for checkpoint_file in checkpoint_dir.glob("*.json"):
        try:
            with open(checkpoint_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            checkpoints.append({
                "id": data.get("checkpoint_id", checkpoint_file.stem),
                "stage": data.get("stage", "未知"),
                "created_time": data.get("created_time", "未知"),
                "file": str(checkpoint_file)
            })
        except:
            continue
    
    if not checkpoints:
        print("📭 未找到任何checkpoint文件")
        return
    
    # 按时间排序
    checkpoints.sort(key=lambda x: x["created_time"], reverse=True)
    
    print(f"📋 找到 {len(checkpoints)} 个checkpoint:")
    print()
    
    for i, checkpoint in enumerate(checkpoints, 1):
        print(f"{i}. {checkpoint['id']}")
        print(f"   阶段: {checkpoint['stage']}")
        print(f"   时间: {checkpoint['created_time']}")
        print(f"   文件: {checkpoint['file']}")
        print()


def format_file_size(size_bytes: int) -> str:
    """格式化文件大小"""
    if size_bytes == 0:
        return "0B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f}{size_names[i]}"


def ensure_directory(path: Path) -> bool:
    """确保目录存在"""
    try:
        path.mkdir(parents=True, exist_ok=True)
        return True
    except Exception as e:
        print(f"❌ 创建目录失败 {path}: {str(e)}")
        return False


def safe_filename(filename: str) -> str:
    """生成安全的文件名"""
    import re
    
    # 移除或替换不安全的字符
    safe_name = re.sub(r'[<>:"/\\|?*]', '_', filename)
    
    # 限制长度
    if len(safe_name) > 200:
        safe_name = safe_name[:200]
    
    return safe_name


def load_json_file(file_path: Path) -> Dict[str, Any]:
    """安全地加载JSON文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"❌ 文件不存在: {file_path}")
        return {}
    except json.JSONDecodeError as e:
        print(f"❌ JSON格式错误: {file_path} - {str(e)}")
        return {}
    except Exception as e:
        print(f"❌ 读取文件失败: {file_path} - {str(e)}")
        return {}


def save_json_file(data: Dict[str, Any], file_path: Path) -> bool:
    """安全地保存JSON文件"""
    try:
        # 确保目录存在
        ensure_directory(file_path.parent)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print(f"❌ 保存文件失败: {file_path} - {str(e)}")
        return False


def get_timestamp() -> str:
    """获取当前时间戳字符串"""
    return time.strftime("%Y%m%d_%H%M%S")


def get_readable_timestamp() -> str:
    """获取可读的时间戳字符串"""
    return time.strftime("%Y-%m-%d %H:%M:%S")


class ProgressTracker:
    """进度跟踪器"""
    
    def __init__(self, total_steps: int, description: str = "处理中"):
        self.total_steps = total_steps
        self.current_step = 0
        self.description = description
        self.start_time = time.time()
    
    def update(self, step: int = None, description: str = None):
        """更新进度"""
        if step is not None:
            self.current_step = step
        else:
            self.current_step += 1
        
        if description:
            self.description = description
        
        # 计算进度百分比
        progress = (self.current_step / self.total_steps) * 100
        
        # 计算预估剩余时间
        elapsed_time = time.time() - self.start_time
        if self.current_step > 0:
            estimated_total_time = elapsed_time * (self.total_steps / self.current_step)
            remaining_time = estimated_total_time - elapsed_time
        else:
            remaining_time = 0
        
        # 格式化输出
        print(f"\r📊 {self.description}: {self.current_step}/{self.total_steps} "
              f"({progress:.1f}%) - 预计剩余: {remaining_time:.0f}秒", end="", flush=True)
        
        if self.current_step >= self.total_steps:
            print()  # 换行
    
    def finish(self, message: str = "完成"):
        """完成进度跟踪"""
        elapsed_time = time.time() - self.start_time
        print(f"\n✅ {message} - 总耗时: {elapsed_time:.1f}秒")


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "config.json"):
        self.config_file = Path(config_file)
        self.config = self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """加载配置"""
        if self.config_file.exists():
            return load_json_file(self.config_file)
        else:
            # 返回默认配置
            return {
                "report": {
                    "max_depth": 6,
                    "primary_sections": 8,
                    "target_words": 50000,
                    "output_dir": "output"
                },
                "api": {
                    "max_tokens": 250000,
                    "use_async": True
                },
                "features": {
                    "enable_chart_generation": True,
                    "enable_image_embedding": True,
                    "enable_search_enhancement": True
                }
            }
    
    def save_config(self) -> bool:
        """保存配置"""
        return save_json_file(self.config, self.config_file)
    
    def get(self, key: str, default=None):
        """获取配置值"""
        keys = key.split('.')
        value = self.config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value):
        """设置配置值"""
        keys = key.split('.')
        config = self.config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def update(self, updates: Dict[str, Any]):
        """批量更新配置"""
        for key, value in updates.items():
            self.set(key, value)