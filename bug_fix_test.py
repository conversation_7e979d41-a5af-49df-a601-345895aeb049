#!/usr/bin/env python3
"""
BUG修复验证测试
"""

print("🔧 开始BUG修复验证...")

try:
    from complete_report_generator import CompleteReportGenerator
    print("✅ 导入成功")
    
    generator = CompleteReportGenerator(use_async=False)
    print("✅ 初始化成功")
    
    # 检查配置
    auto_confirm = generator.report_config.get("search_auto_confirm", False)
    print(f"✅ 自动搜索确认: {auto_confirm}")
    
    # 测试搜索功能
    print("🔍 测试搜索功能...")
    search_works = generator.test_search_functionality()
    
    if search_works:
        print("✅ 搜索功能正常")
        
        # 创建测试报告
        test_content = "# AI报告\n\n## 概述\nAI技术发展。"
        with open("bug_test.md", 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        print("🔍 测试搜索增强...")
        enhanced_path = generator.enhance_report_with_tool_calling(
            "bug_test.md", 
            "AI技术", 
            user_confirm=False
        )
        
        if enhanced_path != "bug_test.md":
            print(f"✅ 搜索增强成功: {enhanced_path}")
            print("🎉 BUG修复验证通过！")
        else:
            print("⚠️ 搜索增强未执行")
            
        # 清理
        import os
        for f in ["bug_test.md", enhanced_path]:
            if f and os.path.exists(f):
                os.remove(f)
    else:
        print("❌ 搜索功能异常")
        
except Exception as e:
    print(f"❌ 测试失败: {str(e)}")
    import traceback
    traceback.print_exc()

print("测试完成")
