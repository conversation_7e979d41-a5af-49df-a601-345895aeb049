"""
工具函数模块 - 包含所有工具函数和主程序入口
从complete_report_generator.py中提取的完整工具函数
"""
import os
import sys
import math
from pathlib import Path
from typing import List, Dict, Any
from datetime import datetime

# 导入报告生成器
from report_generator import CompleteReportGenerator
from api_manager import AsyncConfig


def create_directories():
    """创建必要的目录"""
    directories = ["data", "templates", "output", "logs"]
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)


def create_framework_file(primary_sections=8):
    """创建框架文件（支持动态数量的一级标题）"""
    # 简化的框架创建逻辑
    framework_content = "# 产业研究报告框架模板\n\n"
    
    for i in range(primary_sections):
        section_num = i + 1
        framework_content += f"## {section_num}. 第{section_num}章\n"
        framework_content += f"### {section_num}.1 子章节\n\n"

    framework_path = Path("templates/report_framework.md")
    framework_path.parent.mkdir(parents=True, exist_ok=True)

    with open(framework_path, 'w', encoding='utf-8') as f:
        f.write(framework_content)

    print(f"✅ 创建框架文件: {framework_path} ({primary_sections}个一级标题)")
    return str(framework_path)


def create_sample_data():
    """创建示例数据"""
    sample_data = {
        "data/1_market_overview/analysis.txt": "市场概览示例数据",
        "data/2_technology_trends/trends.txt": "技术趋势示例数据",
        "data/3_competitive_landscape/competition.txt": "竞争格局示例数据"
    }

    for file_path, content in sample_data.items():
        Path(file_path).parent.mkdir(parents=True, exist_ok=True)
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)


def get_user_inputs():
    """获取用户输入的路径配置（支持动态配置）"""
    print("🔧 配置报告生成参数")
    
    # 简化的用户输入
    topic = input("请输入报告主题 [默认: 固态电池产业研究报告]: ").strip() or "固态电池产业研究报告"
    
    primary_sections = 8
    max_depth = 6
    target_words = 50000
    max_tokens = 250000
    
    framework_path = create_framework_file(primary_sections)
    
    data_sources = [
        f"data/{i+1}_section_{i+1}" for i in range(primary_sections)
    ]
    
    reference_reports_path = ""
    predefined_framework = None
    
    return topic, framework_path, data_sources, primary_sections, max_depth, target_words, reference_reports_path, predefined_framework, max_tokens


def create_topic_file_template():
    """创建主题文件模板"""
    topic_dir = Path("inputs")
    topic_dir.mkdir(exist_ok=True)

    topic_file = topic_dir / "topic.txt"
    if not topic_file.exists():
        with open(topic_file, 'w', encoding='utf-8') as f:
            f.write("固态电池产业研究报告")
        print(f"✅ 创建主题文件模板: {topic_file}")


def create_custom_framework_template():
    """创建自定义框架文件模板"""
    framework_dir = Path("templates")
    framework_dir.mkdir(exist_ok=True)

    custom_framework_file = framework_dir / "custom_framework.md"
    if not custom_framework_file.exists():
        framework_content = """# 自定义产业研究报告框架模板

## 1. 行业概述与市场分析
### 1.1 行业定义与分类

## 2. 技术发展现状
### 2.1 核心技术分析

## 3. 市场竞争格局
### 3.1 竞争主体分析

## 4. 政策法规环境
### 4.1 政策体系
"""
        with open(custom_framework_file, 'w', encoding='utf-8') as f:
            f.write(framework_content)
        print(f"✅ 创建自定义框架文件模板: {custom_framework_file}")


def validate_data_sources(data_sources: List[str]) -> List[str]:
    """验证并创建缺失的数据源"""
    validated_sources = []

    for i, source_path in enumerate(data_sources):
        source_dir = Path(source_path)

        if not source_dir.exists():
            print(f"⚠️ 数据源不存在: {source_path}，创建示例数据")
            source_dir.mkdir(parents=True, exist_ok=True)

            # 创建示例文件
            sample_file = source_dir / "sample_data.txt"
            sample_content = f"第{i+1}章节示例数据\n\n这是第{i+1}个章节的示例数据内容。"
            
            with open(sample_file, 'w', encoding='utf-8') as f:
                f.write(sample_content)

        validated_sources.append(source_path)

    return validated_sources


def main():
    """主函数"""
    print("🤖 完整版AI报告生成器")
    print("=" * 60)
    
    try:
        # 创建基础目录和模板
        create_directories()
        create_topic_file_template()
        create_custom_framework_template()

        # 获取用户输入的配置
        topic, framework_path, data_sources, primary_sections, max_depth, target_words, reference_reports_path, predefined_framework, max_tokens = get_user_inputs()

        # 验证和准备数据源
        data_sources = validate_data_sources(data_sources)

        # 如果使用默认配置，创建示例数据
        if not any(Path(source).exists() for source in data_sources):
            print("📁 创建示例数据...")
            create_sample_data()

        print("\n✅ 环境准备完成")

        # 选择运行模式
        print(f"\n🔧 选择运行模式:")
        print(f"   1. 异步并行模式（推荐）- 速度快，结果相同")
        print(f"   2. 同步顺序模式 - 传统模式，稳定可靠")

        mode_choice = input(f"请选择模式 (1/2) [默认: 1]: ").strip() or "1"
        use_async = mode_choice == "1"

        # 确认生成
        confirm = input(f"\n🚀 确认开始生成报告? (y/n) [默认: y]: ").strip().lower() or "y"
        if confirm != "y":
            print("❌ 用户取消生成")
            return

        # 生成报告
        generator = CompleteReportGenerator(use_async=use_async, max_tokens=max_tokens)

        # 配置动态参数
        generator.configure_report(
            title=topic,
            primary_sections=primary_sections,
            max_depth=max_depth,
            target_words=target_words,
            max_tokens=max_tokens
        )

        output_path = generator.generate_report(
            topic=topic,
            data_sources=data_sources,
            framework_file=framework_path
        )

        print(f"\n🎉 报告生成成功!")
        print(f"📄 输出文件: {output_path}")
        print(f"📂 文件位置: {Path(output_path).absolute()}")

    except Exception as e:
        print(f"\n❌ 报告生成失败: {str(e)}")
        import traceback
        traceback.print_exc()


def manage_checkpoints():
    """Checkpoint管理工具"""
    print("📂 Checkpoint管理工具")
    print("=" * 50)

    generator = CompleteReportGenerator(use_async=False)
    checkpoints = generator.list_checkpoints()

    if not checkpoints:
        print("❌ 没有找到任何checkpoint")
        return

    print(f"📋 找到 {len(checkpoints)} 个checkpoint:")
    
    for i, checkpoint in enumerate(checkpoints):
        print(f"{i+1:2d}. {checkpoint['id']}")
        print(f"    时间: {checkpoint['timestamp']}")


if __name__ == "__main__":
    main()