#!/usr/bin/env python3
"""
测试联网搜索控制接口
验证单次任务的搜索开关功能
"""

def test_search_control_interface():
    """测试搜索控制接口"""
    print("🔍 测试联网搜索控制接口")
    print("=" * 60)
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        generator = CompleteReportGenerator(use_async=False)
        print("✅ 生成器创建成功")
        
        # 显示当前全局配置
        print("\n📋 当前全局搜索配置:")
        generator.print_search_config()
        
        # 测试1: 检查generate_report方法的新参数
        print("\n🧪 测试1: 检查方法签名")
        import inspect
        sig = inspect.signature(generator.generate_report)
        params = list(sig.parameters.keys())
        
        expected_params = ['topic', 'data_sources', 'framework_file_path', 'resume_checkpoint', 'enable_search', 'search_auto_confirm']
        
        print(f"   方法参数: {params}")
        
        for param in ['enable_search', 'search_auto_confirm']:
            if param in params:
                print(f"   ✅ {param} 参数存在")
            else:
                print(f"   ❌ {param} 参数缺失")
        
        # 测试2: 测试参数默认值
        print(f"\n🧪 测试2: 检查参数默认值")
        enable_search_param = sig.parameters.get('enable_search')
        search_auto_confirm_param = sig.parameters.get('search_auto_confirm')
        
        if enable_search_param and enable_search_param.default is None:
            print(f"   ✅ enable_search 默认值为 None（使用全局配置）")
        else:
            print(f"   ❌ enable_search 默认值不正确")
            
        if search_auto_confirm_param and search_auto_confirm_param.default is None:
            print(f"   ✅ search_auto_confirm 默认值为 None（使用全局配置）")
        else:
            print(f"   ❌ search_auto_confirm 默认值不正确")
        
        # 测试3: 模拟不同的调用方式
        print(f"\n🧪 测试3: 模拟不同调用方式")
        
        # 保存原始配置
        original_config = generator.get_search_config()
        print(f"   原始配置: 搜索={original_config['enable_search_enhancement']}, 自动确认={original_config['search_auto_confirm']}")
        
        # 模拟调用（不实际执行，只测试配置变化）
        test_cases = [
            {
                "name": "默认调用（使用全局配置）",
                "params": {},
                "expected_search": original_config['enable_search_enhancement'],
                "expected_auto": original_config['search_auto_confirm']
            },
            {
                "name": "禁用搜索",
                "params": {"enable_search": False},
                "expected_search": False,
                "expected_auto": original_config['search_auto_confirm']
            },
            {
                "name": "启用搜索+自动确认",
                "params": {"enable_search": True, "search_auto_confirm": True},
                "expected_search": True,
                "expected_auto": True
            },
            {
                "name": "启用搜索+手动确认",
                "params": {"enable_search": True, "search_auto_confirm": False},
                "expected_search": True,
                "expected_auto": False
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n   测试用例{i}: {test_case['name']}")
            
            # 模拟配置应用逻辑
            temp_config = original_config.copy()
            if "enable_search" in test_case["params"]:
                temp_config["enable_search_enhancement"] = test_case["params"]["enable_search"]
            if "search_auto_confirm" in test_case["params"]:
                temp_config["search_auto_confirm"] = test_case["params"]["search_auto_confirm"]
            
            # 验证预期结果
            if temp_config["enable_search_enhancement"] == test_case["expected_search"]:
                print(f"     ✅ 搜索配置正确: {temp_config['enable_search_enhancement']}")
            else:
                print(f"     ❌ 搜索配置错误: 期望{test_case['expected_search']}, 实际{temp_config['enable_search_enhancement']}")
                
            if temp_config["search_auto_confirm"] == test_case["expected_auto"]:
                print(f"     ✅ 自动确认配置正确: {temp_config['search_auto_confirm']}")
            else:
                print(f"     ❌ 自动确认配置错误: 期望{test_case['expected_auto']}, 实际{temp_config['search_auto_confirm']}")
        
        print(f"\n✅ 搜索控制接口测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_usage_examples():
    """测试使用示例"""
    print(f"\n📖 使用示例演示")
    print("=" * 50)
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        generator = CompleteReportGenerator(use_async=False)
        
        # 示例数据
        topic = "人工智能产业研究"
        data_sources = ["示例数据源.txt"]
        
        print(f"📊 示例场景: 生成'{topic}'报告")
        print(f"📁 数据源: {data_sources}")
        
        print(f"\n💡 不同的调用方式:")
        
        # 方式1: 使用全局配置
        print(f"\n1️⃣ 使用全局配置（默认）:")
        print(f"   generator.generate_report('{topic}', {data_sources})")
        print(f"   # 使用当前全局搜索配置")
        
        # 方式2: 本次任务禁用搜索
        print(f"\n2️⃣ 本次任务禁用搜索:")
        print(f"   generator.generate_report('{topic}', {data_sources}, enable_search=False)")
        print(f"   # 本次任务不进行联网搜索，不影响全局配置")
        
        # 方式3: 本次任务启用搜索+自动确认
        print(f"\n3️⃣ 本次任务启用搜索+自动确认:")
        print(f"   generator.generate_report('{topic}', {data_sources}, enable_search=True, search_auto_confirm=True)")
        print(f"   # 本次任务自动进行联网搜索，无需手动确认")
        
        # 方式4: 本次任务启用搜索+手动确认
        print(f"\n4️⃣ 本次任务启用搜索+手动确认:")
        print(f"   generator.generate_report('{topic}', {data_sources}, enable_search=True, search_auto_confirm=False)")
        print(f"   # 本次任务进行联网搜索，但需要手动确认每次搜索")
        
        # 方式5: 只控制自动确认
        print(f"\n5️⃣ 只控制自动确认（使用全局搜索开关）:")
        print(f"   generator.generate_report('{topic}', {data_sources}, search_auto_confirm=True)")
        print(f"   # 使用全局搜索开关，但本次任务自动确认")
        
        print(f"\n🔧 配置优先级:")
        print(f"   单次任务参数 > 全局配置")
        print(f"   任务完成后自动恢复全局配置")
        
        print(f"\n✅ 使用示例演示完成")
        return True
        
    except Exception as e:
        print(f"❌ 示例演示失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🧪 联网搜索控制接口测试")
    print("=" * 80)
    
    # 测试1: 接口功能
    interface_ok = test_search_control_interface()
    
    # 测试2: 使用示例
    examples_ok = test_usage_examples()
    
    print("\n" + "=" * 80)
    print("📋 测试总结:")
    print(f"   接口功能测试: {'✅ 通过' if interface_ok else '❌ 失败'}")
    print(f"   使用示例演示: {'✅ 通过' if examples_ok else '❌ 失败'}")
    
    if interface_ok and examples_ok:
        print("\n🎉 联网搜索控制接口添加成功！")
        print("\n💡 新功能特点:")
        print("   ✅ 支持单次任务的搜索控制")
        print("   ✅ 不影响全局配置")
        print("   ✅ 任务完成后自动恢复")
        print("   ✅ 支持搜索开关和自动确认的独立控制")
        print("   ✅ 向后兼容，不影响现有代码")
        
        print("\n🚀 立即可用:")
        print("   # 禁用本次任务的联网搜索")
        print("   generator.generate_report(topic, data_sources, enable_search=False)")
        print("   # 启用本次任务的联网搜索且自动确认")
        print("   generator.generate_report(topic, data_sources, enable_search=True, search_auto_confirm=True)")
    else:
        print("\n⚠️ 接口添加不完整，需要进一步检查")

if __name__ == "__main__":
    main()
