#!/usr/bin/env python3
"""
测试空值修复功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from complete_report_generator import safe_json_loads, fix_empty_string_values
import json

def test_empty_value_cases():
    """测试各种空值情况"""
    
    print("🧪 测试空值修复功能")
    print("=" * 60)
    
    # 测试用例1: 根据实际错误信息构造的问题JSON
    test_case_1 = '''{
    "instructions": {
        "节点001": {
            "title": "1. 总论：核电产业概览与研究界定",
            "content_requirements": "
            "word_count": "80-100字"
        },
        "节点024": {
            "title": "2. 核电技术发展现状",
            "content_requirements": "详细分析核电技术",
            "word_count": "100-150字"
        }
    }
}'''
    
    print("📝 测试用例1: 实际错误场景")
    print(f"原始JSON: {repr(test_case_1[:150])}...")
    
    try:
        result = safe_json_loads(test_case_1)
        if result:
            print("✅ 修复成功!")
            instructions = result.get("instructions", {})
            print(f"   指导数量: {len(instructions)}")
            for node_id, instruction in instructions.items():
                print(f"   {node_id}: {instruction.get('title', 'N/A')}")
        else:
            print("❌ 修复失败，返回空结果")
    except Exception as e:
        print(f"❌ 修复失败: {e}")
    
    print()
    
    # 测试用例2: 多种空值情况
    test_case_2 = '''{
    "instructions": {
        "节点001": {
            "content_requirements": "
            "word_count": "80-100字"
        },
        "节点002": {
            "content_requirements": "",
            "word_count": "
        },
        "节点003": {
            "content_requirements": "正常内容",
            "word_count": "100字"
        }
    }
}'''
    
    print("📝 测试用例2: 多种空值情况")
    print(f"原始JSON: {repr(test_case_2[:100])}...")
    
    try:
        result = safe_json_loads(test_case_2)
        if result:
            print("✅ 修复成功!")
            instructions = result.get("instructions", {})
            print(f"   指导数量: {len(instructions)}")
        else:
            print("❌ 修复失败")
    except Exception as e:
        print(f"❌ 修复失败: {e}")
    
    print()
    
    # 测试用例3: 测试修复函数
    test_case_3 = '''            "content_requirements": "
            "word_count": "80-100字"'''
    
    print("📝 测试用例3: 测试修复函数")
    print(f"原始片段: {repr(test_case_3)}")
    
    fixed = fix_empty_string_values(test_case_3)
    print(f"修复后片段: {repr(fixed)}")
    
    print()

def test_standard_vs_safe_parsing():
    """对比标准解析和安全解析"""
    
    print("📊 标准解析 vs 安全解析对比")
    print("=" * 60)
    
    # 问题JSON
    problem_json = '''{
    "instructions": {
        "节点001": {
            "title": "测试标题",
            "content_requirements": "
            "word_count": "100字"
        }
    }
}'''
    
    print("🔍 测试JSON:")
    print(problem_json)
    print()
    
    # 标准解析
    print("❌ 标准json.loads解析:")
    try:
        result = json.loads(problem_json)
        print(f"✅ 成功: {result}")
    except json.JSONDecodeError as e:
        print(f"💥 失败: {e}")
        print(f"   错误位置: 行{e.lineno}, 列{e.colno}")
    
    print()
    
    # 安全解析
    print("✅ safe_json_loads解析:")
    try:
        result = safe_json_loads(problem_json)
        if result:
            print(f"🎯 成功: {len(result)} 个顶级字段")
            instructions = result.get("instructions", {})
            print(f"   指导数量: {len(instructions)}")
        else:
            print("⚠️ 返回空结果")
    except Exception as e:
        print(f"💥 失败: {e}")

if __name__ == "__main__":
    test_empty_value_cases()
    test_standard_vs_safe_parsing()
