# 完整代码重构实施计划

## 任务列表

- [x] 1. 准备工作和环境设置




  - 备份原始文件到安全位置
  - 清理之前不完整的拆分文件
  - 创建新的模块目录结构


  - _需求: 1.1, 1.2_





- [ ] 2. 基础配置模块拆分
  - [x] 2.1 创建完整的config.py模块

    - 提取所有API密钥（完整的10个密钥）
    - 提取MODEL_NAMES和MAX_CONSECUTIVE_CLEANUP_COUNT


    - 提取完整的GeminiModelConfig类及所有方法
    - _需求: 1.1, 1.2, 1.3_



  - [ ] 2.2 创建完整的token_manager.py模块
    - 提取完整的TokenManager类


    - 包含所有token估算和分批处理方法
    - 保留所有原始的计算逻辑和参数
    - _需求: 1.1, 1.2_




- [-] 3. 图表和图片处理模块

  - [ ] 3.1 创建完整的chart_generator.py模块
    - 提取完整的ChartGenerator类
    - 包含所有图表生成方法和字体设置逻辑
    - 保留所有matplotlib和plotly相关代码
    - _需求: 1.1, 1.2_

  - [x] 3.2 创建完整的image_matcher.py模块


    - 提取完整的ImageMatcher类


    - 包含所有图片扫描和匹配方法
    - 保留所有AI图片分析逻辑
    - _需求: 1.1, 1.2_

- [x] 4. API管理模块拆分



  - [ ] 4.1 创建完整的api_manager.py模块
    - 提取GeminiAPILimits类（完整实现）
    - 提取AsyncConfig类（完整实现）
    - 提取BaseGeminiAPIManager类（完整实现）
    - 提取GeminiAPIManager类（完整实现，包含所有同步方法）


    - _需求: 1.1, 1.2, 2.1_

  - [ ] 4.2 创建完整的async_api_manager.py模块
    - 提取完整的AsyncGeminiAPIManager类（不是占位符）
    - 包含所有异步方法的完整实现
    - 包含所有配额管理和智能降级逻辑



    - 包含所有错误处理和重试机制
    - _需求: 2.1, 2.2, 2.3, 2.4_

- [x] 5. 搜索功能模块拆分


  - [ ] 5.1 创建完整的search_manager.py模块
    - 提取完整的SearchTrigger类
    - 提取完整的SearchManager类（包含所有搜索API集成）
    - 提取完整的SearchToolManager类
    - 保留所有搜索API的配置和调用逻辑
    - _需求: 4.1, 4.2, 4.3, 4.4_



- [ ] 6. 内容生成模块拆分
  - [ ] 6.1 创建完整的content_generator.py模块
    - 提取完整的StructuredContentGenerator类
    - 提取完整的RobustContentCleaner类
    - 包含所有内容清理和结构化生成逻辑
    - _需求: 4.1, 4.2_



- [ ] 7. 主报告生成器拆分
  - [ ] 7.1 创建完整的report_generator.py模块
    - 提取完整的CompleteReportGenerator类
    - 包含所有报告生成方法（generate_report等）


    - 包含所有配置管理方法
    - 包含所有异步处理逻辑
    - 包含所有checkpoint功能
    - _需求: 3.1, 3.2, 3.3, 3.4_




- [ ] 8. 工具函数和主程序拆分
  - [x] 8.1 创建完整的utils.py模块



    - 提取所有独立的工具函数
    - 提取checkpoint管理相关函数
    - 提取main()函数的完整实现
    - 提取所有命令行参数处理逻辑
    - _需求: 5.1, 5.2, 5.3, 5.4_

- [ ] 9. 主入口文件创建
  - [ ] 9.1 创建新的主入口文件
    - 导入所有拆分后的模块
    - 提供与原文件相同的接口
    - 确保向后兼容性
    - _需求: 6.1, 6.2_

- [ ] 10. 完整性验证和测试
  - [ ] 10.1 代码完整性检查
    - 验证所有类和函数都被正确拆分
    - 验证代码行数与原文件匹配
    - 验证所有导入语句正确
    - _需求: 6.3, 6.4_

  - [ ] 10.2 功能测试
    - 测试所有模块可以正确导入
    - 测试主要功能与原文件一致
    - 测试异步功能正常工作
    - _需求: 6.1, 6.2, 6.3_

  - [ ] 10.3 集成测试
    - 测试模块间的协作正常
    - 测试完整的报告生成流程
    - 测试错误处理机制
    - _需求: 6.1, 6.2, 6.3_

## 质量检查点

每个任务完成后需要验证：
1. 代码内容与原文件完全一致（不允许任何修改）
2. 所有方法和属性都被完整保留
3. 导入关系正确无误
4. 功能测试通过

## 风险控制

1. 在每个阶段都保留备份
2. 逐步验证，发现问题立即修正
3. 保持原文件作为参考标准
4. 确保每个模块都可以独立导入和测试