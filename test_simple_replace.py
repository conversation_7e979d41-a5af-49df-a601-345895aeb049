#!/usr/bin/env python3
"""
测试简单字符串替换
"""

def test_simple_replace():
    """测试简单字符串替换"""
    
    problem_str = '"content_requirements": "\n            "word_count":'
    
    print("原始字符串:")
    print(repr(problem_str))
    print()
    
    # 测试替换
    fixed_str = problem_str.replace(
        '"content_requirements": "\n            "word_count":',
        '"content_requirements": "",\n            "word_count":'
    )
    
    print("替换后字符串:")
    print(repr(fixed_str))
    print()
    
    # 测试是否匹配
    if '"content_requirements": "\n            "word_count":' in problem_str:
        print("✅ 字符串匹配成功")
    else:
        print("❌ 字符串匹配失败")
    
    # 测试完整JSON
    full_json = '''{
    "instructions": {
        "节点001": {
            "title": "1. 总论：核电产业概览与研究界定",
            "content_requirements": "
            "word_count": "80-100字"
        }
    }
}'''
    
    print("\n完整JSON测试:")
    print("原始JSON:")
    print(repr(full_json))
    print()
    
    # 查找问题字符串
    if '"content_requirements": "\n            "word_count":' in full_json:
        print("✅ 在完整JSON中找到问题字符串")
        
        fixed_json = full_json.replace(
            '"content_requirements": "\n            "word_count":',
            '"content_requirements": "",\n            "word_count":'
        )
        
        print("修复后JSON:")
        print(repr(fixed_json))
        
        # 测试解析
        import json
        try:
            result = json.loads(fixed_json)
            print("✅ 修复后JSON解析成功!")
        except Exception as e:
            print(f"❌ 修复后JSON解析失败: {e}")
    else:
        print("❌ 在完整JSON中未找到问题字符串")

if __name__ == "__main__":
    test_simple_replace()
