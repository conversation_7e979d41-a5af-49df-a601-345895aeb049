#!/usr/bin/env python3
"""
测试文档分类主题混淆修复
验证AI不再混淆不同报告的主题
"""

def test_classification_prompt_improvement():
    """测试分类prompt改进"""
    print("🔧 测试文档分类prompt改进")
    print("=" * 60)
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        generator = CompleteReportGenerator(use_async=False)
        print("✅ 生成器创建成功")
        
        # 设置报告主题
        generator.current_report_topic = "核电产业研究报告"
        
        # 模拟核电相关的框架
        framework_sections = [
            {
                "title": "第一部：战略存在——核能在21世纪文明中的定位与悖论",
                "subsections": [
                    {"title": "核能的战略价值"},
                    {"title": "技术发展现状"}
                ]
            },
            {
                "title": "第二部：全球图景——世界核电的分布、格局与动态",
                "subsections": [
                    {"title": "全球核电分布"},
                    {"title": "主要国家政策"}
                ]
            }
        ]
        
        # 模拟文档
        batch = [
            {
                "name": "核电技术发展报告.pdf",
                "type": ".pdf",
                "size": 1024000,
                "path": "test.pdf",
                "is_image": False
            }
        ]
        
        # 生成分类prompt
        prompt = generator._generate_classification_prompt(framework_sections, batch)
        
        print(f"\n📝 生成的分类prompt:")
        print("=" * 50)
        print(prompt[:800] + "..." if len(prompt) > 800 else prompt)
        
        # 验证prompt包含关键信息
        expected_elements = [
            "核电产业研究报告",
            "第一部：战略存在",
            "第二部：全球图景",
            "标题名称必须完全匹配",
            "不要创造新的标题"
        ]
        
        found_elements = []
        for element in expected_elements:
            if element in prompt:
                found_elements.append(element)
                print(f"✅ 找到关键元素: '{element}'")
            else:
                print(f"❌ 缺少关键元素: '{element}'")
        
        if len(found_elements) >= 4:
            print("✅ prompt改进成功")
            return True
        else:
            print("❌ prompt改进不完整")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def test_title_validation_improvement():
    """测试标题验证改进"""
    print(f"\n🔍 测试标题验证改进")
    print("=" * 50)
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        generator = CompleteReportGenerator(use_async=False)
        generator.current_report_topic = "核电产业研究报告"
        
        # 有效标题（核电相关）
        valid_titles = [
            "第一部：战略存在——核能在21世纪文明中的定位与悖论",
            "第二部：全球图景——世界核电的分布、格局与动态"
        ]
        
        # 测试用例1: 正确的标题
        print(f"\n📝 测试用例1: 正确的标题")
        correct_classification = {
            "核电技术发展.pdf": ["第一部：战略存在——核能在21世纪文明中的定位与悖论"]
        }
        
        result1 = generator._validate_classification_titles(correct_classification, valid_titles)
        print(f"   验证结果: {'✅ 通过' if result1 else '❌ 失败'}")
        
        # 测试用例2: 格式略有差异的标题（应该通过模糊匹配）
        print(f"\n📝 测试用例2: 格式差异标题")
        fuzzy_classification = {
            "核电技术发展.pdf": ["**第一部：战略存在——核能在21世纪文明中的定位与悖论**"]
        }
        
        result2 = generator._validate_classification_titles(fuzzy_classification, valid_titles)
        print(f"   验证结果: {'✅ 通过' if result2 else '❌ 失败'}")
        
        # 测试用例3: 错误的主题（固态电池相关，应该被拒绝）
        print(f"\n📝 测试用例3: 错误主题标题")
        wrong_classification = {
            "核电技术发展.pdf": ["固态电池产业深度研究报告框架"]
        }
        
        result3 = generator._validate_classification_titles(wrong_classification, valid_titles)
        print(f"   验证结果: {'✅ 正确拒绝' if not result3 else '❌ 错误接受'}")
        
        # 测试用例4: 部分匹配（应该通过）
        print(f"\n📝 测试用例4: 部分匹配标题")
        partial_classification = {
            "核电技术发展.pdf": ["战略存在"]
        }
        
        result4 = generator._validate_classification_titles(partial_classification, valid_titles)
        print(f"   验证结果: {'✅ 通过' if result4 else '❌ 失败'}")
        
        return result1 and result2 and not result3 and result4
        
    except Exception as e:
        print(f"❌ 标题验证测试失败: {str(e)}")
        return False

def test_title_cleaning():
    """测试标题清理功能"""
    print(f"\n🧹 测试标题清理功能")
    print("=" * 50)
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        generator = CompleteReportGenerator(use_async=False)
        
        # 测试各种格式的标题清理
        test_cases = [
            ("**第一部：战略存在**", "第一部战略存在"),
            ("【第二部】全球图景", "第二部全球图景"),
            ("第三部：  技术发展  ", "第三部技术发展"),
            ("***核电产业***", "核电产业"),
            ("第四部：经济分析：成本效益", "第四部经济分析成本效益")
        ]
        
        print(f"📋 标题清理测试:")
        all_passed = True
        
        for original, expected in test_cases:
            cleaned = generator._clean_title_for_matching(original)
            expected_clean = expected.lower()
            
            if cleaned == expected_clean:
                print(f"   ✅ '{original}' -> '{cleaned}'")
            else:
                print(f"   ❌ '{original}' -> '{cleaned}' (期望: '{expected_clean}')")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 标题清理测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🧪 文档分类主题混淆修复验证")
    print("=" * 80)
    
    # 测试1: 分类prompt改进
    prompt_ok = test_classification_prompt_improvement()
    
    # 测试2: 标题验证改进
    validation_ok = test_title_validation_improvement()
    
    # 测试3: 标题清理功能
    cleaning_ok = test_title_cleaning()
    
    print("\n" + "=" * 80)
    print("📋 修复验证总结:")
    print(f"   分类prompt改进: {'✅ 通过' if prompt_ok else '❌ 失败'}")
    print(f"   标题验证改进: {'✅ 通过' if validation_ok else '❌ 失败'}")
    print(f"   标题清理功能: {'✅ 通过' if cleaning_ok else '❌ 失败'}")
    
    if prompt_ok and validation_ok and cleaning_ok:
        print("\n🎉 文档分类主题混淆修复成功！")
        print("\n💡 修复内容:")
        print("   ✅ 在分类prompt中明确指定报告主题")
        print("   ✅ 强调必须使用提供的标题，不要创造新标题")
        print("   ✅ 增加模糊匹配支持，处理格式差异")
        print("   ✅ 改进错误提示，明确指出主题混淆问题")
        print("   ✅ 添加标题清理功能，提高匹配准确性")
        
        print("\n🚀 修复效果:")
        print("   - AI不再混淆不同报告的主题")
        print("   - 支持标题格式的轻微差异")
        print("   - 提供更清晰的错误提示")
        print("   - 减少因格式问题导致的分类失败")
        
        print("\n📝 现在的分类流程:")
        print("   1. 明确指定当前报告主题（如：核电产业研究报告）")
        print("   2. 提供完整的框架标题结构")
        print("   3. 强调必须使用框架中的标题")
        print("   4. 支持标题的模糊匹配和格式清理")
        print("   5. 如果出现主题混淆，提供明确的错误提示")
    else:
        print("\n⚠️ 修复不完整，需要进一步检查")

if __name__ == "__main__":
    main()
