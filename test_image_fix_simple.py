#!/usr/bin/env python3
"""
简化的图片嵌入修复测试
"""

import sys
import warnings
from pathlib import Path

# 禁用matplotlib字体警告
warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')

def test_image_placeholder_fix():
    """测试修复后的图片占位符功能"""
    print("🔧 测试修复后的图片占位符功能...")
    
    try:
        from complete_report_generator import CompleteReportGenerator, ChartGenerator, MATPLOTLIB_AVAILABLE
        
        if not MATPLOTLIB_AVAILABLE:
            print("❌ matplotlib不可用")
            return False
        
        # 创建测试图片
        test_images_dir = Path("test_fix_images")
        test_images_dir.mkdir(exist_ok=True)
        
        chart_generator = ChartGenerator(test_images_dir)
        chart_path = chart_generator.generate_industry_chain_chart("测试", {})
        
        if not chart_path or not Path(chart_path).exists():
            print("❌ 无法创建测试图片")
            return False
        
        # 创建报告生成器
        generator = CompleteReportGenerator()
        
        # 测试数据
        test_content = "这是测试内容。\n\n这里需要插入图片。"
        test_images = [{
            'path': chart_path,
            'description': '测试图片',
            'relevance_score': 0.8
        }]
        
        # 测试图片占位符插入
        enhanced_content = generator._insert_image_placeholders_to_content(
            test_content, test_images, "测试节点"
        )
        
        print("原始内容:")
        print(test_content)
        print("\n增强后内容:")
        print(enhanced_content)
        
        # 检查结果
        has_placeholder = "[IMAGE:" in enhanced_content
        print(f"\n✅ 包含图片占位符: {has_placeholder}")
        
        # 清理
        import shutil
        if test_images_dir.exists():
            shutil.rmtree(test_images_dir)
        
        return has_placeholder
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_docx_image_fix():
    """测试修复后的DOCX图片插入功能"""
    print("\n📄 测试修复后的DOCX图片插入功能...")
    
    try:
        from complete_report_generator import CompleteReportGenerator, ChartGenerator
        from docx import Document
        
        # 创建测试图片
        test_images_dir = Path("test_fix_docx")
        test_images_dir.mkdir(exist_ok=True)
        
        chart_generator = ChartGenerator(test_images_dir)
        chart_path = chart_generator.generate_market_size_chart("测试", {})
        
        if not chart_path or not Path(chart_path).exists():
            print("❌ 无法创建测试图片")
            return False
        
        # 创建报告生成器
        generator = CompleteReportGenerator()
        
        # 创建测试DOCX文档
        doc = Document()
        doc.add_heading("测试报告", 0)
        
        # 测试图片插入
        success = generator._insert_image_to_docx_helper(
            doc, chart_path, "测试图片说明"
        )
        
        if success:
            # 保存文档
            test_docx = Path("test_image_fix.docx")
            doc.save(str(test_docx))
            
            if test_docx.exists():
                size_kb = test_docx.stat().st_size / 1024
                print(f"✅ DOCX文档生成成功: {test_docx} ({size_kb:.1f} KB)")
                test_docx.unlink()  # 删除测试文件
            
        # 清理
        import shutil
        if test_images_dir.exists():
            shutil.rmtree(test_images_dir)
        
        return success
        
    except ImportError:
        print("⚠️ python-docx不可用，跳过测试")
        return True
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_markdown_image_fix():
    """测试修复后的Markdown图片处理功能"""
    print("\n📝 测试修复后的Markdown图片处理功能...")
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        generator = CompleteReportGenerator()
        
        # 测试内容
        test_content = """
# 测试报告

这是测试内容。

[IMAGE:test_image.png,测试图片说明]

更多内容。
"""
        
        # 处理Markdown图片
        processed_content = generator._process_markdown_content_with_images(test_content)
        
        print("原始内容:")
        print(test_content)
        print("\n处理后内容:")
        print(processed_content)
        
        # 检查结果
        has_markdown_syntax = "![" in processed_content and "](" in processed_content
        print(f"\n✅ 包含Markdown图片语法: {has_markdown_syntax}")
        
        return has_markdown_syntax
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试图片嵌入修复...")
    print("=" * 50)
    
    tests = [
        ("图片占位符修复测试", test_image_placeholder_fix),
        ("DOCX图片插入修复测试", test_docx_image_fix),
        ("Markdown图片处理修复测试", test_markdown_image_fix),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        try:
            if test_func():
                print(f"✅ {test_name} 通过")
                passed += 1
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有修复测试通过！")
        print("\n🔧 修复内容:")
        print("   ✅ 修复了图片占位符插入方法的参数问题")
        print("   ✅ 修复了DOCX图片插入方法的调用问题")
        print("   ✅ 确保了Markdown图片处理的正常工作")
        return True
    else:
        print("⚠️ 部分修复测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
