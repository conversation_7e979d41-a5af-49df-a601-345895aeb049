#!/usr/bin/env python3
"""
测试异步API管理器的基本功能
"""
import sys
import os

# 添加模块路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'complete_report_generator_modules_final'))

def test_import():
    """测试模块导入"""
    try:
        # 直接导入，不通过__init__.py
        import sys
        sys.path.append('complete_report_generator_modules_final')
        
        from async_api_manager import AsyncGeminiAPIManager
        from config import GeminiModelConfig
        print("✅ 模块导入成功")
        return True
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def test_initialization():
    """测试初始化"""
    try:
        import sys
        sys.path.append('complete_report_generator_modules_final')
        
        from async_api_manager import AsyncGeminiAPIManager
        from config import GeminiModelConfig
        
        # 使用测试API密钥
        test_api_keys = ["test_key_1", "test_key_2"]
        test_models = ["gemini-pro", "gemini-pro-vision"]
        
        # 创建配置
        config = GeminiModelConfig()
        
        # 初始化管理器
        manager = AsyncGeminiAPIManager(test_api_keys, test_models, config)
        print("✅ 初始化成功")
        return True
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔍 测试异步API管理器")
    print("=" * 50)
    
    # 测试导入
    if not test_import():
        return False
    
    # 测试初始化
    if not test_initialization():
        return False
    
    print("\n✅ 所有测试通过！")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)