#!/usr/bin/env python3
"""
提取AsyncGeminiAPIManager类的完整内容
"""

def extract_async_class():
    with open('complete_report_generator.py', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 找到AsyncGeminiAPIManager类的开始
    start_line = None
    for i, line in enumerate(lines):
        if 'class AsyncGeminiAPIManager(BaseGeminiAPIManager):' in line:
            start_line = i
            break
    
    if start_line is None:
        print("Could not find AsyncGeminiAPIManager class")
        return
    
    # 找到类的结束 - 在2523行左右
    end_line = 2523  # 基于之前的搜索结果
    
    # 提取类内容
    class_lines = lines[start_line:end_line]
    
    # 创建完整的文件内容
    file_content = '''"""
异步API管理模块 - 异步Gemini API管理器
从complete_report_generator.py中提取的完整AsyncGeminiAPIManager类
"""
import asyncio
import time
import concurrent.futures
from typing import Dict, Any, List, Tuple, Optional
import google.generativeai as genai

# 导入基础模块
from .api_manager import BaseGeminiAPIManager, AsyncConfig
from .config import GeminiModelConfig


'''
    
    file_content += ''.join(class_lines)
    
    # 写入文件
    with open('complete_report_generator_modules_final/async_api_manager.py', 'w', encoding='utf-8') as f:
        f.write(file_content)
    
    print(f"Successfully extracted AsyncGeminiAPIManager class ({len(class_lines)} lines)")
    print("File saved to complete_report_generator_modules_final/async_api_manager.py")

if __name__ == "__main__":
    extract_async_class()