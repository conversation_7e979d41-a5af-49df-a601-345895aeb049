#!/usr/bin/env python3
"""
重构后模块功能测试脚本
验证所有模块可以正确导入和基本功能正常
"""

import sys
import traceback
from pathlib import Path

def test_module_imports():
    """测试模块导入功能"""
    print("🧪 模块导入测试")
    print("=" * 50)
    
    test_results = {}
    
    # 测试各个模块的导入
    modules_to_test = [
        ('config', ['API_KEYS', 'MODEL_NAMES', 'GeminiModelConfig', 'ImageMatcher']),
        ('token_manager', ['TokenManager']),
        ('api_manager', ['GeminiAPILimits', 'AsyncConfig', 'BaseGeminiAPIManager', 'GeminiAPIManager']),
        ('async_api_manager', ['AsyncGeminiAPIManager']),
        ('chart_generator', ['ChartGenerator']),
        ('search_manager', ['SearchTrigger', 'SearchManager', 'SearchToolManager']),
        ('content_generator', ['StructuredContentGenerator', 'RobustContentCleaner']),
        ('report_generator', ['CompleteReportGenerator']),
        ('utils', ['main', 'create_directories', 'validate_data_sources'])
    ]
    
    for module_name, expected_classes in modules_to_test:
        try:
            print(f"📦 测试模块: {module_name}")
            
            # 动态导入模块
            module = __import__(f'complete_report_generator_modules_final.{module_name}', 
                              fromlist=expected_classes)
            
            # 检查期望的类/函数是否存在
            missing_items = []
            for item_name in expected_classes:
                if hasattr(module, item_name):
                    print(f"   ✅ {item_name}")
                else:
                    missing_items.append(item_name)
                    print(f"   ❌ {item_name} - 缺失")
            
            if not missing_items:
                test_results[module_name] = "PASS"
                print(f"   ✅ 模块 {module_name} 导入成功")
            else:
                test_results[module_name] = f"PARTIAL - 缺失: {missing_items}"
                print(f"   ⚠️ 模块 {module_name} 部分成功")
                
        except ImportError as e:
            test_results[module_name] = f"FAIL - ImportError: {str(e)}"
            print(f"   ❌ 模块 {module_name} 导入失败: {str(e)}")
        except Exception as e:
            test_results[module_name] = f"FAIL - {type(e).__name__}: {str(e)}"
            print(f"   ❌ 模块 {module_name} 测试异常: {str(e)}")
        
        print()
    
    return test_results

def test_basic_functionality():
    """测试基本功能"""
    print("🔧 基本功能测试")
    print("=" * 50)
    
    functionality_results = {}
    
    try:
        # 测试配置模块
        print("📋 测试配置功能...")
        from complete_report_generator_modules_final.config import GeminiModelConfig
        
        config = GeminiModelConfig()
        test_config = config.get_model_config('gemini-2.5-pro')
        
        if isinstance(test_config, dict) and 'temperature' in test_config:
            functionality_results['config'] = "PASS"
            print("   ✅ 配置功能正常")
        else:
            functionality_results['config'] = "FAIL - 配置格式错误"
            print("   ❌ 配置功能异常")
            
    except Exception as e:
        functionality_results['config'] = f"FAIL - {str(e)}"
        print(f"   ❌ 配置功能测试失败: {str(e)}")
    
    try:
        # 测试Token管理器
        print("🔢 测试Token管理功能...")
        from complete_report_generator_modules_final.token_manager import TokenManager
        
        token_manager = TokenManager(max_tokens=100000)
        test_text = "这是一个测试文本" * 100
        
        batches = token_manager.split_into_batches([test_text])
        
        if isinstance(batches, list) and len(batches) > 0:
            functionality_results['token_manager'] = "PASS"
            print("   ✅ Token管理功能正常")
        else:
            functionality_results['token_manager'] = "FAIL - 批次分割失败"
            print("   ❌ Token管理功能异常")
            
    except Exception as e:
        functionality_results['token_manager'] = f"FAIL - {str(e)}"
        print(f"   ❌ Token管理功能测试失败: {str(e)}")
    
    try:
        # 测试图表生成器
        print("📊 测试图表生成功能...")
        from complete_report_generator_modules_final.chart_generator import ChartGenerator
        
        chart_gen = ChartGenerator()
        
        # 测试图表生成器初始化
        if hasattr(chart_gen, 'output_dir') and hasattr(chart_gen, '_setup_chinese_fonts'):
            functionality_results['chart_generator'] = "PASS"
            print("   ✅ 图表生成器初始化正常")
        else:
            functionality_results['chart_generator'] = "FAIL - 初始化异常"
            print("   ❌ 图表生成器初始化异常")
            
    except Exception as e:
        functionality_results['chart_generator'] = f"FAIL - {str(e)}"
        print(f"   ❌ 图表生成功能测试失败: {str(e)}")
    
    try:
        # 测试内容生成器
        print("📝 测试内容生成功能...")
        from complete_report_generator_modules_final.content_generator import RobustContentCleaner
        
        cleaner = RobustContentCleaner()
        test_content = "这是测试内容\n优化后的内容：这部分应该被清理\n正常内容保留"
        
        cleaned = cleaner._intelligent_clean_content(test_content)
        
        if isinstance(cleaned, str) and len(cleaned) > 0:
            functionality_results['content_generator'] = "PASS"
            print("   ✅ 内容生成功能正常")
        else:
            functionality_results['content_generator'] = "FAIL - 内容清理失败"
            print("   ❌ 内容生成功能异常")
            
    except Exception as e:
        functionality_results['content_generator'] = f"FAIL - {str(e)}"
        print(f"   ❌ 内容生成功能测试失败: {str(e)}")
    
    return functionality_results

def test_integration():
    """测试集成功能"""
    print("🔗 集成测试")
    print("=" * 50)
    
    integration_results = {}
    
    try:
        # 测试主模块导入
        print("📦 测试主模块集成...")
        from complete_report_generator_modules_final import CompleteReportGenerator
        
        # 测试报告生成器初始化（不使用真实API）
        generator = CompleteReportGenerator(use_async=False, max_tokens=50000)
        
        if hasattr(generator, 'api_manager') and hasattr(generator, 'report_config'):
            integration_results['main_integration'] = "PASS"
            print("   ✅ 主模块集成正常")
        else:
            integration_results['main_integration'] = "FAIL - 初始化异常"
            print("   ❌ 主模块集成异常")
            
    except Exception as e:
        integration_results['main_integration'] = f"FAIL - {str(e)}"
        print(f"   ❌ 主模块集成测试失败: {str(e)}")
    
    try:
        # 测试工具函数
        print("🛠️ 测试工具函数...")
        from complete_report_generator_modules_final.utils import create_directories, validate_data_sources
        
        # 测试目录创建
        create_directories()
        
        # 测试数据源验证
        test_sources = ["test_data_1", "test_data_2"]
        validated = validate_data_sources(test_sources)
        
        if isinstance(validated, list) and len(validated) == len(test_sources):
            integration_results['utils'] = "PASS"
            print("   ✅ 工具函数正常")
        else:
            integration_results['utils'] = "FAIL - 函数返回异常"
            print("   ❌ 工具函数异常")
            
    except Exception as e:
        integration_results['utils'] = f"FAIL - {str(e)}"
        print(f"   ❌ 工具函数测试失败: {str(e)}")
    
    return integration_results

def generate_test_report(import_results, functionality_results, integration_results):
    """生成测试报告"""
    print("\n📋 测试报告总结")
    print("=" * 60)
    
    # 统计结果
    total_tests = len(import_results) + len(functionality_results) + len(integration_results)
    passed_tests = 0
    failed_tests = 0
    partial_tests = 0
    
    print("📦 模块导入测试结果:")
    for module, result in import_results.items():
        status = "✅" if result == "PASS" else "⚠️" if "PARTIAL" in result else "❌"
        print(f"   {status} {module:<20} {result}")
        if result == "PASS":
            passed_tests += 1
        elif "PARTIAL" in result:
            partial_tests += 1
        else:
            failed_tests += 1
    
    print("\n🔧 功能测试结果:")
    for func, result in functionality_results.items():
        status = "✅" if result == "PASS" else "❌"
        print(f"   {status} {func:<20} {result}")
        if result == "PASS":
            passed_tests += 1
        else:
            failed_tests += 1
    
    print("\n🔗 集成测试结果:")
    for integration, result in integration_results.items():
        status = "✅" if result == "PASS" else "❌"
        print(f"   {status} {integration:<20} {result}")
        if result == "PASS":
            passed_tests += 1
        else:
            failed_tests += 1
    
    print(f"\n📊 测试统计:")
    print(f"   总测试数: {total_tests}")
    print(f"   通过: {passed_tests}")
    print(f"   部分通过: {partial_tests}")
    print(f"   失败: {failed_tests}")
    
    success_rate = (passed_tests + partial_tests * 0.5) / total_tests * 100 if total_tests > 0 else 0
    print(f"   成功率: {success_rate:.1f}%")
    
    if success_rate >= 80:
        print(f"\n✅ 测试结果: 优秀 - 重构成功")
        return True
    elif success_rate >= 60:
        print(f"\n⚠️ 测试结果: 良好 - 基本可用")
        return True
    else:
        print(f"\n❌ 测试结果: 需要改进")
        return False

def main():
    """主测试函数"""
    print("🧪 重构后模块功能测试")
    print("=" * 60)
    print("测试目标: 验证所有重构模块的导入和基本功能")
    print("=" * 60)
    
    try:
        # 执行各项测试
        import_results = test_module_imports()
        functionality_results = test_basic_functionality()
        integration_results = test_integration()
        
        # 生成测试报告
        success = generate_test_report(import_results, functionality_results, integration_results)
        
        if success:
            print(f"\n🎉 重构模块测试完成 - 功能正常!")
        else:
            print(f"\n⚠️ 重构模块测试完成 - 需要进一步优化")
            
        return success
        
    except Exception as e:
        print(f"\n❌ 测试执行失败: {str(e)}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)