#!/usr/bin/env python3
"""
测试数据结构不匹配修复效果
仅验证数据结构兼容性，不改动其他逻辑
"""

import asyncio
import time

async def test_data_structure_compatibility():
    """测试数据结构兼容性修复"""
    print("🔧 测试数据结构不匹配修复")
    print("=" * 50)
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        generator = CompleteReportGenerator(use_async=False)
        print("✅ 生成器创建成功")
        
        # 测试1: 原始tuple格式（向后兼容）
        print("\n📝 测试1: 原始tuple格式兼容性")
        old_format_nodes = [
            ({"title": "测试节点1", "level": 1}, 0),
            ({"title": "测试节点2", "level": 2}, 0),
            ({"title": "测试节点3", "level": 2}, 1)
        ]
        
        old_result = generator._format_nodes_for_instruction(old_format_nodes)
        print("原始格式输出:")
        print(old_result)
        
        # 验证原始格式是否正常
        expected_old = ["节点001", "节点002", "节点003"]
        for expected_id in expected_old:
            if expected_id in old_result:
                print(f"✅ {expected_id} 正确生成")
            else:
                print(f"❌ {expected_id} 缺失")
        
        # 测试2: 新dict格式（全局ID）
        print("\n📝 测试2: 新dict格式（全局ID）")
        new_format_nodes = [
            {
                "node": {"title": "产业概览", "level": 1},
                "section_idx": 0,
                "global_id": "节点001",
                "global_index": 0
            },
            {
                "node": {"title": "市场规模", "level": 2},
                "section_idx": 0,
                "global_id": "节点002", 
                "global_index": 1
            },
            {
                "node": {"title": "技术分析", "level": 1},
                "section_idx": 1,
                "global_id": "节点007",
                "global_index": 6
            }
        ]
        
        new_result = generator._format_nodes_for_instruction(new_format_nodes)
        print("新格式输出:")
        print(new_result)
        
        # 验证新格式是否使用全局ID
        expected_new = ["节点001", "节点002", "节点007"]
        for expected_id in expected_new:
            if expected_id in new_result:
                print(f"✅ {expected_id} 正确使用全局ID")
            else:
                print(f"❌ {expected_id} 全局ID缺失")
        
        # 测试3: 验证ID匹配逻辑
        print("\n📝 测试3: 验证ID匹配逻辑")
        
        # 模拟AI返回的指导（使用全局ID）
        mock_instructions = {
            "节点001": {
                "title": "产业概览",
                "content_requirements": "全面分析产业概况",
                "word_count": "3000-5000字"
            },
            "节点002": {
                "title": "市场规模", 
                "content_requirements": "分析市场规模数据",
                "word_count": "2000-3000字"
            },
            "节点007": {
                "title": "技术分析",
                "content_requirements": "深入技术分析",
                "word_count": "3000-5000字"
            }
        }
        
        # 测试匹配逻辑
        matched_count = 0
        for item in new_format_nodes:
            global_id = item["global_id"]
            title = item["node"].get("title", "无标题")
            
            if global_id in mock_instructions:
                instruction = mock_instructions[global_id]
                print(f"✅ {global_id} ({title}) 匹配成功")
                print(f"   指导: {instruction['content_requirements']}")
                matched_count += 1
            else:
                print(f"❌ {global_id} ({title}) 匹配失败")
        
        match_rate = matched_count / len(new_format_nodes) * 100
        print(f"\n📊 匹配结果: {matched_count}/{len(new_format_nodes)} ({match_rate:.1f}%)")
        
        if match_rate == 100:
            print("🎉 数据结构不匹配问题已完全修复！")
            return True
        else:
            print("⚠️ 仍存在匹配问题")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_minimal_instruction_generation():
    """测试最小化的指导生成（验证修复效果）"""
    print("\n🧪 测试最小化指导生成")
    print("=" * 50)
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        generator = CompleteReportGenerator(use_async=False)
        
        # 创建最小测试数据
        test_sections = [
            {
                "title": "测试章节",
                "level": 1,
                "children": [
                    {"title": "子节点1", "level": 2, "children": []},
                    {"title": "子节点2", "level": 2, "children": []}
                ]
            }
        ]
        
        test_data_sources = ["测试数据源"]
        
        print(f"📊 测试数据: 1个章节，3个节点")
        
        start_time = time.time()
        
        # 只测试任务指导生成，不测试其他功能
        await generator._generate_task_instructions_async(test_sections, test_data_sources)
        
        end_time = time.time()
        elapsed_time = end_time - start_time
        
        # 验证结果
        nodes_with_instructions = 0
        total_nodes = 0
        
        def check_instructions(node):
            nonlocal nodes_with_instructions, total_nodes
            total_nodes += 1
            if "task_instruction" in node and node["task_instruction"]:
                nodes_with_instructions += 1
                instruction = node["task_instruction"]
                content_req = instruction.get("content_requirements", "")
                print(f"   ✅ {node.get('title', '无标题')}: {content_req[:40]}...")
            else:
                print(f"   ❌ {node.get('title', '无标题')}: 缺少指导")
            
            if "children" in node:
                for child in node["children"]:
                    check_instructions(child)
        
        print(f"\n📋 指导生成结果:")
        for section in test_sections:
            check_instructions(section)
        
        coverage_rate = nodes_with_instructions / total_nodes * 100
        
        print(f"\n📊 修复效果:")
        print(f"   耗时: {elapsed_time:.1f}秒")
        print(f"   覆盖率: {coverage_rate:.1f}% ({nodes_with_instructions}/{total_nodes})")
        
        if coverage_rate >= 90:
            print("🎉 数据结构修复成功，指导生成正常！")
            return True
        else:
            print("⚠️ 仍需进一步优化")
            return False
            
    except Exception as e:
        print(f"❌ 指导生成测试失败: {str(e)}")
        return False

async def main():
    """主测试函数"""
    print("🔧 数据结构不匹配修复验证")
    print("=" * 80)
    
    # 测试1: 数据结构兼容性
    compatibility_ok = await test_data_structure_compatibility()
    
    # 测试2: 实际指导生成
    generation_ok = await test_minimal_instruction_generation()
    
    print("\n" + "=" * 80)
    print("📋 修复验证总结:")
    print(f"   数据结构兼容性: {'✅ 通过' if compatibility_ok else '❌ 失败'}")
    print(f"   指导生成功能: {'✅ 通过' if generation_ok else '❌ 失败'}")
    
    if compatibility_ok and generation_ok:
        print("\n🎉 数据结构不匹配问题修复成功！")
        print("\n💡 修复内容:")
        print("   ✅ _format_nodes_for_instruction 方法支持两种数据格式")
        print("   ✅ 自动识别并使用正确的ID（全局ID vs 局部ID）")
        print("   ✅ 保持向后兼容性，不影响现有功能")
        print("   ✅ 修复ID匹配失败导致的指导丢失问题")
    else:
        print("\n⚠️ 修复不完整，需要进一步检查")
    
    print("\n🔧 修复说明:")
    print("   这是一个最小化修复，只解决数据结构不匹配问题")
    print("   其他复杂逻辑保持不变，确保系统稳定性")

if __name__ == "__main__":
    asyncio.run(main())
