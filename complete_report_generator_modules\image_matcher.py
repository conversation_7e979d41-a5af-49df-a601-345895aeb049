"""
图片匹配器模块
"""
from pathlib import Path
from typing import Dict, Any, List

class ImageMatcher:
    """图片匹配器 - 使用AI匹配内容与图片"""

    def __init__(self, data_sources_paths: List[str], api_manager):
        self.data_sources_paths = data_sources_paths
        self.api_manager = api_manager
        self.image_cache = {}
        self.analysis_cache = {}  # 缓存图片分析结果
        self._scan_images()

    def _scan_images(self):
        """递归扫描所有数据源路径中的图片"""
        print("🔍 扫描数据源中的图片文件...")

        image_extensions = {'.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff', '.webp'}
        total_images = 0

        for data_source in self.data_sources_paths:
            source_path = Path(data_source)
            if source_path.is_file():
                # 如果是文件，扫描其所在目录
                source_path = source_path.parent

            if source_path.exists() and source_path.is_dir():
                # 递归搜索所有图片
                for image_path in source_path.rglob('*'):
                    if image_path.is_file() and image_path.suffix.lower() in image_extensions:
                        try:
                            # 尝试获取相对路径，如果失败则使用绝对路径
                            relative_path = str(image_path.relative_to(Path.cwd()))
                        except ValueError:
                            # 如果不在当前工作目录下，使用绝对路径
                            relative_path = str(image_path.absolute())

                        self.image_cache[str(image_path)] = {
                            'path': str(image_path),
                            'relative_path': relative_path,
                            'name': image_path.name,
                            'size': image_path.stat().st_size,
                            'analyzed': False
                        }
                        total_images += 1

        print(f"   📊 共发现 {total_images} 张图片")
        return self.image_cache

    async def match_images_for_node(self, node_content: str, node_title: str, max_images: int = 5) -> List[Dict]:
        """为节点内容匹配合适的图片"""
        if not self.image_cache:
            return []

        print(f"   🖼️ 为节点 '{node_title}' 匹配图片...")

        try:
            # 准备图片信息用于AI分析
            image_list = []
            for img_path, img_info in list(self.image_cache.items())[:20]:  # 限制分析图片数量
                image_list.append({
                    'path': img_info['relative_path'],
                    'name': img_info['name']
                })

            if not image_list:
                return []

            # 构建AI匹配prompt
            prompt = f"""
作为图片内容分析专家，请分析以下节点内容与图片的匹配度。

节点标题：{node_title}

节点内容：
{node_content[:1000]}...

可用图片列表：
{self._format_image_list(image_list)}

请选择最多{max_images}张与节点内容最相关的图片，按相关度排序。

要求：
1. 图片内容应与节点主题高度相关
2. 优先选择能够支撑或说明节点内容的图片
3. 避免选择无关或装饰性图片
4. 如果没有合适的图片，可以返回空列表

请以JSON格式返回：
{{
    "matched_images": [
        {{
            "path": "图片相对路径",
            "relevance_score": 0.95,
            "reason": "选择理由"
        }}
    ]
}}
"""

            # 调用AI进行匹配 - 修复：移除不支持的purpose参数
            if hasattr(self.api_manager, 'generate_content_with_model_async'):
                response, key_index = await self.api_manager.generate_content_with_model_async(
                    prompt,
                    "gemini-2.5-flash"
                )
            else:
                # 备用方案
                response = "图片匹配功能暂时不可用"

            if response:
                matched_images = self._parse_image_matching_response(response)
                print(f"     ✅ 匹配到 {len(matched_images)} 张图片")
                return matched_images
            else:
                print(f"     ⚠️ 图片匹配失败")
                return []

        except (ValueError, KeyError, AttributeError) as e:
            print(f"     ❌ 图片匹配参数错误: {str(e)}")
            return []
        except Exception as e:
            print(f"     ❌ 图片匹配未知错误: {str(e)}")
            return []

    def _format_image_list(self, image_list: List[Dict]) -> str:
        """格式化图片列表用于AI分析"""
        formatted = []
        for i, img in enumerate(image_list, 1):
            formatted.append(f"{i}. {img['name']} (路径: {img['path']})")
        return "\n".join(formatted)

    def _parse_image_matching_response(self, response: str) -> List[Dict]:
        """解析AI图片匹配响应"""
        try:
            import json
            import re

            # 提取JSON部分
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                data = json.loads(json_str)

                matched_images = data.get('matched_images', [])

                # 验证图片路径存在
                valid_images = []
                for img in matched_images:
                    img_path = img.get('path', '')
                    if any(info['relative_path'] == img_path for info in self.image_cache.values()):
                        valid_images.append(img)

                return valid_images

        except Exception as e:
            print(f"     ⚠️ 解析图片匹配响应失败: {str(e)}")

        return []

    # 注释掉的单张最佳图片选择功能（备用）
    # async def select_best_single_image(self, node_content: str, node_title: str) -> Dict:
    #     """选择单张最佳匹配图片（备用功能，已注释）"""
    #     matched_images = await self.match_images_for_node(node_content, node_title, max_images=10)
    #     if matched_images:
    #         # 返回相关度最高的图片
    #         best_image = max(matched_images, key=lambda x: x.get('relevance_score', 0))
    #         return best_image
    #     return {}

    def insert_images_to_content(self, content: str, matched_images: List[Dict]) -> str:
        """将匹配的图片插入到内容中"""
        if not matched_images:
            return content

        # 在内容末尾添加图片占位符
        content_with_images = content + "\n\n"

        for i, img in enumerate(matched_images, 1):
            img_path = img.get('path', '')
            reason = img.get('reason', '相关图片')

            # 生成图片标题
            img_title = f"图 {i}: {reason}"

            # 添加图片占位符（支持DOCX和Markdown）
            content_with_images += f"[IMAGE:{img_path},{img_title}]\n\n"

        return content_with_images