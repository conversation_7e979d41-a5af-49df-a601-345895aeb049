#!/usr/bin/env python3
"""
JSON解析修复功能演示
模拟实际运行中遇到的JSON解析问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from complete_report_generator import safe_json_loads
import json

def simulate_api_response_with_control_characters():
    """模拟包含控制字符的API响应"""
    
    # 这是一个典型的会导致"Invalid control character"错误的响应
    problematic_response = '''```json
{
    "instructions": {
        "节点001": {
            "content_requirements": "全面分析地热发电技术的发展现状
重点关注以下几个方面：
1. 技术原理与分类
2. 全球发展趋势	
3. 主要技术挑战",
            "word_count": "2000-3000字"
        },
        "节点002": {
            "content_requirements": "深入研究增强地热系统(EGS)技术
包括技术特点、应用前景等内容
确保内容的专业性和准确性",
            "word_count": "1500-2500字"
        }
    }
}
```'''
    
    print("🔍 模拟API响应解析场景")
    print("=" * 60)
    print("📡 收到API响应（包含控制字符）:")
    print(f"响应长度: {len(problematic_response)} 字符")
    print(f"前200字符: {repr(problematic_response[:200])}...")
    print()
    
    # 尝试使用标准json.loads解析（会失败）
    print("❌ 使用标准json.loads解析:")
    try:
        # 提取JSON部分
        if "```json" in problematic_response:
            start = problematic_response.find("```json") + 7
            end = problematic_response.find("```", start)
            if end != -1:
                json_str = problematic_response[start:end].strip()
                result = json.loads(json_str)
                print(f"✅ 标准解析成功: {len(result)} 个字段")
    except json.JSONDecodeError as e:
        print(f"💥 标准解析失败: {e}")
        print(f"   错误位置: 行{e.lineno}, 列{e.colno}")
    except Exception as e:
        print(f"💥 标准解析异常: {e}")
    
    print()
    
    # 使用修复后的safe_json_loads解析（会成功）
    print("✅ 使用safe_json_loads解析:")
    try:
        result = safe_json_loads(problematic_response)
        if result:
            instructions = result.get("instructions", {})
            print(f"🎯 解析成功!")
            print(f"   获得指导数量: {len(instructions)}")
            print(f"   指导节点: {list(instructions.keys())}")
            
            # 显示解析后的内容示例
            for node_id, instruction in instructions.items():
                content_req = instruction.get("content_requirements", "")
                word_count = instruction.get("word_count", "")
                print(f"   📋 {node_id}:")
                print(f"      内容要求: {content_req[:50]}...")
                print(f"      字数要求: {word_count}")
        else:
            print("⚠️ 解析结果为空")
    except Exception as e:
        print(f"💥 修复解析也失败: {e}")
    
    print()
    print("🎉 演示完成！JSON解析问题已修复")

def show_before_after_comparison():
    """展示修复前后的对比"""
    
    print("\n📊 修复效果对比")
    print("=" * 60)
    
    # 常见的问题JSON字符串
    problem_cases = [
        {
            "name": "换行符问题",
            "json": '{"content": "包含\n换行符的内容"}',
            "description": "JSON字符串值中包含未转义的换行符"
        },
        {
            "name": "制表符问题", 
            "json": '{"description": "包含\t制表符的描述"}',
            "description": "JSON字符串值中包含未转义的制表符"
        },
        {
            "name": "回车符问题",
            "json": '{"text": "包含\r回车符的文本"}',
            "description": "JSON字符串值中包含未转义的回车符"
        },
        {
            "name": "混合控制字符",
            "json": '{"content": "复杂内容\n包含换行\t制表符\r回车符"}',
            "description": "JSON字符串值中包含多种控制字符"
        }
    ]
    
    for i, case in enumerate(problem_cases, 1):
        print(f"🔍 测试用例 {i}: {case['name']}")
        print(f"   描述: {case['description']}")
        print(f"   原始JSON: {repr(case['json'])}")
        
        # 标准解析
        try:
            json.loads(case['json'])
            print("   ✅ 标准json.loads: 成功")
        except json.JSONDecodeError as e:
            print(f"   ❌ 标准json.loads: 失败 - {e}")
        
        # 修复后解析
        try:
            result = safe_json_loads(case['json'])
            print(f"   ✅ safe_json_loads: 成功 - {result}")
        except Exception as e:
            print(f"   ❌ safe_json_loads: 失败 - {e}")
        
        print()

if __name__ == "__main__":
    simulate_api_response_with_control_characters()
    show_before_after_comparison()
