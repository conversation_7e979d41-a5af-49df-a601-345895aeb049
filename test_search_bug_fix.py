#!/usr/bin/env python3
"""
测试搜索功能BUG修复
验证联网搜索功能是否能正常工作
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from complete_report_generator import AIReportGenerator

def test_search_functionality():
    """测试搜索功能"""
    print("🔧 开始测试搜索功能修复...")
    
    try:
        # 创建生成器实例
        generator = AIReportGenerator(use_async=False)
        
        # 测试搜索功能
        print("\n1️⃣ 测试基础搜索功能...")
        search_result = generator.test_search_functionality()
        
        if search_result:
            print("✅ 基础搜索功能测试通过")
        else:
            print("❌ 基础搜索功能测试失败")
            return False
        
        # 测试搜索增强配置
        print("\n2️⃣ 测试搜索增强配置...")
        enable_search = generator.report_config.get("enable_search_enhancement", False)
        auto_confirm = generator.report_config.get("search_auto_confirm", False)
        
        print(f"   搜索增强启用: {enable_search}")
        print(f"   自动确认搜索: {auto_confirm}")
        
        if enable_search and auto_confirm:
            print("✅ 搜索增强配置正确")
        else:
            print("❌ 搜索增强配置有问题")
            return False
        
        # 测试默认搜索策略
        print("\n3️⃣ 测试默认搜索策略...")
        default_strategy = generator._create_default_search_strategy("人工智能")
        
        if default_strategy and len(default_strategy) > 0:
            print(f"✅ 默认搜索策略创建成功，包含 {len(default_strategy)} 个搜索任务")
            for i, task in enumerate(default_strategy, 1):
                print(f"   {i}. {task['tool_name']}: {task['parameters']['query']}")
        else:
            print("❌ 默认搜索策略创建失败")
            return False
        
        print("\n🎉 基础搜索功能测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 开始测试搜索功能修复...")
    
    # 测试基础搜索功能
    basic_test = test_search_functionality()
    
    if basic_test:
        print("\n🎉 搜索功能修复测试通过！")
        print("✅ 现在可以在完整版报告生成中正常使用联网搜索功能")
    else:
        print("\n❌ 搜索功能测试失败")
    
    print("\n📋 修复总结:")
    print("1. 强化了工具调用prompt，确保Gemini建议进行搜索")
    print("2. 添加了默认搜索策略，即使Gemini不建议也会执行搜索")
    print("3. 强制跳过用户确认，自动启用搜索增强")
    print("4. 增加了详细的调试信息和错误处理")
    print("5. 添加了搜索功能预检查")
    print("\n🔧 修复的关键问题:")
    print("- 修复了工具调用prompt过于简单的问题")
    print("- 修复了Gemini不建议搜索时跳过搜索增强的问题")
    print("- 修复了用户确认阻塞搜索增强的问题")
    print("- 添加了强制搜索策略确保搜索功能始终启用")
