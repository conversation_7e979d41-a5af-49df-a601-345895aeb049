"""
搜索管理模块 - 搜索功能管理器
从complete_report_generator.py中提取的完整搜索相关类
"""
import os
import json
import traceback
from typing import List, Dict, Any


class SearchTrigger:
    """搜索需求识别系统"""

    def __init__(self, generator):
        self.generator = generator

    def analyze_content_gaps(self, generated_content, topic):
        """分析内容缺口，确定搜索需求"""
        gaps = []

        # 时效性检查
        if self.needs_latest_data(generated_content, topic):
            gaps.append({
                'type': 'latest_data',
                'query': f'{topic} 最新数据 2024 2025',
                'priority': 'high',
                'reason': '内容可能缺乏最新的市场数据和发展动态'
            })

        # 市场数据检查
        if self.lacks_market_data(generated_content, topic):
            gaps.append({
                'type': 'market_data',
                'query': f'{topic} 市场规模 竞争格局 行业分析',
                'priority': 'medium',
                'reason': '缺少详细的市场分析和竞争格局信息'
            })

        # 技术发展检查
        if self.needs_tech_updates(generated_content, topic):
            gaps.append({
                'type': 'technology',
                'query': f'{topic} 技术发展 创新 突破',
                'priority': 'medium',
                'reason': '需要补充最新的技术发展和创新信息'
            })

        # 政策法规检查
        if self.needs_policy_info(generated_content, topic):
            gaps.append({
                'type': 'policy',
                'query': f'{topic} 政策 法规 标准 监管',
                'priority': 'medium',
                'reason': '缺少相关政策法规和行业标准信息'
            })

        # 案例研究检查
        if self.needs_case_studies(generated_content, topic):
            gaps.append({
                'type': 'cases',
                'query': f'{topic} 案例 项目 应用 实践',
                'priority': 'low',
                'reason': '需要补充实际案例和应用实践'
            })

        return gaps

    def needs_latest_data(self, content, topic):
        """检查是否需要最新数据"""
        # topic参数用于上下文分析
        # 检查内容中是否缺少2024年的数据
        current_year_mentions = content.count('2024') + content.count('2025')
        data_keywords = ['数据', '统计', '报告', '调研', '市场规模']
        data_mentions = sum(content.count(keyword) for keyword in data_keywords)

        # 如果数据关键词多但缺少最新年份，则需要最新数据
        return data_mentions > 5 and current_year_mentions < 3

    def lacks_market_data(self, content, topic):
        """检查是否缺少市场数据"""
        # topic参数用于市场相关性判断
        market_keywords = ['市场', '竞争', '份额', '规模', '增长率', '预测']
        market_score = sum(content.count(keyword) for keyword in market_keywords)

        # 如果市场关键词提及较少，可能需要补充
        return market_score < 10

    def needs_tech_updates(self, content, topic):
        """检查是否需要技术更新"""
        tech_keywords = ['技术', '创新', '突破', '发展', '趋势', '前沿']
        tech_score = sum(content.count(keyword) for keyword in tech_keywords)

        # 技术类主题通常需要最新的技术信息
        tech_topics = ['AI', '人工智能', '区块链', '物联网', '5G', '云计算', '大数据']
        is_tech_topic = any(keyword in topic for keyword in tech_topics)

        return is_tech_topic and tech_score < 15

    def needs_policy_info(self, content, topic):
        """检查是否需要政策信息"""
        # topic参数用于政策相关性判断
        policy_keywords = ['政策', '法规', '标准', '监管', '规范', '指导']
        policy_score = sum(content.count(keyword) for keyword in policy_keywords)

        # 如果政策关键词较少，可能需要补充
        return policy_score < 5

    def needs_case_studies(self, content, topic):
        """检查是否需要案例研究"""
        # topic参数用于案例相关性判断
        case_keywords = ['案例', '项目', '应用', '实践', '示例', '成功']
        case_score = sum(content.count(keyword) for keyword in case_keywords)

        # 如果案例关键词较少，可能需要补充
        return case_score < 8


class SearchManager:
    """搜索API管理器"""

    def __init__(self, generator):
        self.generator = generator
        self.search_apis = {}
        self.init_search_apis()

    def init_search_apis(self):
        """初始化搜索API"""
        # Metaso Search API (优先使用，使用固定密钥)
        try:
            # 使用您提供的固定API密钥
            metaso_api_key = 'mk-988A8E4DC50C53312E3D1A8729687F4C'
            self.search_apis['metaso'] = {
                'api_key': metaso_api_key,
                'enabled': True
            }
            print("✅ Metaso Search API 已配置 (使用固定密钥)")
            print("   🌐 支持网页搜索模式")
            print("   🎓 支持学术搜索模式")
        except Exception as e:
            print(f"⚠️ Metaso Search API 配置失败: {str(e)}")

        # Google Custom Search API
        try:
            google_api_key = os.getenv('GOOGLE_SEARCH_API_KEY')
            google_cx = os.getenv('GOOGLE_SEARCH_CX')
            if google_api_key and google_cx:
                self.search_apis['google'] = {
                    'api_key': google_api_key,
                    'cx': google_cx,
                    'enabled': True
                }
                print("✅ Google Search API 已配置")
            else:
                print("⚠️ Google Search API 未配置")
        except Exception as e:
            print(f"⚠️ Google Search API 配置失败: {str(e)}")

        # Bing Search API
        try:
            bing_api_key = os.getenv('BING_SEARCH_API_KEY')
            if bing_api_key:
                self.search_apis['bing'] = {
                    'api_key': bing_api_key,
                    'enabled': True
                }
                print("✅ Bing Search API 已配置")
            else:
                print("⚠️ Bing Search API 未配置")
        except Exception as e:
            print(f"⚠️ Bing Search API 配置失败: {str(e)}")

    def search_google(self, query, num_results=5):
        """使用Google Custom Search API搜索"""
        if 'google' not in self.search_apis or not self.search_apis['google']['enabled']:
            return []

        try:
            import requests

            api_key = self.search_apis['google']['api_key']
            cx = self.search_apis['google']['cx']

            url = "https://www.googleapis.com/customsearch/v1"
            params = {
                'key': api_key,
                'cx': cx,
                'q': query,
                'num': min(num_results, 10),
                'dateRestrict': 'y1'  # 限制在一年内的结果
            }

            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()

            data = response.json()
            results = []

            for item in data.get('items', []):
                results.append({
                    'title': item.get('title', ''),
                    'url': item.get('link', ''),
                    'snippet': item.get('snippet', ''),
                    'source': 'google',
                    'date': item.get('pagemap', {}).get('metatags', [{}])[0].get('article:published_time', '')
                })

            return results

        except Exception as e:
            print(f"⚠️ Google搜索失败: {str(e)}")
            return []

    def search_bing(self, query, num_results=5):
        """使用Bing Search API搜索"""
        if 'bing' not in self.search_apis or not self.search_apis['bing']['enabled']:
            return []

        try:
            import requests

            api_key = self.search_apis['bing']['api_key']

            url = "https://api.bing.microsoft.com/v7.0/search"
            headers = {
                'Ocp-Apim-Subscription-Key': api_key
            }
            params = {
                'q': query,
                'count': min(num_results, 10),
                'freshness': 'Year'  # 限制在一年内的结果
            }

            response = requests.get(url, headers=headers, params=params, timeout=10)
            response.raise_for_status()

            data = response.json()
            results = []

            for item in data.get('webPages', {}).get('value', []):
                results.append({
                    'title': item.get('name', ''),
                    'url': item.get('url', ''),
                    'snippet': item.get('snippet', ''),
                    'source': 'bing',
                    'date': item.get('dateLastCrawled', '')
                })

            return results

        except requests.exceptions.Timeout as e:
            print(f"⚠️ Bing搜索超时: {str(e)}")
            return []
        except requests.exceptions.ConnectionError as e:
            print(f"⚠️ Bing搜索网络连接错误: {str(e)}")
            return []
        except requests.exceptions.HTTPError as e:
            print(f"⚠️ Bing搜索HTTP错误: {e.response.status_code} - {str(e)}")
            return []
        except json.JSONDecodeError as e:
            print(f"⚠️ Bing搜索响应JSON格式错误: {str(e)}")
            return []
        except KeyError as e:
            print(f"⚠️ Bing搜索响应缺少必需字段: {str(e)}")
            return []
        except Exception as e:
            print(f"⚠️ 未预期的Bing搜索错误: {str(e)}")
            return []

    def search_metaso(self, query, scope='webpage', num_results=5):
        """使用Metaso Search API搜索 - 更新版本"""
        try:
            import http.client
            import json

            # 使用固定的API密钥
            api_key = 'mk-988A8E4DC50C53312E3D1A8729687F4C'

            conn = http.client.HTTPSConnection("metaso.cn")

            # 根据scope设置不同的payload
            if scope == 'webpage':
                payload = json.dumps({
                    "q": query,
                    "scope": "webpage",
                    "includeSummary": True,
                    "size": str(min(num_results, 10)),
                    "includeRawContent": True,
                    "conciseSnippet": True
                })
            elif scope == 'scholar':
                payload = json.dumps({
                    "q": query,
                    "scope": "scholar",
                    "includeSummary": True,
                    "size": str(min(num_results, 10)),
                    "conciseSnippet": True
                })
            else:
                # 默认使用webpage模式
                payload = json.dumps({
                    "q": query,
                    "scope": "webpage",
                    "includeSummary": True,
                    "size": str(min(num_results, 10)),
                    "includeRawContent": True,
                    "conciseSnippet": True
                })

            headers = {
                'Authorization': f'Bearer {api_key}',
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }

            print(f"🔍 Metaso搜索: {query} (模式: {scope})")

            conn.request("POST", "/api/v1/search", payload, headers)
            res = conn.getresponse()
            data = res.read()

            if res.status == 200:
                response_data = json.loads(data.decode("utf-8"))
                results = []

                # 调试：打印完整响应结构
                print(f"   📋 API响应结构: {list(response_data.keys())}")

                # 尝试多种可能的响应格式
                items = []
                if scope == 'webpage' and 'webpages' in response_data:
                    # 网页搜索结果在webpages字段
                    items = response_data['webpages']
                elif scope == 'scholar' and 'papers' in response_data:
                    # 学术搜索结果在papers字段
                    items = response_data['papers']
                elif scope == 'scholar' and 'scholars' in response_data:
                    # 学术搜索结果可能在scholars字段
                    items = response_data['scholars']
                elif 'data' in response_data:
                    if 'results' in response_data['data']:
                        items = response_data['data']['results']
                    elif 'items' in response_data['data']:
                        items = response_data['data']['items']
                    else:
                        items = response_data['data'] if isinstance(response_data['data'], list) else []
                elif 'results' in response_data:
                    items = response_data['results']
                elif 'items' in response_data:
                    items = response_data['items']
                elif isinstance(response_data, list):
                    items = response_data

                print(f"   ✅ 获取到 {len(items)} 个{scope}搜索结果")

                # 如果没有结果，打印响应内容用于调试
                if len(items) == 0:
                    print(f"   🔍 调试信息 - 完整响应: {json.dumps(response_data, ensure_ascii=False, indent=2)[:500]}...")

                for item in items:
                    result = {
                        'title': item.get('title', '') or item.get('name', ''),
                        'url': item.get('url', '') or item.get('link', ''),
                        'snippet': item.get('summary', '') or item.get('content', '') or item.get('snippet', '') or item.get('description', ''),
                        'source': f'metaso_{scope}',
                        'date': item.get('publishedDate', '') or item.get('date', '') or item.get('published', ''),
                        'score': item.get('score', 0) or item.get('relevance', 0)
                    }

                    # 学术搜索添加额外字段
                    if scope == 'scholar':
                        result['authors'] = item.get('authors', '') or item.get('author', '')
                        result['journal'] = item.get('journal', '') or item.get('venue', '')
                        result['citations'] = item.get('citations', 0) or item.get('citedBy', 0)

                    # 只添加有标题的结果
                    if result['title'].strip():
                        results.append(result)

                return results
            else:
                print(f"⚠️ Metaso搜索失败: HTTP {res.status}")
                print(f"   响应内容: {data.decode('utf-8')[:500]}...")
                return []

        except Exception as e:
            print(f"⚠️ Metaso搜索失败: {str(e)}")
            return []

    def _determine_search_scope(self, query):
        """智能判断使用网页还是学术搜索模式"""
        try:
            # 学术研究相关的关键词
            academic_keywords = [
                # 研究相关
                '研究', '论文', '学术', '期刊', '文献', '综述', 'research', 'paper', 'study', 'journal',
                'academic', 'literature', 'review', 'analysis', 'investigation', 'experiment',

                # 技术相关
                '技术', '工艺', '方法', '算法', '理论', '模型', 'technology', 'method', 'algorithm',
                'theory', 'model', 'approach', 'technique', 'process', 'mechanism',

                # 科学相关
                '科学', '科技', '创新', '发明', '专利', 'science', 'innovation', 'invention', 'patent',
                'scientific', 'breakthrough', 'discovery', 'development',

                # 学术机构和出版
                '大学', '学院', '研究所', '实验室', 'university', 'institute', 'laboratory', 'lab',
                'college', 'academia', 'publication', 'proceedings', 'conference',

                # 数据和分析
                '数据分析', '实验数据', '测试结果', '性能评估', 'data analysis', 'experimental',
                'performance', 'evaluation', 'measurement', 'testing', 'validation'
            ]

            # 转换为小写进行匹配
            query_lower = query.lower()

            # 计算学术关键词匹配度
            academic_score = sum(1 for keyword in academic_keywords if keyword in query_lower)

            # 如果匹配到学术关键词，使用学术搜索
            if academic_score > 0:
                print(f"   🎓 检测到学术内容关键词({academic_score}个)，使用学术搜索模式")
                return 'scholar'
            else:
                print(f"   🌐 未检测到学术关键词，使用网页搜索模式")
                return 'webpage'

        except Exception as e:
            print(f"   ⚠️ 搜索模式判断失败，默认使用网页模式: {str(e)}")
            return 'webpage'

    def multi_source_search(self, query, search_types=['metaso'], num_results=5):
        """多源搜索 - 智能选择搜索模式"""
        all_results = []

        for source in search_types:
            if source == 'metaso':
                # 智能判断使用网页还是学术搜索
                scope = self._determine_search_scope(query)
                results = self.search_metaso(query, scope, num_results)
            elif source == 'google':
                results = self.search_google(query, num_results)
            elif source == 'bing':
                results = self.search_bing(query, num_results)
            else:
                continue

            all_results.extend(results)

        # 去重和排序
        return self.merge_and_rank_results(all_results)

    def merge_and_rank_results(self, results):
        """合并和排序搜索结果"""
        # 简单去重（基于URL）
        seen_urls = set()
        unique_results = []

        for result in results:
            url = result.get('url', '')
            if url and url not in seen_urls:
                seen_urls.add(url)
                unique_results.append(result)

        # 按来源权重排序（Metaso > Google > Bing）
        def sort_key(result):
            source = result.get('source', '')
            if source.startswith('metaso'):
                weight = 3  # Metaso最高权重
            elif source == 'google':
                weight = 2
            elif source == 'bing':
                weight = 1
            else:
                weight = 0

            # 结合评分（如果有的话）- 修复类型错误
            score = result.get('score', 0)
            try:
                # 确保score是数字类型
                if isinstance(score, str):
                    score = float(score) if score.replace('.', '').isdigit() else 0
                elif not isinstance(score, (int, float)):
                    score = 0
            except (ValueError, AttributeError):
                score = 0

            return weight + score * 0.1

        unique_results.sort(key=sort_key, reverse=True)
        return unique_results

    def generate_search_queries(self, topic, content_gap):
        """生成优化的搜索查询"""
        base_queries = []

        if content_gap['type'] == 'latest_data':
            base_queries = [
                f"{topic} 2024年最新数据",
                f"{topic} 市场报告 2024",
                f"{topic} 行业分析 最新"
            ]
        elif content_gap['type'] == 'market_data':
            base_queries = [
                f"{topic} 市场规模",
                f"{topic} 竞争格局 主要企业",
                f"{topic} 投资 融资 数据"
            ]
        elif content_gap['type'] == 'technology':
            base_queries = [
                f"{topic} 技术发展 2024",
                f"{topic} 创新 突破",
                f"{topic} 技术趋势"
            ]
        elif content_gap['type'] == 'policy':
            base_queries = [
                f"{topic} 政策 法规",
                f"{topic} 标准 规范",
                f"{topic} 监管 指导"
            ]
        elif content_gap['type'] == 'cases':
            base_queries = [
                f"{topic} 成功案例",
                f"{topic} 项目 应用",
                f"{topic} 实践 经验"
            ]
        else:
            base_queries = [content_gap.get('query', topic)]

        return base_queries


class SearchToolManager:
    """搜索工具管理器 - 支持Gemini工具调用"""

    def __init__(self, generator):
        self.generator = generator
        self.search_manager = SearchManager(generator)

    def get_search_tools_definition(self):
        """获取搜索工具的定义，供Gemini使用"""
        tools = [
            {
                "name": "search_web_content",
                "description": "搜索最新的网页内容，获取实时信息、新闻、市场数据等",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "query": {
                            "type": "string",
                            "description": "搜索查询词，应该包含具体的关键词和主题"
                        },
                        "num_results": {
                            "type": "integer",
                            "description": "返回结果数量，默认5个",
                            "default": 10
                        }
                    },
                    "required": ["query"]
                }
            },
            {
                "name": "search_academic_papers",
                "description": "搜索学术论文和研究报告，获取前沿研究成果和技术发展",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "query": {
                            "type": "string",
                            "description": "学术搜索查询词，应该包含技术术语和研究领域"
                        },
                        "num_results": {
                            "type": "integer",
                            "description": "返回结果数量，默认5个",
                            "default": 10
                        }
                    },
                    "required": ["query"]
                }
            }
        ]
        return tools

    def execute_tool_call(self, tool_name: str, parameters: dict):
        """执行工具调用"""
        try:
            if tool_name == "search_web_content":
                return self._search_web_content(**parameters)
            elif tool_name == "search_academic_papers":
                return self._search_academic_papers(**parameters)
            else:
                return {"error": f"未知的工具: {tool_name}"}
        except Exception as e:
            return {"error": f"工具执行失败: {str(e)}"}

    def _search_web_content(self, query: str, num_results: int = 5):
        """搜索网页内容 - 使用metaso网页搜索"""
        try:
            print(f"🌐 执行网页搜索: {query}")
            results = self.search_manager.search_metaso(query, 'webpage', num_results)

            formatted_results = []
            for result in results:
                formatted_results.append({
                    "title": result.get('title', ''),
                    "url": result.get('url', ''),
                    "summary": result.get('snippet', ''),
                    "date": result.get('date', ''),
                    "source": "网页搜索(Metaso)",
                    "score": result.get('score', 0)
                })

            print(f"   ✅ 网页搜索完成，获得 {len(formatted_results)} 个结果")
            return {
                "success": True,
                "query": query,
                "search_mode": "webpage",
                "results_count": len(formatted_results),
                "results": formatted_results
            }
        except Exception as e:
            print(f"   ❌ 网页搜索失败: {str(e)}")
            return {"error": f"网页搜索失败: {str(e)}"}

    def _search_academic_papers(self, query: str, num_results: int = 5):
        """搜索学术论文 - 使用metaso学术搜索"""
        try:
            print(f"🎓 执行学术搜索: {query}")
            results = self.search_manager.search_metaso(query, 'scholar', num_results)

            formatted_results = []
            for result in results:
                formatted_results.append({
                    "title": result.get('title', ''),
                    "url": result.get('url', ''),
                    "summary": result.get('snippet', ''),
                    "date": result.get('date', ''),
                    "authors": result.get('authors', ''),
                    "journal": result.get('journal', ''),
                    "citations": result.get('citations', 0),
                    "source": "学术搜索(Metaso)",
                    "score": result.get('score', 0)
                })

            print(f"   ✅ 学术搜索完成，获得 {len(formatted_results)} 个结果")
            return {
                "success": True,
                "query": query,
                "search_mode": "scholar",
                "results_count": len(formatted_results),
                "results": formatted_results
            }
        except Exception as e:
            print(f"   ❌ 学术搜索失败: {str(e)}")
            return {"error": f"学术搜索失败: {str(e)}"}


class ContentValidator:
    """内容质量控制和验证"""

    def __init__(self, generator):
        self.generator = generator

    def validate_search_result(self, result, topic):
        """验证搜索结果的质量和相关性"""
        score = 0.0

        # 相关性评分 (40%)
        relevance = self.calculate_relevance(result.get('snippet', ''), topic)
        score += relevance * 0.4

        # 权威性评分 (30%)
        authority = self.evaluate_source_authority(result.get('url', ''))
        score += authority * 0.3

        # 时效性评分 (20%)
        freshness = self.evaluate_freshness(result.get('date', ''))
        score += freshness * 0.2

        # 内容质量评分 (10%)
        quality = self.evaluate_content_quality(result.get('snippet', ''))
        score += quality * 0.1

        return score

    def calculate_relevance(self, content, topic):
        """计算内容相关性"""
        if not content or not topic:
            return 0.0

        # 简单的关键词匹配算法
        topic_words = topic.lower().split()
        content_lower = content.lower()

        matches = sum(1 for word in topic_words if word in content_lower)
        return min(matches / len(topic_words), 1.0)

    def evaluate_source_authority(self, url):
        """评估来源权威性"""
        if not url:
            return 0.5

        # 权威域名列表
        authoritative_domains = [
            'gov.cn', 'edu.cn', 'org.cn', 'ac.cn',
            'gov', 'edu', 'org', 'mil',
            'ieee.org', 'acm.org', 'nature.com', 'science.org',
            'cnki.net', 'wanfangdata.com.cn'
        ]

        url_lower = url.lower()
        for domain in authoritative_domains:
            if domain in url_lower:
                return 1.0

        return 0.5

    def evaluate_freshness(self, date_str):
        """评估时效性"""
        if not date_str:
            return 0.5

        try:
            from datetime import datetime, timedelta
            import re

            # 尝试解析日期
            current_date = datetime.now()

            # 简单的日期匹配
            year_match = re.search(r'202[0-9]', date_str)
            if year_match:
                year = int(year_match.group())
                if year == current_date.year:
                    return 1.0
                elif year == current_date.year - 1:
                    return 0.8
                elif year >= current_date.year - 2:
                    return 0.6
                else:
                    return 0.3

            return 0.5
        except:
            return 0.5

    def evaluate_content_quality(self, content):
        """评估内容质量"""
        if not content:
            return 0.0

        # 基于内容长度和结构的简单评分
        length_score = min(len(content) / 200, 1.0)  # 200字符为满分
        
        # 检查是否包含有意义的信息
        meaningful_words = ['分析', '研究', '发展', '技术', '市场', '数据', '报告']
        meaningful_score = sum(1 for word in meaningful_words if word in content) / len(meaningful_words)

        return (length_score + meaningful_score) / 2