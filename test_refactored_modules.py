"""
测试重构后的模块是否正常工作
"""
import sys
from pathlib import Path

def test_imports():
    """测试所有模块是否可以正常导入"""
    print("🧪 测试模块导入...")
    
    try:
        # 测试配置模块
        from complete_report_generator_modules.config import (
            API_KEYS, MODEL_NAMES, GeminiModelConfig
        )
        print("✅ config.py 导入成功")
        
        # 测试Token管理器
        from complete_report_generator_modules.token_manager import TokenManager
        print("✅ token_manager.py 导入成功")
        
        # 测试图片匹配器
        from complete_report_generator_modules.image_matcher import ImageMatcher
        print("✅ image_matcher.py 导入成功")
        
        # 测试图表生成器
        from complete_report_generator_modules.chart_generator import ChartGenerator
        print("✅ chart_generator.py 导入成功")
        
        # 测试API管理器
        from complete_report_generator_modules.api_manager import (
            GeminiAPILimits, AsyncConfig, GeminiAPIManager
        )
        print("✅ api_manager.py 导入成功")
        
        # 测试搜索管理器
        from complete_report_generator_modules.search_manager import (
            SearchTrigger, SearchManager
        )
        print("✅ search_manager.py 导入成功")
        
        # 测试内容生成器
        from complete_report_generator_modules.content_generator import (
            StructuredContentGenerator, RobustContentCleaner
        )
        print("✅ content_generator.py 导入成功")
        
        # 测试工具模块
        from complete_report_generator_modules.utils import (
            ProgressTracker, ConfigManager
        )
        print("✅ utils.py 导入成功")
        
        # 测试主入口文件
        from complete_report_generator_new import CompleteReportGenerator
        print("✅ complete_report_generator_new.py 导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 未知错误: {str(e)}")
        return False


def test_basic_functionality():
    """测试基本功能是否正常"""
    print("\n🔧 测试基本功能...")
    
    try:
        # 测试配置管理
        from complete_report_generator_modules.config import GeminiModelConfig
        config = GeminiModelConfig()
        print("✅ GeminiModelConfig 创建成功")
        
        # 测试Token管理器
        from complete_report_generator_modules.token_manager import TokenManager
        token_mgr = TokenManager()
        test_text = "这是一个测试文本" * 100
        token_info = token_mgr.get_token_info(test_text)
        print(f"✅ TokenManager 工作正常 - 估算tokens: {token_info['estimated_tokens']}")
        
        # 测试图表生成器
        from complete_report_generator_modules.chart_generator import ChartGenerator
        chart_gen = ChartGenerator()
        print("✅ ChartGenerator 创建成功")
        
        # 测试工具函数
        from complete_report_generator_modules.utils import get_timestamp, ProgressTracker
        timestamp = get_timestamp()
        print(f"✅ 工具函数正常 - 时间戳: {timestamp}")
        
        # 测试进度跟踪器
        tracker = ProgressTracker(5, "测试进度")
        for i in range(5):
            tracker.update()
        tracker.finish("测试完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 功能测试失败: {str(e)}")
        return False


def test_main_generator():
    """测试主生成器是否可以创建"""
    print("\n🚀 测试主生成器...")
    
    try:
        from complete_report_generator_new import CompleteReportGenerator
        
        # 创建生成器实例（不使用异步，避免API调用）
        generator = CompleteReportGenerator(use_async=False)
        print("✅ CompleteReportGenerator 创建成功")
        
        # 测试配置方法
        generator.print_model_configs()
        print("✅ 配置方法正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 主生成器测试失败: {str(e)}")
        return False


def main():
    """主测试函数"""
    print("🧪 开始测试重构后的模块")
    print("=" * 50)
    
    # 检查文件结构
    modules_dir = Path("complete_report_generator_modules")
    if not modules_dir.exists():
        print("❌ complete_report_generator_modules 目录不存在")
        return
    
    required_files = [
        "__init__.py", "config.py", "token_manager.py", "image_matcher.py",
        "chart_generator.py", "api_manager.py", "search_manager.py",
        "content_generator.py", "utils.py"
    ]
    
    missing_files = []
    for file in required_files:
        if not (modules_dir / file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少文件: {', '.join(missing_files)}")
        return
    
    print("✅ 文件结构检查通过")
    
    # 运行测试
    tests = [
        ("模块导入测试", test_imports),
        ("基本功能测试", test_basic_functionality),
        ("主生成器测试", test_main_generator),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        if test_func():
            passed += 1
            print(f"✅ {test_name} 通过")
        else:
            print(f"❌ {test_name} 失败")
    
    # 总结
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！重构成功！")
        print("\n💡 使用方法:")
        print("   from complete_report_generator_new import CompleteReportGenerator")
        print("   generator = CompleteReportGenerator()")
    else:
        print("⚠️ 部分测试失败，请检查错误信息")


if __name__ == "__main__":
    main()