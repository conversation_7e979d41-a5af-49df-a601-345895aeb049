#!/usr/bin/env python3
"""
测试降级逻辑修复
验证5次失败后降级的逻辑是否正确工作
"""

import sys
import os
from pathlib import Path
from unittest.mock import patch, MagicMock

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_fallback_logic():
    """测试降级逻辑"""
    print("🧪 测试降级逻辑")
    print("=" * 50)
    
    try:
        from complete_report_generator import ModelFallbackManager
        
        # 创建降级管理器
        manager = ModelFallbackManager()
        
        print(f"📋 初始状态:")
        print(f"   最大重试次数: {manager.max_pro_retries}")
        
        # 测试任务ID
        task_id = "test_audit_task"
        
        print(f"\n🔄 测试5次失败后降级:")
        
        # 模拟5次失败
        for i in range(5):
            should_fallback = manager.record_pro_failure(task_id)
            current_model = manager.get_model_for_task(task_id, "gemini-2.5-pro")
            print(f"   第{i+1}次失败 - 应该降级: {should_fallback}, 当前模型: {current_model}")
        
        # 检查最终状态
        final_should_fallback = manager.should_use_fallback(task_id)
        final_model = manager.get_model_for_task(task_id, "gemini-2.5-pro")
        
        print(f"\n📊 最终状态:")
        print(f"   应该使用fallback: {final_should_fallback}")
        print(f"   推荐模型: {final_model}")
        
        # 验证结果
        if final_should_fallback and final_model == "gemini-2.5-flash":
            print(f"   ✅ 降级逻辑正确")
        else:
            print(f"   ❌ 降级逻辑错误")
            return False
        
        # 测试成功后重置
        print(f"\n🔄 测试成功重置:")
        manager.record_pro_success(task_id)
        
        reset_should_fallback = manager.should_use_fallback(task_id)
        reset_model = manager.get_model_for_task(task_id, "gemini-2.5-pro")
        
        print(f"   重置后应该使用fallback: {reset_should_fallback}")
        print(f"   重置后推荐模型: {reset_model}")
        
        if not reset_should_fallback and reset_model == "gemini-2.5-pro":
            print(f"   ✅ 重置逻辑正确")
        else:
            print(f"   ❌ 重置逻辑错误")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 降级逻辑测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_task_id_independence():
    """测试任务ID独立性"""
    print("\n🧪 测试任务ID独立性")
    print("=" * 50)
    
    try:
        from complete_report_generator import ModelFallbackManager
        
        # 创建降级管理器
        manager = ModelFallbackManager()
        
        # 创建两个不同的任务
        task_id_1 = "audit_task_1"
        task_id_2 = "audit_task_2"
        
        print(f"📋 测试两个独立任务:")
        print(f"   任务1: {task_id_1}")
        print(f"   任务2: {task_id_2}")
        
        # 任务1失败5次，触发降级
        print(f"\n🔄 任务1失败5次:")
        for i in range(5):
            manager.record_pro_failure(task_id_1)
        
        # 检查任务1和任务2的状态
        task1_should_fallback = manager.should_use_fallback(task_id_1)
        task1_model = manager.get_model_for_task(task_id_1, "gemini-2.5-pro")
        
        task2_should_fallback = manager.should_use_fallback(task_id_2)
        task2_model = manager.get_model_for_task(task_id_2, "gemini-2.5-pro")
        
        print(f"   任务1状态 - 降级: {task1_should_fallback}, 模型: {task1_model}")
        print(f"   任务2状态 - 降级: {task2_should_fallback}, 模型: {task2_model}")
        
        # 验证独立性
        if task1_should_fallback and task1_model == "gemini-2.5-flash" and \
           not task2_should_fallback and task2_model == "gemini-2.5-pro":
            print(f"   ✅ 任务ID独立性正确")
            return True
        else:
            print(f"   ❌ 任务ID独立性错误")
            return False
        
    except Exception as e:
        print(f"❌ 任务ID独立性测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_orchestrator_model_call():
    """测试统筹模型调用"""
    print("\n🧪 测试统筹模型调用")
    print("=" * 50)
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        # 创建报告生成器
        generator = CompleteReportGenerator(use_async=False)
        
        print(f"📋 模型配置:")
        print(f"   统筹模型: {generator.ORCHESTRATOR_MODEL}")
        print(f"   执行模型: {generator.EXECUTOR_MODEL}")
        
        # 检查降级管理器状态
        print(f"\n📊 降级管理器状态:")
        print(f"   活跃任务数: {len(generator.model_fallback_manager.pro_retry_count)}")
        print(f"   降级任务数: {len(generator.model_fallback_manager.fallback_active)}")
        
        # 模拟API管理器
        mock_api_manager = MagicMock()
        mock_response = MagicMock()
        mock_response.text = "测试响应内容"
        
        # 设置模拟返回值
        mock_api_manager.generate_content_with_model.return_value = (mock_response, 0)
        mock_api_manager.record_successful_processing.return_value = None
        
        # 替换API管理器
        original_api_manager = generator.api_manager
        generator.api_manager = mock_api_manager
        
        try:
            print(f"\n🔄 测试统筹模型调用:")
            
            # 调用统筹模型
            test_prompt = "测试prompt"
            response = generator.call_orchestrator_model(test_prompt)
            
            print(f"   响应: {response}")
            
            # 检查API调用
            if mock_api_manager.generate_content_with_model.called:
                call_args = mock_api_manager.generate_content_with_model.call_args
                used_model = call_args[0][1]  # 第二个参数是模型名
                print(f"   实际使用的模型: {used_model}")
                
                if used_model == generator.ORCHESTRATOR_MODEL:
                    print(f"   ✅ 正确使用统筹模型")
                    return True
                else:
                    print(f"   ❌ 错误使用了: {used_model}")
                    return False
            else:
                print(f"   ❌ API未被调用")
                return False
            
        finally:
            # 恢复原始API管理器
            generator.api_manager = original_api_manager
        
    except Exception as e:
        print(f"❌ 统筹模型调用测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_audit_method_integration():
    """测试审核方法集成"""
    print("\n🧪 测试审核方法集成")
    print("=" * 50)
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        # 创建报告生成器
        generator = CompleteReportGenerator(use_async=False)
        
        # 创建测试章节
        test_section = {
            "title": "市场概览与现状分析",
            "content": "这是一个测试章节的内容，用于验证审核功能。"
        }
        
        # 创建测试数据源
        test_data_source = "test_data"
        
        print(f"📋 测试审核方法:")
        print(f"   章节标题: {test_section['title']}")
        print(f"   数据源: {test_data_source}")
        
        # 模拟API管理器
        mock_api_manager = MagicMock()
        mock_response = MagicMock()
        mock_response.text = '{"overall_score": 8.5, "needs_optimization": false, "issues": [], "strengths": ["内容完整"]}'
        
        # 设置模拟返回值
        mock_api_manager.generate_content_with_model.return_value = (mock_response, 0)
        mock_api_manager.record_successful_processing.return_value = None
        
        # 替换API管理器
        original_api_manager = generator.api_manager
        generator.api_manager = mock_api_manager
        
        try:
            print(f"\n🔄 执行审核:")
            
            # 调用审核方法
            audit_result = generator._audit_section_with_orchestrator(
                test_section, test_data_source, 1
            )
            
            print(f"   审核结果: {audit_result}")
            
            # 检查API调用
            if mock_api_manager.generate_content_with_model.called:
                call_args = mock_api_manager.generate_content_with_model.call_args
                used_model = call_args[0][1]  # 第二个参数是模型名
                print(f"   实际使用的模型: {used_model}")
                
                if used_model == generator.ORCHESTRATOR_MODEL:
                    print(f"   ✅ 审核方法正确使用统筹模型")
                    return True
                else:
                    print(f"   ❌ 审核方法错误使用了: {used_model}")
                    return False
            else:
                print(f"   ❌ 审核方法未调用API")
                return False
            
        finally:
            # 恢复原始API管理器
            generator.api_manager = original_api_manager
        
    except Exception as e:
        print(f"❌ 审核方法集成测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 降级逻辑修复测试")
    print("=" * 80)
    
    print("\n📋 测试内容:")
    print("1. 降级逻辑测试")
    print("2. 任务ID独立性测试")
    print("3. 统筹模型调用测试")
    print("4. 审核方法集成测试")
    
    # 测试1：降级逻辑
    test1_success = test_fallback_logic()
    
    # 测试2：任务ID独立性
    test2_success = test_task_id_independence()
    
    # 测试3：统筹模型调用
    test3_success = test_orchestrator_model_call()
    
    # 测试4：审核方法集成
    test4_success = test_audit_method_integration()
    
    # 总结
    all_tests_passed = all([test1_success, test2_success, test3_success, test4_success])
    
    print(f"\n📊 测试结果总结:")
    print(f"   降级逻辑: {'✅ 通过' if test1_success else '❌ 失败'}")
    print(f"   任务ID独立性: {'✅ 通过' if test2_success else '❌ 失败'}")
    print(f"   统筹模型调用: {'✅ 通过' if test3_success else '❌ 失败'}")
    print(f"   审核方法集成: {'✅ 通过' if test4_success else '❌ 失败'}")
    
    if all_tests_passed:
        print("\n🎉 所有测试通过！")
        print("✅ 降级逻辑正确工作")
        print("✅ 5次失败后才降级到gemini-2.5-flash")
        print("✅ 任务ID独立管理")
        print("✅ 统筹任务正确使用gemini-2.5-pro")
    else:
        print("\n⚠️ 部分测试失败")
    
    print("\n📋 降级逻辑说明:")
    print("1. ✅ 每个任务独立跟踪重试次数")
    print("2. ✅ gemini-2.5-pro失败5次后才降级到gemini-2.5-flash")
    print("3. ✅ 成功调用后自动重置重试计数")
    print("4. ✅ 不同任务的降级状态互不影响")
    print("5. ✅ 统筹任务（审核、优化、框架生成）使用正确的降级逻辑")
    
    print("\n🎯 修复重点:")
    print("修复前：降级逻辑被错误触发，统筹任务过早降级")
    print("修复后：严格按照5次失败后降级的逻辑执行")
