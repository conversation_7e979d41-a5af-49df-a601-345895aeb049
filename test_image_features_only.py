#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
图片嵌入功能单独测试脚本
专门测试图片匹配和嵌入功能，不依赖完整的报告生成
"""

import os
import sys
from pathlib import Path
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

from complete_report_generator import CompleteReportGenerator

def create_test_environment():
    """创建测试环境"""
    print("🔧 创建测试环境...")
    
    # 创建测试目录
    test_dir = Path("test_image_features")
    test_dir.mkdir(exist_ok=True)
    
    # 创建数据源目录
    data_dir = test_dir / "data_sources"
    data_dir.mkdir(exist_ok=True)
    
    # 创建测试图片目录
    images_dir = data_dir / "images"
    images_dir.mkdir(exist_ok=True)
    
    # 创建模拟图片文件（空文件，仅用于测试文件名匹配）
    test_images = [
        "market_analysis_chart.png",
        "technology_trend_graph.jpg",
        "competition_comparison.png",
        "data_statistics.jpg",
        "process_flow_diagram.png",
        "market_share_pie_chart.png"
    ]
    
    for img_name in test_images:
        img_path = images_dir / img_name
        if not img_path.exists():
            img_path.touch()
    
    print(f"✅ 测试环境创建完成: {test_dir}")
    return test_dir, data_dir, images_dir

def test_image_keyword_extraction():
    """测试图片关键词提取功能"""
    print("\n🔍 测试图片关键词提取功能")
    print("-" * 40)
    
    try:
        generator = CompleteReportGenerator()
        
        test_filenames = [
            "market_analysis_chart.png",
            "technology_trend_graph.jpg",
            "competition_comparison.png",
            "data_statistics.jpg"
        ]
        
        for filename in test_filenames:
            keywords = generator._extract_image_keywords(filename)
            description = generator._extract_image_description(Path(filename))
            
            print(f"📄 文件: {filename}")
            print(f"   关键词: {keywords}")
            print(f"   描述: {description}")
        
        print("✅ 图片关键词提取测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 图片关键词提取测试失败: {str(e)}")
        return False

def test_node_image_relevance():
    """测试节点与图片相关性计算"""
    print("\n🎯 测试节点与图片相关性计算")
    print("-" * 40)
    
    try:
        generator = CompleteReportGenerator()
        
        # 测试节点
        test_nodes = [
            {
                'title': '市场分析',
                'content': '人工智能市场在过去几年中经历了快速增长。根据最新数据显示，全球AI市场规模已达到500亿美元。',
                'level': 1
            },
            {
                'title': '技术趋势',
                'content': '机器学习和深度学习技术不断进步，推动了AI应用的广泛普及。',
                'level': 1
            },
            {
                'title': '竞争格局',
                'content': '当前AI市场的主要参与者包括科技巨头和创新型企业。',
                'level': 1
            }
        ]
        
        # 测试图片
        test_images = [
            {
                'filename': 'market_analysis_chart.png',
                'description': '市场分析图表',
                'path': 'test_image_features/data_sources/images/market_analysis_chart.png'
            },
            {
                'filename': 'technology_trend_graph.jpg',
                'description': '技术趋势图',
                'path': 'test_image_features/data_sources/images/technology_trend_graph.jpg'
            },
            {
                'filename': 'competition_comparison.png',
                'description': '竞争对比图',
                'path': 'test_image_features/data_sources/images/competition_comparison.png'
            }
        ]
        
        print("📊 相关性计算结果:")
        for node in test_nodes:
            print(f"\n🔸 节点: {node['title']}")
            
            for img in test_images:
                node_text = node['title'] + ' ' + node['content']
                relevance = generator._calculate_image_node_relevance(
                    node_text.lower(), img, node['level']
                )
                
                print(f"   📷 {img['filename']}: {relevance:.3f}")
        
        print("\n✅ 节点图片相关性计算测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 节点图片相关性计算测试失败: {str(e)}")
        return False

def test_document_node_extraction():
    """测试文档节点提取功能"""
    print("\n📄 测试文档节点提取功能")
    print("-" * 40)
    
    try:
        generator = CompleteReportGenerator()
        test_dir, data_dir, images_dir = create_test_environment()
        
        # 创建测试Markdown文档
        test_md = test_dir / "test_document.md"
        md_content = """# AI市场分析报告

## 市场概况
人工智能市场在过去几年中经历了快速增长。

### 市场规模
全球AI市场规模已达到500亿美元。

## 技术趋势
机器学习和深度学习技术不断进步。

### 核心技术
- 自然语言处理
- 计算机视觉
- 强化学习

## 竞争格局
当前AI市场的主要参与者包括科技巨头。

### 主要企业
- Google
- Microsoft
- Amazon
"""
        
        with open(test_md, 'w', encoding='utf-8') as f:
            f.write(md_content)
        
        # 测试节点提取
        nodes = generator._extract_document_nodes(str(test_md))
        
        print(f"📊 提取到 {len(nodes)} 个节点:")
        for i, node in enumerate(nodes, 1):
            print(f"   {i}. 级别{node['level']}: {node['title']}")
            print(f"      内容长度: {len(node['content'])} 字符")
        
        print("✅ 文档节点提取测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 文档节点提取测试失败: {str(e)}")
        return False

def test_enhanced_image_matching():
    """测试增强的图片匹配算法"""
    print("\n🚀 测试增强的图片匹配算法")
    print("-" * 40)
    
    try:
        generator = CompleteReportGenerator()
        
        # 创建测试数据
        test_images = [
            {
                'filename': 'market_analysis_chart.png',
                'description': '市场分析图表',
                'filename_keywords': ['market', 'analysis', 'chart'],
                'image_type': 'chart',
                'importance_score': 0.8
            },
            {
                'filename': 'tech_trend_graph.jpg',
                'description': '技术趋势图',
                'filename_keywords': ['tech', 'trend', 'graph'],
                'image_type': 'trend',
                'importance_score': 0.7
            },
            {
                'filename': 'competition_data.png',
                'description': '竞争数据图',
                'filename_keywords': ['competition', 'data'],
                'image_type': 'data',
                'importance_score': 0.6
            }
        ]
        
        test_report_analysis = {
            'keywords': ['market', 'analysis', 'technology', 'trend', 'ai', 'competition'],
            'sections': ['市场概况', '技术趋势', '竞争分析'],
            'data_points': ['35%', '500亿美元', '1000亿美元']
        }
        
        test_context = {
            'topic': 'AI市场分析报告',
            'report_type': '技术分析报告'
        }
        
        # 测试增强匹配算法
        gemini_matcher = generator.GeminiImageMatcher(generator)
        
        print("🎯 多维度匹配结果:")
        for img_info in test_images:
            match_result = gemini_matcher._calculate_multi_dimensional_match(
                img_info, test_report_analysis, test_context
            )
            
            print(f"\n📷 图片: {img_info['filename']}")
            print(f"   📊 总得分: {match_result['overall_score']:.3f}")
            print(f"   🔍 语义得分: {match_result['semantic_score']:.3f}")
            print(f"   🔑 关键词得分: {match_result['keyword_score']:.3f}")
            print(f"   🌐 上下文得分: {match_result['context_score']:.3f}")
            print(f"   ⭐ 重要性得分: {match_result['importance_score']:.3f}")
            print(f"   🎯 置信度: {match_result['match_details']['confidence_level']}")
            print(f"   🏷️ 主要匹配类型: {match_result['match_details']['primary_match_type']}")
            print(f"   📍 建议位置: {match_result['match_details']['recommended_position']}")
        
        print("\n✅ 增强图片匹配算法测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 增强图片匹配算法测试失败: {str(e)}")
        return False

def test_image_data_collection():
    """测试图片数据收集功能"""
    print("\n📁 测试图片数据收集功能")
    print("-" * 40)
    
    try:
        generator = CompleteReportGenerator()
        test_dir, data_dir, images_dir = create_test_environment()
        
        # 测试图片数据收集
        all_image_data = generator.collect_all_image_data([str(data_dir)])
        
        print(f"📊 收集结果:")
        if all_image_data:
            for source, data in all_image_data.items():
                print(f"   📂 数据源: {source}")
                if isinstance(data, dict) and 'images' in data:
                    print(f"      图片数量: {len(data['images'])}")
                    for img in data['images'][:3]:  # 只显示前3个
                        print(f"         📷 {img.get('filename', '未知')}")
                else:
                    print(f"      数据类型: {type(data)}")
        else:
            print("   ⚠️ 未收集到图片数据")
        
        print("✅ 图片数据收集测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 图片数据收集测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🧪 图片嵌入功能单独测试")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tests = [
        ("图片关键词提取", test_image_keyword_extraction),
        ("节点图片相关性计算", test_node_image_relevance),
        ("文档节点提取", test_document_node_extraction),
        ("增强图片匹配算法", test_enhanced_image_matching),
        ("图片数据收集", test_image_data_collection)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name}测试异常: {str(e)}")
            results[test_name] = False
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    passed_count = 0
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed_count += 1
    
    print(f"\n总体结果: {passed_count}/{len(tests)} 项测试通过")
    
    if passed_count == len(tests):
        print("🎉 所有图片功能测试通过！")
    elif passed_count >= len(tests) * 0.8:
        print("✅ 大部分图片功能正常工作")
    else:
        print("⚠️ 需要进一步检查和优化")

if __name__ == "__main__":
    main()
