#!/usr/bin/env python3
"""
测试预定义框架使用时的None值错误修复
验证不再出现TypeError: unsupported operand type(s) for +: 'NoneType' and 'int'
"""

def test_none_value_handling():
    """测试None值处理修复"""
    print("🔧 测试None值处理修复")
    print("=" * 60)
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        generator = CompleteReportGenerator(use_async=False)
        print("✅ 生成器创建成功")
        
        # 测试1: _get_smart_framework_template方法的None值处理
        print(f"\n📝 测试1: 智能框架模板的None值处理")
        
        # 测试传入None值
        try:
            template = generator._get_smart_framework_template(None, None)
            print(f"✅ None值处理成功，生成了模板")
            if template and "sections" in template:
                print(f"   模板包含 {len(template['sections'])} 个章节")
            else:
                print(f"❌ 模板格式不正确")
                return False
        except Exception as e:
            print(f"❌ None值处理失败: {str(e)}")
            return False
        
        # 测试2: 预定义框架转换
        print(f"\n📝 测试2: 预定义框架转换")
        
        # 模拟预定义框架
        predefined_framework = {
            "reportTitle": "测试报告",
            "framework": [
                {
                    "part": "I",
                    "sections": [
                        {"title": "章节1", "content": []},
                        {"title": "章节2", "content": []}
                    ]
                }
            ]
        }
        
        try:
            converted = generator._convert_predefined_framework_to_sections(predefined_framework)
            if converted and "sections" in converted:
                print(f"✅ 预定义框架转换成功")
                print(f"   转换后包含 {len(converted['sections'])} 个章节")
            else:
                print(f"❌ 预定义框架转换失败")
                return False
        except Exception as e:
            print(f"❌ 预定义框架转换异常: {str(e)}")
            return False
        
        # 测试3: 框架验证的None值处理
        print(f"\n📝 测试3: 框架验证的None值处理")
        
        # 模拟report_config中有None值的情况
        original_config = generator.report_config.copy()
        generator.report_config["primary_sections"] = None
        generator.report_config["max_depth"] = None
        
        try:
            test_framework = {
                "sections": [
                    {"title": "章节1"},
                    {"title": "章节2"}
                ]
            }
            
            result = generator._validate_and_fix_framework(test_framework)
            if result:
                print(f"✅ None值配置下框架验证成功")
            else:
                print(f"❌ None值配置下框架验证失败")
                return False
        except Exception as e:
            print(f"❌ 框架验证异常: {str(e)}")
            return False
        finally:
            # 恢复原始配置
            generator.report_config = original_config
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_framework_loading_logic():
    """测试框架加载逻辑"""
    print(f"\n🔍 测试框架加载逻辑")
    print("=" * 50)
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        generator = CompleteReportGenerator(use_async=False)
        
        # 测试预定义框架加载
        print(f"📖 测试预定义框架加载...")
        
        framework_path = "核电框架.json"
        predefined_framework = generator._load_predefined_framework(framework_path)
        
        if predefined_framework:
            print(f"✅ 预定义框架加载成功")
            
            # 测试转换
            converted = generator._convert_predefined_framework_to_sections(predefined_framework)
            if converted:
                print(f"✅ 框架转换成功")
                print(f"   包含 {len(converted['sections'])} 个章节")
                
                # 测试验证
                # 临时设置None配置来模拟预定义框架场景
                original_config = generator.report_config.copy()
                generator.report_config["primary_sections"] = None
                generator.report_config["max_depth"] = None
                
                try:
                    validated = generator._validate_and_fix_framework(converted)
                    if validated:
                        print(f"✅ 框架验证成功")
                        return True
                    else:
                        print(f"❌ 框架验证失败")
                        return False
                finally:
                    generator.report_config = original_config
            else:
                print(f"❌ 框架转换失败")
                return False
        else:
            print(f"❌ 预定义框架加载失败")
            return False
            
    except Exception as e:
        print(f"❌ 框架加载逻辑测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🧪 预定义框架None值错误修复验证")
    print("=" * 80)
    
    # 测试1: None值处理
    none_handling_ok = test_none_value_handling()
    
    # 测试2: 框架加载逻辑
    loading_logic_ok = test_framework_loading_logic()
    
    print("\n" + "=" * 80)
    print("📋 修复验证总结:")
    print(f"   None值处理: {'✅ 通过' if none_handling_ok else '❌ 失败'}")
    print(f"   框架加载逻辑: {'✅ 通过' if loading_logic_ok else '❌ 失败'}")
    
    if none_handling_ok and loading_logic_ok:
        print("\n🎉 预定义框架None值错误修复成功！")
        print("\n💡 修复内容:")
        print("   ✅ _get_smart_framework_template方法增加None值检查")
        print("   ✅ 框架验证逻辑支持None配置（预定义框架模式）")
        print("   ✅ 添加预定义框架转换方法")
        print("   ✅ 修改主流程，预定义框架时跳过AI生成")
        print("   ✅ 智能框架模板备用方案增加None值保护")
        
        print("\n🚀 修复效果:")
        print("   - 不再出现 'NoneType' and 'int' 错误")
        print("   - 预定义框架直接使用，不走AI生成流程")
        print("   - 框架验证适配预定义框架场景")
        print("   - 所有None值都有适当的默认值处理")
        
        print("\n📝 现在的流程:")
        print("   1. 检测到预定义框架 → 直接加载")
        print("   2. 转换为标准sections格式")
        print("   3. 跳过AI框架生成步骤")
        print("   4. 直接进入任务指导生成")
    else:
        print("\n⚠️ 修复不完整，需要进一步检查")

if __name__ == "__main__":
    main()
