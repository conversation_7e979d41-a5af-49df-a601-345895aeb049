#!/usr/bin/env python3
"""
提取API管理器相关的所有类
"""

def extract_api_classes():
    with open('complete_report_generator.py', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 找到需要提取的类
    classes_to_extract = [
        'GeminiAPILimits',
        'AsyncConfig', 
        'BaseGeminiAPIManager',
        'GeminiAPIManager'
    ]
    
    extracted_content = '''"""
API管理模块 - Gemini API管理器
从complete_report_generator.py中提取的完整API管理类
"""
import time
import threading
from typing import Dict, Any, List, Tuple
import google.generativeai as genai

# 导入配置模块
from config import API_KEYS, GeminiModelConfig


'''
    
    # 提取每个类
    for class_name in classes_to_extract:
        print(f"Extracting {class_name}...")
        
        # 找到类的开始
        start_line = None
        for i, line in enumerate(lines):
            if f'class {class_name}' in line and ':' in line:
                start_line = i
                break
        
        if start_line is None:
            print(f"Could not find {class_name}")
            continue
        
        # 找到类的结束
        end_line = None
        for i in range(start_line + 1, len(lines)):
            line = lines[i]
            # 如果遇到下一个类定义或者文件结束
            if (line.startswith('class ') and ':' in line) or i == len(lines) - 1:
                end_line = i
                break
            # 如果遇到同级别的函数定义（不在类内部）
            elif line.startswith('def ') and not line.startswith('    def'):
                end_line = i
                break
        
        if end_line is None:
            end_line = len(lines)
        
        # 提取类内容
        class_lines = lines[start_line:end_line]
        extracted_content += ''.join(class_lines) + '\n\n'
        print(f"Extracted {class_name} ({len(class_lines)} lines)")
    
    # 写入文件
    with open('complete_report_generator_modules_final/api_manager.py', 'w', encoding='utf-8') as f:
        f.write(extracted_content)
    
    print("Successfully created api_manager.py")

if __name__ == "__main__":
    extract_api_classes()