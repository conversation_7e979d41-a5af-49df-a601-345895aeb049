#!/usr/bin/env python3
"""
测试修复后的图表生成功能
"""

import sys
import warnings
from pathlib import Path

# 禁用matplotlib字体警告
warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')

def test_chart_generation_fixes():
    """测试修复后的图表生成功能"""
    print("🧪 测试修复后的图表生成功能...")
    
    try:
        # 导入修复后的ChartGenerator
        from complete_report_generator import ChartGenerator, MATPLOTLIB_AVAILABLE, PLOTLY_AVAILABLE
        
        print(f"📊 MATPLOTLIB_AVAILABLE: {MATPLOTLIB_AVAILABLE}")
        print(f"📊 PLOTLY_AVAILABLE: {PLOTLY_AVAILABLE}")
        
        if not MATPLOTLIB_AVAILABLE:
            print("❌ matplotlib不可用，无法测试图表生成")
            return False
        
        # 创建图表生成器实例
        chart_generator = ChartGenerator(Path("test_charts_fixed"))
        print("✅ ChartGenerator实例创建成功")
        
        # 测试产业链图表生成
        print("\n📊 测试1: 产业链图表生成...")
        chart_path = chart_generator.generate_industry_chain_chart("地热发电", {})
        if chart_path and Path(chart_path).exists():
            size_kb = Path(chart_path).stat().st_size / 1024
            print(f"   ✅ 产业链图表生成成功: {chart_path} ({size_kb:.1f} KB)")
        else:
            print("   ❌ 产业链图表生成失败")
            return False
        
        # 测试市场规模图表生成
        print("\n📈 测试2: 市场规模图表生成...")
        chart_path = chart_generator.generate_market_size_chart("地热发电", {})
        if chart_path and Path(chart_path).exists():
            size_kb = Path(chart_path).stat().st_size / 1024
            print(f"   ✅ 市场规模图表生成成功: {chart_path} ({size_kb:.1f} KB)")
        else:
            print("   ❌ 市场规模图表生成失败")
            return False
        
        # 测试技术趋势图表生成
        print("\n🔬 测试3: 技术趋势图表生成...")
        chart_path = chart_generator.generate_technology_trend_chart("地热发电", {})
        if chart_path and Path(chart_path).exists():
            size_kb = Path(chart_path).stat().st_size / 1024
            print(f"   ✅ 技术趋势图表生成成功: {chart_path} ({size_kb:.1f} KB)")
        else:
            print("   ❌ 技术趋势图表生成失败")
            return False
        
        # 测试英文主题（测试字体回退功能）
        print("\n🌍 测试4: 英文主题图表生成...")
        chart_path = chart_generator.generate_industry_chain_chart("Geothermal Energy", {})
        if chart_path and Path(chart_path).exists():
            size_kb = Path(chart_path).stat().st_size / 1024
            print(f"   ✅ 英文主题图表生成成功: {chart_path} ({size_kb:.1f} KB)")
        else:
            print("   ❌ 英文主题图表生成失败")
            return False
        
        # 列出所有生成的图表
        chart_dir = Path("test_charts_fixed")
        if chart_dir.exists():
            chart_files = list(chart_dir.glob("*.png"))
            print(f"\n📁 共生成 {len(chart_files)} 个图表文件:")
            for chart_file in chart_files:
                size_kb = chart_file.stat().st_size / 1024
                print(f"   - {chart_file.name} ({size_kb:.1f} KB)")
        
        return True
        
    except Exception as e:
        print(f"❌ 图表生成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_font_handling():
    """测试字体处理功能"""
    print("\n🔤 测试字体处理功能...")
    
    try:
        from complete_report_generator import ChartGenerator
        
        # 创建图表生成器并检查字体设置
        chart_generator = ChartGenerator(Path("test_charts_font"))
        
        # 检查中文字体可用性
        has_chinese = hasattr(chart_generator, 'chinese_font_available') and chart_generator.chinese_font_available
        print(f"   中文字体可用: {has_chinese}")
        
        # 测试字体确保功能
        font_result = chart_generator._ensure_chinese_font()
        print(f"   字体确保结果: {font_result}")
        
        return True
        
    except Exception as e:
        print(f"❌ 字体处理测试失败: {e}")
        return False

def test_error_handling():
    """测试错误处理"""
    print("\n⚠️ 测试错误处理...")
    
    try:
        from complete_report_generator import ChartGenerator
        
        # 创建图表生成器
        chart_generator = ChartGenerator(Path("test_charts_error"))
        
        # 测试空主题
        chart_path = chart_generator.generate_industry_chain_chart("", {})
        print(f"   空主题测试: {'通过' if isinstance(chart_path, str) else '失败'}")
        
        # 测试特殊字符主题
        chart_path = chart_generator.generate_market_size_chart("测试/特殊\\字符", {})
        print(f"   特殊字符测试: {'通过' if isinstance(chart_path, str) else '失败'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试修复后的图表生成功能...")
    print("=" * 60)
    
    tests = [
        ("图表生成功能测试", test_chart_generation_fixes),
        ("字体处理功能测试", test_font_handling),
        ("错误处理测试", test_error_handling),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 40)
        try:
            if test_func():
                print(f"✅ {test_name} 通过")
                passed += 1
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！图表生成功能修复成功！")
        print("\n🔧 修复内容总结:")
        print("   ✅ 修复了中文字体设置逻辑")
        print("   ✅ 添加了英文标题备用方案")
        print("   ✅ 改进了字体警告处理")
        print("   ✅ 增强了错误处理机制")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
