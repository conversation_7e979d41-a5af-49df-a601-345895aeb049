#!/usr/bin/env python3
"""
测试增强的思考过程清理功能
验证五步优化的效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from complete_report_generator import CompleteReportGenerator

def test_enhanced_cleaning():
    """测试增强的清理功能"""
    
    # 创建生成器实例
    generator = CompleteReportGenerator()
    
    # 测试内容 - 包含您提到的具体问题
    test_content = """
地热能作为清洁可再生能源，具有重要的战略价值。

原有的技术介绍内容将被剥离，并构建一个以市场数据和战略分析为核心的全新框架。

以下是优化后的完整章节内容：

优化后的内容严格遵循了参考报告的专业标准和风格，具体体现在：

1. **重构章节结构**：为了逻辑更清晰、内容更完整，将原内容拆分为四个子章节。

2. **强化问题导入（解决"深度分析"问题）**：在章节中，紧随地热能价值论述之后，精准切入其传统发展瓶颈。

优化后的版本在结构上采用了"总-分"模式，将核心观点前置；在风格上强化了数据驱动和客观陈述；在专业性上则力求分析更深入、术语更精准。

写作风格 (严谨、客观、逻辑性) 得到了显著提升。

**标题概括性**：采用了更加精准的标题表述。
**引用规范**：严格按照学术标准进行引用。
**善用类比**：通过恰当的类比增强理解。
**语言精炼**：去除冗余表述，提高信息密度。
**图文表结合**：合理配置图表支撑论述。
**段落清晰**：每个段落主题明确，逻辑清晰。
**漏斗式逻辑**：从宏观到微观，层层递进。

{ "sections": [ { "title": "概述与背景", "level": 1, "children": [ {"title": "研究背景", "level": 2}, {"title": "研究目的", "level": 2} ] } ] }

地热发电技术的发展前景广阔，市场潜力巨大。

优化后的章节内容更加专业和完整。
"""
    
    print("🧪 测试增强的思考过程清理功能")
    print("=" * 60)
    print(f"📝 原始内容长度: {len(test_content)} 字符")
    print(f"📝 原始内容预览:")
    print(test_content[:200] + "...")
    print()
    
    # 执行清理
    print("🧹 开始执行增强清理...")
    cleaned_content = generator._remove_thinking_process(test_content)
    
    print(f"✅ 清理完成")
    print(f"🧹 清理后内容长度: {len(cleaned_content)} 字符")
    print(f"📊 清理率: {((len(test_content) - len(cleaned_content)) / len(test_content) * 100):.1f}%")
    print()
    print(f"🧹 清理后内容:")
    print("-" * 40)
    print(cleaned_content)
    print("-" * 40)
    print()
    
    # 验证清理效果
    print("🔍 验证清理效果...")
    problematic_phrases = [
        "优化后的版本",
        "写作风格 (严谨、客观、逻辑性)",
        "结构上采用了",
        "风格上强化了",
        "专业性上则",
        "原有的",
        "内容将被剥离",
        "以下是优化后的完整章节内容：",
        "优化后的内容严格遵循",
        "具体体现在：",
        "**标题概括性**",
        "**引用规范**",
        "**善用类比**",
        "**语言精炼**",
        "**图文表结合**",
        "**段落清晰**",
        "**漏斗式逻辑**",
        '"sections":',
        '{ "title":',
        '"level": 1',
        '"level": 2',
        "采用了\"总-分\"模式",
        "将核心观点前置",
        "力求分析更深入",
        "术语更精准"
    ]
    
    remaining_issues = [phrase for phrase in problematic_phrases if phrase in cleaned_content]
    
    if remaining_issues:
        print("❌ 仍存在问题短语:")
        for issue in remaining_issues:
            print(f"   - {issue}")
        print(f"📊 清理成功率: {((len(problematic_phrases) - len(remaining_issues)) / len(problematic_phrases) * 100):.1f}%")
    else:
        print("✅ 所有问题短语已清理")
        print("📊 清理成功率: 100%")
    
    print()
    print("🎯 测试总结:")
    print(f"   原始长度: {len(test_content)} 字符")
    print(f"   清理后长度: {len(cleaned_content)} 字符")
    print(f"   内容压缩率: {((len(test_content) - len(cleaned_content)) / len(test_content) * 100):.1f}%")
    print(f"   问题清理率: {((len(problematic_phrases) - len(remaining_issues)) / len(problematic_phrases) * 100):.1f}%")
    
    if len(remaining_issues) == 0:
        print("🎉 测试通过！所有问题内容已成功清理")
    else:
        print(f"⚠️ 测试部分通过，仍有 {len(remaining_issues)} 个问题需要解决")
    
    return len(remaining_issues) == 0

if __name__ == "__main__":
    success = test_enhanced_cleaning()
    sys.exit(0 if success else 1)
