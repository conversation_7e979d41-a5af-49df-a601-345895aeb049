#!/usr/bin/env python3
"""
测试checkpoint功能修复
验证checkpoint保存和恢复是否正常工作
"""

import sys
import os
import json
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from complete_report_generator import CompleteReportGenerator

def test_checkpoint_data_structure():
    """测试checkpoint数据结构的一致性"""
    print("🔧 测试checkpoint数据结构...")
    
    try:
        generator = CompleteReportGenerator(use_async=False)
        
        # 测试数据
        test_data = {
            "framework": {"title": "测试报告", "sections": []},
            "topic": "人工智能",
            "data_sources": ["test.txt"],
            "framework_file_path": "test_framework.json",
            "total_nodes": 5,
            "sections": [],
            "completed_iterations": 1
        }
        
        # 测试不同阶段的checkpoint保存
        stages = [
            "framework_generated",
            "task_instructions_generated", 
            "content_generated",
            "optimization_round_1",
            "word_count_controlled",
            "report_completed"
        ]
        
        checkpoint_ids = []
        
        for stage in stages:
            print(f"📝 测试保存 {stage} checkpoint...")
            
            # 为不同阶段准备适当的数据
            stage_data = test_data.copy()
            if stage == "report_completed":
                stage_data["output_path"] = "test_report.docx"
                stage_data["completed"] = True
            
            checkpoint_id = generator.create_checkpoint(stage, stage_data)
            
            if checkpoint_id:
                checkpoint_ids.append(checkpoint_id)
                print(f"   ✅ {stage} checkpoint保存成功: {checkpoint_id}")
                
                # 验证checkpoint数据
                if generator.validate_checkpoint_data(stage_data, stage):
                    print(f"   ✅ {stage} 数据结构验证通过")
                else:
                    print(f"   ❌ {stage} 数据结构验证失败")
            else:
                print(f"   ❌ {stage} checkpoint保存失败")
                return False
        
        print(f"\n✅ 所有checkpoint保存测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ checkpoint数据结构测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 清理测试checkpoint
        try:
            for checkpoint_id in checkpoint_ids:
                checkpoint_file = generator.checkpoint_dir / f"{checkpoint_id}.json"
                if checkpoint_file.exists():
                    checkpoint_file.unlink()
                    print(f"🗑️ 清理测试checkpoint: {checkpoint_id}")
        except:
            pass

def test_checkpoint_recovery():
    """测试checkpoint恢复功能"""
    print("\n🔧 测试checkpoint恢复功能...")
    
    try:
        generator = CompleteReportGenerator(use_async=False)
        
        # 创建一个测试checkpoint
        test_data = {
            "framework": {
                "title": "测试报告",
                "sections": [
                    {"title": "第一章", "content": "测试内容1"},
                    {"title": "第二章", "content": "测试内容2"}
                ]
            },
            "topic": "人工智能测试",
            "data_sources": ["test.txt"],
            "framework_file_path": "test_framework.json",
            "total_nodes": 2,
            "sections": [
                {"title": "第一章", "content": "测试内容1"},
                {"title": "第二章", "content": "测试内容2"}
            ],
            "completed_iterations": 2
        }
        
        # 保存测试checkpoint
        checkpoint_id = generator.create_checkpoint("content_generated", test_data)
        
        if not checkpoint_id:
            print("❌ 无法创建测试checkpoint")
            return False
        
        print(f"✅ 创建测试checkpoint: {checkpoint_id}")
        
        # 测试checkpoint恢复
        print("🔄 测试checkpoint恢复...")
        
        # 创建新的生成器实例来模拟恢复
        new_generator = CompleteReportGenerator(use_async=False)
        
        try:
            # 尝试恢复checkpoint
            result = new_generator._resume_from_checkpoint(
                checkpoint_id, 
                "人工智能测试", 
                ["test.txt"]
            )
            
            if result:
                print(f"✅ Checkpoint恢复成功: {result}")
                return True
            else:
                print("❌ Checkpoint恢复失败：返回空结果")
                return False
                
        except Exception as e:
            print(f"❌ Checkpoint恢复失败: {str(e)}")
            return False
        finally:
            # 清理测试checkpoint
            try:
                checkpoint_file = generator.checkpoint_dir / f"{checkpoint_id}.json"
                if checkpoint_file.exists():
                    checkpoint_file.unlink()
                    print(f"🗑️ 清理测试checkpoint: {checkpoint_id}")
            except:
                pass
        
    except Exception as e:
        print(f"❌ checkpoint恢复测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 开始测试checkpoint功能修复...")
    
    # 测试checkpoint数据结构
    structure_test = test_checkpoint_data_structure()
    
    if structure_test:
        # 测试checkpoint恢复
        recovery_test = test_checkpoint_recovery()
        
        if recovery_test:
            print("\n🎉 所有checkpoint测试通过！")
            print("✅ checkpoint保存和恢复功能已修复")
        else:
            print("\n⚠️ checkpoint恢复测试失败")
    else:
        print("\n❌ checkpoint数据结构测试失败")
    
    print("\n📋 修复总结:")
    print("1. 修复了checkpoint数据结构不一致的问题")
    print("2. 修复了恢复时变量引用错误的问题")
    print("3. 统一了所有阶段的checkpoint数据格式")
    print("4. 添加了checkpoint数据验证功能")
    print("5. 增加了详细的调试信息")
    print("\n🔧 修复的关键问题:")
    print("- 修复了self.checkpoint_data.get()应该是checkpoint_data.get()的错误")
    print("- 修复了不同阶段checkpoint数据结构不一致的问题")
    print("- 修复了恢复时缺少必要字段的问题")
    print("- 添加了数据完整性验证机制")
