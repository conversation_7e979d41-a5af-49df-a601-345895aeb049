# 如何运行重构后的代码

## 🎯 概述

重构后的代码已经修复了所有语法错误，现在可以正常运行。代码被分解为多个模块，提供了更好的可维护性和扩展性。

## 📁 文件结构

```
complete_report_generator_modules_final/
├── __init__.py                 # 模块初始化文件
├── main.py                     # 主入口文件
├── config.py                   # 配置管理
├── api_manager.py              # API管理器
├── async_api_manager.py        # 异步API管理器
├── token_manager.py            # Token管理
├── chart_generator.py          # 图表生成
├── content_generator.py        # 内容生成
├── search_manager.py           # 搜索管理
├── report_generator.py         # 报告生成器
└── utils.py                    # 工具函数
```

## 🔧 环境准备

### 1. 安装必需依赖

```bash
pip install google-generativeai
```

### 2. 安装可选依赖（推荐）

```bash
# 图表生成功能
pip install matplotlib plotly

# PDF和图片处理功能
pip install PyPDF2 Pillow

# 进度条显示
pip install tqdm
```

### 3. 配置API密钥

编辑 `complete_report_generator_modules_final/config.py` 文件：

```python
# 将这些替换为你的真实API密钥
API_KEYS = [
    "你的第一个Gemini API密钥",
    "你的第二个Gemini API密钥",  # 可选，多个密钥可以提高并发性能
    # 添加更多密钥...
]
```

## 🚀 运行方法

### 方法1: 运行主程序（推荐）

```bash
cd complete_report_generator_modules_final
python main.py
```

### 方法2: 直接运行工具函数

```bash
cd complete_report_generator_modules_final
python utils.py
```

### 方法3: 作为模块导入使用

```python
import sys
sys.path.append('complete_report_generator_modules_final')

from report_generator import CompleteReportGenerator
from config import GeminiModelConfig

# 创建报告生成器
generator = CompleteReportGenerator(use_async=True)

# 生成报告
output_path = generator.generate_report(
    topic="你的报告主题",
    data_sources=["data/source1", "data/source2"],
    framework_file="templates/framework.md"
)
```

## 📋 运行流程

1. **环境检查**: 程序会自动检查依赖和配置
2. **用户输入**: 输入报告主题和参数
3. **数据准备**: 自动创建必要的目录和示例数据
4. **模式选择**: 选择异步并行模式或同步模式
5. **报告生成**: 开始生成报告
6. **输出结果**: 生成的报告保存在 `output/` 目录

## ⚙️ 配置选项

### 异步模式 vs 同步模式

- **异步模式（推荐）**: 使用多个API密钥并行处理，速度更快
- **同步模式**: 传统的顺序处理模式，更稳定

### 报告参数

- **主题**: 报告的主要研究主题
- **章节数量**: 一级章节的数量（默认8个）
- **最大深度**: 章节嵌套的最大深度（默认6级）
- **目标字数**: 报告的目标字数（默认50000字）
- **最大Token**: 单次API调用的最大Token数（默认250000）

## 🔍 故障排除

### 常见问题

1. **API密钥错误**
   - 检查 `config.py` 中的API密钥是否正确
   - 确保API密钥有足够的配额

2. **模块导入错误**
   - 确保在正确的目录下运行
   - 检查Python路径设置

3. **依赖缺失**
   - 运行 `pip install -r requirements.txt`（如果有的话）
   - 手动安装缺失的依赖

### 调试模式

如果遇到问题，可以启用详细日志：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📊 性能优化

### 多API密钥配置

配置多个API密钥可以显著提高性能：

```python
API_KEYS = [
    "key1",
    "key2", 
    "key3",  # 3个密钥可以实现3倍并发
]
```

### 异步模式优势

- 并发处理多个章节
- 智能配额管理
- 自动错误恢复
- 预计2-3倍性能提升

## 📝 输出文件

生成的报告将保存在以下位置：

- **主报告**: `output/complete_report_YYYYMMDD_HHMMSS.md`
- **图表文件**: `output/charts/`
- **日志文件**: `logs/`

## 🎉 成功运行的标志

当你看到以下输出时，说明代码运行成功：

```
🤖 完整版AI报告生成器
============================================================
🔧 智能异步API管理器初始化完成:
   可用API密钥: X 个
   每密钥并发: 1
   全局最大并发: X
   最小请求间隔: 5.0秒
   配额监控: 启用

📊 性能配置:
   预计总调用: XXX 次
   预计加速: 保守估计2-3x（智能限速）

🎉 报告生成成功!
📄 输出文件: output/complete_report_YYYYMMDD_HHMMSS.md
```

## 💡 提示

1. 首次运行建议使用较小的参数进行测试
2. 确保网络连接稳定
3. 监控API配额使用情况
4. 定期备份生成的报告

现在你可以开始使用重构后的代码了！