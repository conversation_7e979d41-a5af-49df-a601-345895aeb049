"""
自动提取剩余类的脚本
"""
import re
from pathlib import Path

def find_all_classes(file_path):
    """找到文件中所有的类定义"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 找到所有类定义
    class_pattern = r'^class\s+(\w+).*?:'
    classes = []
    
    lines = content.split('\n')
    for i, line in enumerate(lines):
        if re.match(class_pattern, line):
            class_name = re.match(class_pattern, line).group(1)
            classes.append({
                'name': class_name,
                'start_line': i + 1,
                'line_content': line
            })
    
    return classes

def extract_class_content(file_path, class_name, start_line):
    """提取类的完整内容"""
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 找到类的结束位置
    class_lines = []
    indent_level = None
    
    for i in range(start_line - 1, len(lines)):
        line = lines[i]
        
        # 确定缩进级别
        if indent_level is None and line.strip():
            if line.startswith('class '):
                indent_level = len(line) - len(line.lstrip())
            
        # 如果是空行或注释，继续
        if not line.strip() or line.strip().startswith('#'):
            class_lines.append(line)
            continue
            
        # 检查是否还在类内部
        current_indent = len(line) - len(line.lstrip())
        
        # 如果遇到同级别或更高级别的定义，停止
        if (line.strip() and 
            current_indent <= indent_level and 
            i > start_line - 1 and
            (line.strip().startswith('class ') or 
             line.strip().startswith('def ') or
             line.strip().startswith('if __name__'))):
            break
            
        class_lines.append(line)
    
    return ''.join(class_lines)

# 执行提取
if __name__ == "__main__":
    file_path = "complete_report_generator.py"
    classes = find_all_classes(file_path)
    
    print("找到的所有类:")
    for cls in classes:
        print(f"  {cls['name']} (第{cls['start_line']}行)")
    
    # 提取每个类的内容
    for cls in classes:
        if cls['name'] not in ['GeminiModelConfig', 'TokenManager']:  # 已经提取的跳过
            print(f"\n提取类: {cls['name']}")
            content = extract_class_content(file_path, cls['name'], cls['start_line'])
            print(f"内容长度: {len(content)} 字符")
            print(f"前100字符: {content[:100]}")