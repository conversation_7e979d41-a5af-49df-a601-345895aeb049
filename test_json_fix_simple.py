#!/usr/bin/env python3
"""
简单测试JSON修复
"""

import json
import re

def test_direct_fix():
    # 测试问题JSON - 根据实际错误构造
    problem_json = '''{
    "instructions": {
        "节点001": {
            "title": "1. 总论：核电产业概览与研究界定",
            "content_requirements": "
            "word_count": "80-100字"
        }
    }
}'''
    
    print("🔍 原始问题JSON:")
    print(problem_json)
    print()
    
    # 直接修复方案
    print("🔧 应用直接修复:")
    
    # 方案1: 简单替换
    fixed1 = problem_json.replace('"content_requirements": "\n            "word_count":', 
                                  '"content_requirements": "",\n            "word_count":')
    
    print("修复方案1:")
    print(fixed1)
    print()
    
    try:
        result1 = json.loads(fixed1)
        print("✅ 方案1解析成功!")
        print(f"指导数量: {len(result1.get('instructions', {}))}")
    except Exception as e:
        print(f"❌ 方案1解析失败: {e}")
    
    print()
    
    # 方案2: 正则表达式修复
    pattern = r'("content_requirements"):\s*"\s*("word_count"):'
    fixed2 = re.sub(pattern, r'\1: "",\n            \2:', problem_json)
    
    print("修复方案2:")
    print(fixed2)
    print()
    
    try:
        result2 = json.loads(fixed2)
        print("✅ 方案2解析成功!")
        print(f"指导数量: {len(result2.get('instructions', {}))}")
    except Exception as e:
        print(f"❌ 方案2解析失败: {e}")

if __name__ == "__main__":
    test_direct_fix()
