#!/usr/bin/env python3
"""
测试用户配置驱动的智能框架系统
验证系统是否根据用户输入的参数动态生成框架
"""

import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from complete_report_generator import CompleteReportGenerator

def test_user_config_6_sections_5_depth():
    """测试用户配置：6个一级标题，5层深度"""
    print("🧪 测试用户配置：6个一级标题，5层深度")
    print("=" * 60)
    
    # 创建生成器并设置用户配置
    generator = CompleteReportGenerator(use_async=False)
    
    # 模拟用户输入的配置
    generator.report_config.update({
        "primary_sections": 6,  # 一级标题数量
        "max_depth": 5,         # 最大层级深度
        "target_words": 50000   # 目标字数
    })
    
    print(f"📊 用户配置:")
    print(f"   • 一级标题数量: {generator.report_config['primary_sections']}")
    print(f"   • 最大层级深度: {generator.report_config['max_depth']}")
    print(f"   • 目标字数: {generator.report_config['target_words']:,} 字")
    
    # 生成智能框架模板
    print("\n🏗️ 生成智能框架模板...")
    template = generator._get_smart_framework_template(
        max_sections=6,
        max_depth=5
    )
    
    # 统计模板结构
    sections = template.get("sections", [])
    active_sections = [s for s in sections if s.get("active", False)]
    
    print(f"\n📊 模板统计:")
    print(f"   • 总一级标题: {len(sections)} 个")
    print(f"   • 激活的一级标题: {len(active_sections)} 个")
    print(f"   • 支持的最大深度: {sections[0].get('max_supported_depth', 'N/A')} 层")
    
    # 显示激活的标题
    print(f"\n📋 激活的一级标题:")
    for i, section in enumerate(active_sections, 1):
        title = section.get("title", "")
        print(f"   {i}. {title}")
    
    return template

def test_ai_framework_matching_with_user_config():
    """测试AI框架匹配（使用用户配置）"""
    print("\n🧪 测试AI框架匹配（使用用户配置）")
    print("=" * 60)
    
    # 创建生成器并设置用户配置
    generator = CompleteReportGenerator(use_async=False)
    
    # 模拟用户输入的配置
    generator.report_config.update({
        "primary_sections": 6,  # 一级标题数量
        "max_depth": 5,         # 最大层级深度
        "target_words": 50000   # 目标字数
    })
    
    # 模拟AI生成的框架（包含5层深度）
    ai_framework = {
        "sections": [
            {
                "title": "1. 核电产业概览与发展现状",
                "level": 1,
                "children": [
                    {
                        "title": "1.1. 核电技术基础",
                        "level": 2,
                        "children": [
                            {
                                "title": "1.1.1. 核裂变原理",
                                "level": 3,
                                "children": [
                                    {
                                        "title": "1.1.1.1. 原子核结构",
                                        "level": 4,
                                        "children": [
                                            {
                                                "title": "1.1.1.1.1. 质子与中子",
                                                "level": 5,
                                                "children": []
                                            }
                                        ]
                                    }
                                ]
                            }
                        ]
                    }
                ]
            },
            {
                "title": "2. 核反应堆技术深度解析",
                "level": 1,
                "children": [
                    {
                        "title": "2.1. 压水堆技术",
                        "level": 2,
                        "children": []
                    }
                ]
            },
            {
                "title": "3. 全球核电市场分析",
                "level": 1,
                "children": []
            }
        ]
    }
    
    topic = "核电产业研究报告"
    
    print(f"📋 模拟AI生成的框架 (主题: {topic}):")
    print(f"   • AI生成了 {len(ai_framework['sections'])} 个一级标题")
    print(f"   • 包含最深 5 层的嵌套结构")
    
    print("\n🔄 使用智能框架系统处理...")
    smart_framework = generator.create_smart_framework_from_ai_response(ai_framework, topic)
    
    print("\n✅ AI框架匹配完成！")
    return smart_framework

def test_different_user_configs():
    """测试不同的用户配置"""
    print("\n🧪 测试不同的用户配置")
    print("=" * 60)
    
    test_configs = [
        {"primary_sections": 4, "max_depth": 3, "target_words": 20000},
        {"primary_sections": 8, "max_depth": 6, "target_words": 80000},
        {"primary_sections": 10, "max_depth": 4, "target_words": 100000},
    ]
    
    for i, config in enumerate(test_configs, 1):
        print(f"\n{i}. 测试配置 {i}:")
        print(f"   • 一级标题数量: {config['primary_sections']}")
        print(f"   • 最大层级深度: {config['max_depth']}")
        print(f"   • 目标字数: {config['target_words']:,} 字")
        
        # 创建生成器并设置配置
        generator = CompleteReportGenerator(use_async=False)
        generator.report_config.update(config)
        
        # 生成模板
        template = generator._get_smart_framework_template(
            max_sections=config['primary_sections'],
            max_depth=config['max_depth']
        )
        
        # 统计结果
        sections = template.get("sections", [])
        active_sections = [s for s in sections if s.get("active", False)]
        
        print(f"   结果: 生成了{len(active_sections)}个激活的一级标题")

def main():
    """主测试函数"""
    print("🎯 用户配置驱动的智能框架系统测试")
    print("=" * 80)
    print("📋 验证系统是否根据用户输入参数动态生成框架")
    print("🔧 测试场景: 6个一级标题，5层深度，50000字")
    print("=" * 80)
    
    try:
        # 测试1: 用户配置的模板生成
        template = test_user_config_6_sections_5_depth()
        
        # 测试2: AI框架匹配（使用用户配置）
        if template:
            smart_framework = test_ai_framework_matching_with_user_config()
        
        # 测试3: 不同用户配置
        test_different_user_configs()
        
        print("\n" + "=" * 80)
        print("✅ 用户配置驱动的智能框架系统测试完成！")
        print("💡 系统已成功根据用户输入参数动态生成框架")
        print("🎯 支持灵活的一级标题数量和层级深度配置")
        print("📊 完全按照用户需求执行，不再使用固定结构")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
