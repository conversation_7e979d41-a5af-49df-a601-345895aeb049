#!/usr/bin/env python3
"""
演示修复后的数据源配置
展示新的单一数据源路径配置方式
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def create_demo_data():
    """创建演示数据"""
    print("🔧 创建演示数据...")
    
    demo_dir = Path("demo_data_source")
    demo_dir.mkdir(exist_ok=True)
    
    # 创建不同类型的文档，模拟真实场景
    demo_files = [
        {
            "name": "industry_overview.md",
            "content": """# 人工智能行业概述

## 行业发展历程
人工智能从1956年达特茅斯会议开始，经历了多次发展浪潮。

## 当前市场状况
全球AI市场规模持续增长，预计2024年将达到新的高度。

## 主要参与者
- OpenAI: GPT系列模型
- Google: Gemini和Bard
- Microsoft: Copilot系列
- 百度: 文心一言
"""
        },
        {
            "name": "technical_deep_dive.txt",
            "content": """技术深度分析

核心技术栈：
1. 大语言模型 (LLM)
   - Transformer架构
   - 注意力机制
   - 预训练和微调

2. 多模态AI
   - 视觉-语言模型
   - 音频处理
   - 跨模态理解

3. 推理和规划
   - 思维链推理
   - 工具使用能力
   - 代码生成

技术趋势：
- 模型规模持续增长
- 效率优化成为关键
- 专用硬件加速发展
"""
        },
        {
            "name": "market_data.csv",
            "content": """年份,全球市场规模(亿美元),增长率(%),主要应用领域
2020,1500,15.2,语音识别
2021,1800,20.0,图像识别
2022,2200,22.2,自然语言处理
2023,2800,27.3,生成式AI
2024,3500,25.0,多模态AI
2025,4200,20.0,AGI初步应用
"""
        },
        {
            "name": "investment_analysis.json",
            "content": """{
  "investment_overview": {
    "total_funding_2023": "250亿美元",
    "major_rounds": [
      {
        "company": "OpenAI",
        "amount": "100亿美元",
        "investor": "Microsoft"
      },
      {
        "company": "Anthropic", 
        "amount": "45亿美元",
        "investor": "Google"
      }
    ]
  },
  "market_segments": {
    "enterprise_ai": "40%",
    "consumer_ai": "35%", 
    "infrastructure": "25%"
  },
  "growth_projections": {
    "2024": "30%增长",
    "2025": "25%增长",
    "2026": "20%增长"
  }
}"""
        },
        {
            "name": "future_trends.docx",
            "content": """未来发展趋势报告

1. 技术发展方向
   1.1 通用人工智能(AGI)
   - 多领域推理能力
   - 自主学习和适应
   - 人机协作模式

   1.2 效率优化
   - 模型压缩技术
   - 边缘计算部署
   - 绿色AI发展

2. 应用场景扩展
   2.1 垂直行业深化
   - 医疗诊断辅助
   - 金融风险控制
   - 教育个性化

   2.2 新兴应用领域
   - 科学研究加速
   - 创意内容生成
   - 智能制造升级

3. 挑战与机遇
   3.1 技术挑战
   - 可解释性问题
   - 安全性保障
   - 伦理道德考量

   3.2 发展机遇
   - 政策支持加强
   - 基础设施完善
   - 人才培养体系
"""
        },
        {
            "name": "regulatory_landscape.pdf",
            "content": """监管环境分析

全球AI监管现状：

美国：
- 国家AI倡议
- NIST AI风险管理框架
- 各州级别的AI法规

欧盟：
- AI法案(AI Act)
- GDPR数据保护
- 数字服务法案

中国：
- AI治理原则
- 算法推荐管理规定
- 深度合成规定

监管趋势：
1. 风险分级管理
2. 透明度要求提升
3. 跨国协调加强
4. 行业自律推进
"""
        }
    ]
    
    created_files = []
    for file_info in demo_files:
        file_path = demo_dir / file_info["name"]
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(file_info["content"])
        created_files.append(str(file_path))
        print(f"   📄 创建: {file_info['name']}")
    
    print(f"✅ 创建了 {len(created_files)} 个演示文档")
    return demo_dir, created_files

def demo_old_vs_new_config():
    """演示旧配置vs新配置的对比"""
    print("\n📋 配置方式对比")
    print("=" * 60)
    
    print("🔴 旧的配置方式:")
    print("   用户需要输入:")
    print("   - 第1个数据源路径: data/1_market_overview")
    print("   - 第2个数据源路径: data/2_technology_trends") 
    print("   - 第3个数据源路径: data/3_competitive_landscape")
    print("   - 第4个数据源路径: data/4_regulatory_environment")
    print("   - 第5个数据源路径: data/5_investment_analysis")
    print("   - 第6个数据源路径: data/6_future_outlook")
    print("   - 第7个数据源路径: data/7_challenges")
    print("   - 第8个数据源路径: data/8_recommendations")
    print("   ❌ 问题：需要手动为每个章节准备对应的文档")
    
    print("\n🟢 新的配置方式:")
    print("   用户只需输入:")
    print("   - 数据源路径: demo_data_source")
    print("   ✅ 优势：系统自动根据报告框架智能分类文档")

def demo_intelligent_classification():
    """演示智能分类功能"""
    print("\n🧠 智能分类演示")
    print("=" * 60)
    
    try:
        # 创建演示数据
        demo_dir, created_files = create_demo_data()
        
        from complete_report_generator import CompleteReportGenerator
        
        # 创建报告生成器
        generator = CompleteReportGenerator(use_async=False)
        
        print(f"\n📁 数据源路径: {demo_dir}")
        print(f"📄 包含文档: {len(created_files)} 个")
        
        # 定义报告框架
        framework_sections = [
            {"title": "行业概述", "subsections": [{"title": "发展历程"}, {"title": "市场现状"}]},
            {"title": "技术分析", "subsections": [{"title": "核心技术"}, {"title": "技术趋势"}]},
            {"title": "市场研究", "subsections": [{"title": "市场规模"}, {"title": "增长预测"}]},
            {"title": "投资分析", "subsections": [{"title": "融资情况"}, {"title": "投资趋势"}]},
            {"title": "监管环境", "subsections": [{"title": "政策法规"}, {"title": "合规要求"}]},
            {"title": "发展前景", "subsections": [{"title": "未来趋势"}, {"title": "机遇挑战"}]}
        ]
        
        print(f"\n📋 报告框架 ({len(framework_sections)} 个章节):")
        for i, section in enumerate(framework_sections, 1):
            print(f"   {i}. {section['title']}")
        
        # 执行智能分类
        print(f"\n🤖 执行智能文档分类...")
        classified_path = generator.classify_documents_by_framework(str(demo_dir), framework_sections)
        
        print(f"✅ 分类完成: {classified_path}")
        
        # 展示分类结果
        classified_dir = Path(classified_path)
        if classified_dir.exists():
            print(f"\n📂 分类结果:")
            total_classified_files = 0
            
            for section in framework_sections:
                title = section['title']
                section_folder = classified_dir / generator._sanitize_folder_name(title)
                
                if section_folder.exists():
                    files = list(section_folder.glob("*"))
                    total_classified_files += len(files)
                    print(f"   📁 {title}: {len(files)} 个文件")
                    
                    for file_path in files:
                        original_name = file_path.name.split('_')[0] + '.' + file_path.suffix[1:]
                        print(f"      📄 {original_name} → {file_path.name}")
                else:
                    print(f"   📁 {title}: 0 个文件")
            
            print(f"\n📊 分类统计:")
            print(f"   原始文档: {len(created_files)} 个")
            print(f"   分类后文档: {total_classified_files} 个")
            print(f"   分类效率: {total_classified_files/len(created_files)*100:.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ 智能分类演示失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 清理演示文件
        try:
            import shutil
            if demo_dir.exists():
                shutil.rmtree(demo_dir)
                print(f"\n🗑️ 清理演示目录: {demo_dir}")
            
            classified_dir = Path(f"{demo_dir}_classified")
            if classified_dir.exists():
                shutil.rmtree(classified_dir)
                print(f"🗑️ 清理分类目录: {classified_dir}")
        except:
            pass

if __name__ == "__main__":
    print("🚀 数据源配置修复演示")
    print("=" * 80)
    
    print("📋 演示内容:")
    print("1. 配置方式对比（旧 vs 新）")
    print("2. 智能文档分类演示")
    print("3. 实际使用效果展示")
    
    # 演示1：配置对比
    demo_old_vs_new_config()
    
    # 演示2：智能分类
    classification_success = demo_intelligent_classification()
    
    if classification_success:
        print("\n🎉 演示完成！")
        print("✅ 数据源配置已成功修复")
        print("✅ 智能文档分类功能正常工作")
    else:
        print("\n⚠️ 演示过程中出现问题")
    
    print("\n📋 修复总结:")
    print("1. ✅ 用户体验大幅改善：从输入8个路径简化为1个路径")
    print("2. ✅ 智能化程度提升：AI自动理解文档内容并分类")
    print("3. ✅ 灵活性增强：支持任意数量的章节和文档")
    print("4. ✅ 准确性保证：基于内容相关性而非文件名进行分类")
    print("5. ✅ 容错能力强：支持多章节匹配和fallback机制")
    
    print("\n🎯 实际使用:")
    print("现在用户只需要：")
    print("1. 将所有相关文档放在一个文件夹中")
    print("2. 输入这个文件夹的路径")
    print("3. 系统自动根据报告框架智能分类文档")
    print("4. 开始生成高质量的报告")
