#!/usr/bin/env python3
"""
代码对比分析脚本 - 检查重构前后的区别
"""

import os
import re
from pathlib import Path
from typing import Dict, List, Set, Tuple

def extract_classes_from_file(file_path: str) -> Dict[str, Dict]:
    """从文件中提取所有类定义"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        print(f"❌ 无法读取文件 {file_path}: {str(e)}")
        return {}
    
    classes = {}
    lines = content.split('\n')
    
    for i, line in enumerate(lines):
        # 匹配类定义
        class_match = re.match(r'^class\s+(\w+).*:', line.strip())
        if class_match:
            class_name = class_match.group(1)
            
            # 提取类的文档字符串
            docstring = ""
            if i + 1 < len(lines) and '"""' in lines[i + 1]:
                for j in range(i + 1, min(i + 10, len(lines))):
                    if '"""' in lines[j]:
                        docstring = lines[j].strip().replace('"""', '').strip()
                        break
            
            classes[class_name] = {
                'line': i + 1,
                'docstring': docstring,
                'methods': []
            }
    
    return classes

def extract_functions_from_file(file_path: str) -> Dict[str, Dict]:
    """从文件中提取所有函数定义"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        print(f"❌ 无法读取文件 {file_path}: {str(e)}")
        return {}
    
    functions = {}
    lines = content.split('\n')
    
    for i, line in enumerate(lines):
        # 匹配函数定义（不在类内部）
        if re.match(r'^def\s+(\w+).*:', line.strip()):
            func_match = re.match(r'^def\s+(\w+).*:', line.strip())
            if func_match:
                func_name = func_match.group(1)
                functions[func_name] = {
                    'line': i + 1,
                    'signature': line.strip()
                }
    
    return functions

def extract_constants_from_file(file_path: str) -> Dict[str, any]:
    """从文件中提取常量定义"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        print(f"❌ 无法读取文件 {file_path}: {str(e)}")
        return {}
    
    constants = {}
    lines = content.split('\n')
    
    for i, line in enumerate(lines):
        # 匹配常量定义（全大写变量）
        const_match = re.match(r'^([A-Z_][A-Z0-9_]*)\s*=', line.strip())
        if const_match:
            const_name = const_match.group(1)
            constants[const_name] = {
                'line': i + 1,
                'definition': line.strip()
            }
    
    return constants

def analyze_original_file():
    """分析原始文件"""
    print("🔍 分析原始文件...")
    original_file = "complete_report_generator.py"
    
    if not Path(original_file).exists():
        print(f"❌ 原始文件不存在: {original_file}")
        return {}
    
    # 获取文件统计信息
    with open(original_file, 'r', encoding='utf-8') as f:
        content = f.read()
        lines = content.split('\n')
    
    original_analysis = {
        'file_stats': {
            'total_lines': len(lines),
            'total_chars': len(content),
            'file_size': Path(original_file).stat().st_size
        },
        'classes': extract_classes_from_file(original_file),
        'functions': extract_functions_from_file(original_file),
        'constants': extract_constants_from_file(original_file)
    }
    
    print(f"   📊 原始文件统计:")
    print(f"      总行数: {original_analysis['file_stats']['total_lines']:,}")
    print(f"      总字符数: {original_analysis['file_stats']['total_chars']:,}")
    print(f"      类数量: {len(original_analysis['classes'])}")
    print(f"      函数数量: {len(original_analysis['functions'])}")
    print(f"      常量数量: {len(original_analysis['constants'])}")
    
    return original_analysis

def analyze_refactored_modules():
    """分析重构后的模块"""
    print("\n🔍 分析重构后的模块...")
    modules_dir = Path("complete_report_generator_modules_final")
    
    if not modules_dir.exists():
        print(f"❌ 重构模块目录不存在: {modules_dir}")
        return {}
    
    refactored_analysis = {
        'modules': {},
        'total_stats': {
            'total_lines': 0,
            'total_chars': 0,
            'total_files': 0
        },
        'all_classes': {},
        'all_functions': {},
        'all_constants': {}
    }
    
    for py_file in modules_dir.glob("*.py"):
        if py_file.name == "complete_report_generator_backup.py":
            continue
            
        module_name = py_file.stem
        
        try:
            with open(py_file, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
            
            module_analysis = {
                'file_stats': {
                    'lines': len(lines),
                    'chars': len(content),
                    'file_size': py_file.stat().st_size
                },
                'classes': extract_classes_from_file(str(py_file)),
                'functions': extract_functions_from_file(str(py_file)),
                'constants': extract_constants_from_file(str(py_file))
            }
            
            refactored_analysis['modules'][module_name] = module_analysis
            
            # 累计统计
            refactored_analysis['total_stats']['total_lines'] += module_analysis['file_stats']['lines']
            refactored_analysis['total_stats']['total_chars'] += module_analysis['file_stats']['chars']
            refactored_analysis['total_stats']['total_files'] += 1
            
            # 合并所有类、函数、常量
            refactored_analysis['all_classes'].update(module_analysis['classes'])
            refactored_analysis['all_functions'].update(module_analysis['functions'])
            refactored_analysis['all_constants'].update(module_analysis['constants'])
            
            print(f"   📦 {module_name:<20} {module_analysis['file_stats']['lines']:>6} 行  {len(module_analysis['classes']):>2} 类  {len(module_analysis['functions']):>2} 函数")
            
        except Exception as e:
            print(f"   ❌ 分析失败 {module_name}: {str(e)}")
    
    print(f"\n   📊 重构后总计:")
    print(f"      模块数量: {refactored_analysis['total_stats']['total_files']}")
    print(f"      总行数: {refactored_analysis['total_stats']['total_lines']:,}")
    print(f"      总字符数: {refactored_analysis['total_stats']['total_chars']:,}")
    print(f"      类数量: {len(refactored_analysis['all_classes'])}")
    print(f"      函数数量: {len(refactored_analysis['all_functions'])}")
    print(f"      常量数量: {len(refactored_analysis['all_constants'])}")
    
    return refactored_analysis

def compare_classes(original_classes: Dict, refactored_classes: Dict):
    """对比类的差异"""
    print("\n🔍 类对比分析:")
    print("=" * 60)
    
    original_set = set(original_classes.keys())
    refactored_set = set(refactored_classes.keys())
    
    # 找出缺失的类
    missing_classes = original_set - refactored_set
    # 找出新增的类
    new_classes = refactored_set - original_set
    # 找出共同的类
    common_classes = original_set & refactored_set
    
    print(f"📊 类统计:")
    print(f"   原始文件类数量: {len(original_classes)}")
    print(f"   重构后类数量: {len(refactored_classes)}")
    print(f"   共同类数量: {len(common_classes)}")
    print(f"   缺失类数量: {len(missing_classes)}")
    print(f"   新增类数量: {len(new_classes)}")
    
    if missing_classes:
        print(f"\n❌ 缺失的类:")
        for class_name in sorted(missing_classes):
            print(f"   - {class_name}")
    
    if new_classes:
        print(f"\n➕ 新增的类:")
        for class_name in sorted(new_classes):
            print(f"   + {class_name}")
    
    if common_classes:
        print(f"\n✅ 成功保留的类:")
        for class_name in sorted(common_classes):
            print(f"   ✓ {class_name}")
    
    return {
        'missing': missing_classes,
        'new': new_classes,
        'common': common_classes
    }

def compare_functions(original_functions: Dict, refactored_functions: Dict):
    """对比函数的差异"""
    print("\n🔍 函数对比分析:")
    print("=" * 60)
    
    original_set = set(original_functions.keys())
    refactored_set = set(refactored_functions.keys())
    
    # 找出缺失的函数
    missing_functions = original_set - refactored_set
    # 找出新增的函数
    new_functions = refactored_set - original_set
    # 找出共同的函数
    common_functions = original_set & refactored_set
    
    print(f"📊 函数统计:")
    print(f"   原始文件函数数量: {len(original_functions)}")
    print(f"   重构后函数数量: {len(refactored_functions)}")
    print(f"   共同函数数量: {len(common_functions)}")
    print(f"   缺失函数数量: {len(missing_functions)}")
    print(f"   新增函数数量: {len(new_functions)}")
    
    if missing_functions:
        print(f"\n❌ 缺失的函数:")
        for func_name in sorted(missing_functions):
            print(f"   - {func_name}")
    
    if new_functions:
        print(f"\n➕ 新增的函数:")
        for func_name in sorted(new_functions):
            print(f"   + {func_name}")
    
    if common_functions:
        print(f"\n✅ 成功保留的函数 (前10个):")
        for func_name in sorted(common_functions)[:10]:
            print(f"   ✓ {func_name}")
        if len(common_functions) > 10:
            print(f"   ... 还有 {len(common_functions) - 10} 个函数")
    
    return {
        'missing': missing_functions,
        'new': new_functions,
        'common': common_functions
    }

def compare_constants(original_constants: Dict, refactored_constants: Dict):
    """对比常量的差异"""
    print("\n🔍 常量对比分析:")
    print("=" * 60)
    
    original_set = set(original_constants.keys())
    refactored_set = set(refactored_constants.keys())
    
    # 找出缺失的常量
    missing_constants = original_set - refactored_set
    # 找出新增的常量
    new_constants = refactored_set - original_set
    # 找出共同的常量
    common_constants = original_set & refactored_set
    
    print(f"📊 常量统计:")
    print(f"   原始文件常量数量: {len(original_constants)}")
    print(f"   重构后常量数量: {len(refactored_constants)}")
    print(f"   共同常量数量: {len(common_constants)}")
    print(f"   缺失常量数量: {len(missing_constants)}")
    print(f"   新增常量数量: {len(new_constants)}")
    
    if missing_constants:
        print(f"\n❌ 缺失的常量:")
        for const_name in sorted(missing_constants):
            print(f"   - {const_name}")
    
    if new_constants:
        print(f"\n➕ 新增的常量:")
        for const_name in sorted(new_constants):
            print(f"   + {const_name}")
    
    if common_constants:
        print(f"\n✅ 成功保留的常量:")
        for const_name in sorted(common_constants):
            print(f"   ✓ {const_name}")
    
    return {
        'missing': missing_constants,
        'new': new_constants,
        'common': common_constants
    }

def generate_difference_report(original_analysis: Dict, refactored_analysis: Dict):
    """生成差异报告"""
    print("\n📋 重构差异总结报告")
    print("=" * 80)
    
    # 文件统计对比
    original_stats = original_analysis['file_stats']
    refactored_stats = refactored_analysis['total_stats']
    
    print(f"📊 文件统计对比:")
    print(f"   原始文件: 1个文件, {original_stats['total_lines']:,} 行, {original_stats['total_chars']:,} 字符")
    print(f"   重构后: {refactored_stats['total_files']}个模块, {refactored_stats['total_lines']:,} 行, {refactored_stats['total_chars']:,} 字符")
    
    line_coverage = (refactored_stats['total_lines'] / original_stats['total_lines']) * 100 if original_stats['total_lines'] > 0 else 0
    char_coverage = (refactored_stats['total_chars'] / original_stats['total_chars']) * 100 if original_stats['total_chars'] > 0 else 0
    
    print(f"   行数覆盖率: {line_coverage:.1f}%")
    print(f"   字符覆盖率: {char_coverage:.1f}%")
    
    # 对比分析
    class_comparison = compare_classes(original_analysis['classes'], refactored_analysis['all_classes'])
    function_comparison = compare_functions(original_analysis['functions'], refactored_analysis['all_functions'])
    constant_comparison = compare_constants(original_analysis['constants'], refactored_analysis['all_constants'])
    
    # 总体评估
    print(f"\n🎯 重构质量评估:")
    
    class_retention = len(class_comparison['common']) / len(original_analysis['classes']) * 100 if original_analysis['classes'] else 100
    function_retention = len(function_comparison['common']) / len(original_analysis['functions']) * 100 if original_analysis['functions'] else 100
    constant_retention = len(constant_comparison['common']) / len(original_analysis['constants']) * 100 if original_analysis['constants'] else 100
    
    print(f"   类保留率: {class_retention:.1f}% ({len(class_comparison['common'])}/{len(original_analysis['classes'])})")
    print(f"   函数保留率: {function_retention:.1f}% ({len(function_comparison['common'])}/{len(original_analysis['functions'])})")
    print(f"   常量保留率: {constant_retention:.1f}% ({len(constant_comparison['common'])}/{len(original_analysis['constants'])})")
    
    overall_score = (class_retention + function_retention + constant_retention) / 3
    print(f"   总体保留率: {overall_score:.1f}%")
    
    if overall_score >= 90:
        print(f"   ✅ 重构质量: 优秀")
    elif overall_score >= 80:
        print(f"   ✅ 重构质量: 良好")
    elif overall_score >= 70:
        print(f"   ⚠️ 重构质量: 一般")
    else:
        print(f"   ❌ 重构质量: 需要改进")
    
    # 模块分布分析
    print(f"\n📦 模块分布分析:")
    for module_name, module_data in refactored_analysis['modules'].items():
        class_count = len(module_data['classes'])
        func_count = len(module_data['functions'])
        const_count = len(module_data['constants'])
        lines = module_data['file_stats']['lines']
        
        print(f"   {module_name:<20} {lines:>6} 行  {class_count:>2} 类  {func_count:>2} 函数  {const_count:>2} 常量")

def main():
    """主函数"""
    print("🔍 代码重构差异分析")
    print("=" * 80)
    print("分析目标: 对比原始文件和重构后模块的差异")
    print("=" * 80)
    
    try:
        # 分析原始文件
        original_analysis = analyze_original_file()
        if not original_analysis:
            return
        
        # 分析重构后的模块
        refactored_analysis = analyze_refactored_modules()
        if not refactored_analysis:
            return
        
        # 生成差异报告
        generate_difference_report(original_analysis, refactored_analysis)
        
        print(f"\n🎉 差异分析完成!")
        
    except Exception as e:
        print(f"\n❌ 分析过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()