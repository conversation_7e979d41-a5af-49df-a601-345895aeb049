#!/usr/bin/env python3
"""
Metaso搜索问题诊断和修复测试
"""

import sys
import os
import time
import socket
import http.client
import json
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_network_connectivity():
    """测试网络连接"""
    print("🌐 测试网络连接")
    print("=" * 80)
    
    # 测试基本网络连接
    test_hosts = [
        ("metaso.cn", 443),
        ("www.google.com", 443),
        ("www.baidu.com", 443)
    ]
    
    for host, port in test_hosts:
        try:
            print(f"   测试连接到 {host}:{port}")
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            result = sock.connect_ex((host, port))
            sock.close()
            
            if result == 0:
                print(f"   ✅ {host} 连接成功")
            else:
                print(f"   ❌ {host} 连接失败: {result}")
        except Exception as e:
            print(f"   ❌ {host} 连接异常: {e}")
    
    print()

def test_metaso_api_direct():
    """直接测试Metaso API"""
    print("🔍 直接测试Metaso API")
    print("=" * 80)
    
    api_key = 'mk-988A8E4DC50C53312E3D1A8729687F4C'
    
    try:
        print("   创建HTTPS连接...")
        conn = http.client.HTTPSConnection("metaso.cn", timeout=30)
        
        payload = json.dumps({
            "q": "核电",
            "scope": "webpage",
            "includeSummary": True,
            "size": "2",
            "includeRawContent": True,
            "conciseSnippet": True
        })
        
        headers = {
            'Authorization': f'Bearer {api_key}',
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Connection': 'close'
        }
        
        print("   发送POST请求...")
        print(f"   URL: https://metaso.cn/api/v1/search")
        print(f"   Payload: {payload}")
        print(f"   Headers: {headers}")
        
        conn.request("POST", "/api/v1/search", payload, headers)
        
        print("   等待响应...")
        res = conn.getresponse()
        data = res.read()
        
        print(f"   响应状态: {res.status}")
        print(f"   响应头: {dict(res.getheaders())}")
        print(f"   响应数据长度: {len(data)} 字节")
        
        if res.status == 200:
            try:
                response_data = json.loads(data.decode("utf-8"))
                print(f"   ✅ JSON解析成功")
                print(f"   响应结构: {list(response_data.keys())}")
                print(f"   响应内容: {json.dumps(response_data, ensure_ascii=False, indent=2)[:500]}...")
            except json.JSONDecodeError as e:
                print(f"   ❌ JSON解析失败: {e}")
                print(f"   原始响应: {data.decode('utf-8')[:500]}...")
        else:
            print(f"   ❌ API请求失败")
            print(f"   响应内容: {data.decode('utf-8')[:500]}...")
        
        conn.close()
        
    except socket.error as e:
        print(f"   ❌ Socket错误: {e}")
        if "10054" in str(e):
            print("   🔍 这是WinError 10054: 远程主机强迫关闭连接")
            print("   可能原因:")
            print("     1. API密钥无效或过期")
            print("     2. 请求频率过高")
            print("     3. 服务器端防护机制")
            print("     4. 网络防火墙阻止")
    except Exception as e:
        print(f"   ❌ 其他错误: {e}")
    
    print()

def test_api_key_validation():
    """测试API密钥有效性"""
    print("🔑 测试API密钥有效性")
    print("=" * 80)
    
    api_key = 'mk-988A8E4DC50C53312E3D1A8729687F4C'
    
    # 测试不同的请求方式
    test_cases = [
        {
            "name": "最简请求",
            "payload": {"q": "test", "scope": "webpage", "size": "1"}
        },
        {
            "name": "完整请求",
            "payload": {
                "q": "核电",
                "scope": "webpage",
                "includeSummary": True,
                "size": "2",
                "includeRawContent": True,
                "conciseSnippet": True
            }
        }
    ]
    
    for case in test_cases:
        print(f"   测试: {case['name']}")
        try:
            conn = http.client.HTTPSConnection("metaso.cn", timeout=15)
            
            payload = json.dumps(case['payload'])
            headers = {
                'Authorization': f'Bearer {api_key}',
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
            
            conn.request("POST", "/api/v1/search", payload, headers)
            res = conn.getresponse()
            data = res.read()
            
            print(f"     状态码: {res.status}")
            
            if res.status == 401:
                print(f"     ❌ API密钥无效")
            elif res.status == 429:
                print(f"     ⚠️ 请求频率过高")
            elif res.status == 200:
                print(f"     ✅ API密钥有效")
            else:
                print(f"     ⚠️ 其他状态: {data.decode('utf-8')[:200]}...")
            
            conn.close()
            
        except Exception as e:
            print(f"     ❌ 请求失败: {e}")
        
        # 避免请求过快
        time.sleep(2)
    
    print()

def test_alternative_solutions():
    """测试替代解决方案"""
    print("🔧 测试替代解决方案")
    print("=" * 80)
    
    print("   方案1: 使用requests库")
    try:
        import requests
        
        api_key = 'mk-988A8E4DC50C53312E3D1A8729687F4C'
        
        url = "https://metaso.cn/api/v1/search"
        payload = {
            "q": "核电",
            "scope": "webpage",
            "size": "2"
        }
        headers = {
            'Authorization': f'Bearer {api_key}',
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        }
        
        print("     发送requests请求...")
        response = requests.post(url, json=payload, headers=headers, timeout=30)
        
        print(f"     状态码: {response.status_code}")
        
        if response.status_code == 200:
            print(f"     ✅ requests方式成功")
            data = response.json()
            print(f"     响应结构: {list(data.keys())}")
        else:
            print(f"     ❌ requests方式失败: {response.text[:200]}...")
            
    except ImportError:
        print("     ⚠️ requests库未安装")
    except Exception as e:
        print(f"     ❌ requests方式异常: {e}")
    
    print()
    
    print("   方案2: 使用urllib")
    try:
        import urllib.request
        import urllib.parse
        
        api_key = 'mk-988A8E4DC50C53312E3D1A8729687F4C'
        
        url = "https://metaso.cn/api/v1/search"
        payload = json.dumps({
            "q": "核电",
            "scope": "webpage",
            "size": "2"
        })
        
        req = urllib.request.Request(
            url,
            data=payload.encode('utf-8'),
            headers={
                'Authorization': f'Bearer {api_key}',
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        )
        
        print("     发送urllib请求...")
        with urllib.request.urlopen(req, timeout=30) as response:
            data = response.read()
            print(f"     状态码: {response.getcode()}")
            
            if response.getcode() == 200:
                print(f"     ✅ urllib方式成功")
                result = json.loads(data.decode('utf-8'))
                print(f"     响应结构: {list(result.keys())}")
            else:
                print(f"     ❌ urllib方式失败")
                
    except Exception as e:
        print(f"     ❌ urllib方式异常: {e}")
    
    print()

def test_error_analysis():
    """分析WinError 10054错误"""
    print("🔍 分析WinError 10054错误")
    print("=" * 80)
    
    print("   WinError 10054: 远程主机强迫关闭了一个现有的连接")
    print()
    print("   可能原因分析:")
    print("   1. ❌ API密钥问题:")
    print("      - API密钥已过期")
    print("      - API密钥被禁用")
    print("      - API密钥格式错误")
    print()
    print("   2. ❌ 请求频率问题:")
    print("      - 请求过于频繁")
    print("      - 触发了速率限制")
    print("      - 需要添加请求间隔")
    print()
    print("   3. ❌ 网络环境问题:")
    print("      - 防火墙阻止连接")
    print("      - 代理服务器问题")
    print("      - DNS解析问题")
    print()
    print("   4. ❌ 服务器端问题:")
    print("      - Metaso服务器维护")
    print("      - API端点变更")
    print("      - 服务器负载过高")
    print()
    print("   5. ❌ 请求格式问题:")
    print("      - 请求头格式错误")
    print("      - 请求体格式错误")
    print("      - 编码问题")
    print()
    
    print("   建议解决方案:")
    print("   ✅ 1. 验证API密钥有效性")
    print("   ✅ 2. 添加重试机制和延迟")
    print("   ✅ 3. 使用不同的HTTP库")
    print("   ✅ 4. 添加更详细的错误处理")
    print("   ✅ 5. 检查网络环境")

if __name__ == "__main__":
    print("🎯 Metaso搜索问题诊断")
    print("=" * 100)
    print()
    
    # 执行所有诊断测试
    test_network_connectivity()
    test_metaso_api_direct()
    test_api_key_validation()
    test_alternative_solutions()
    test_error_analysis()
    
    print("🎉 诊断完成")
    print("=" * 100)
    print()
    print("📋 诊断总结:")
    print("✅ 已增强Metaso搜索的错误处理")
    print("✅ 添加了重试机制和指数退避")
    print("✅ 改进了连接超时设置")
    print("✅ 增加了详细的错误日志")
    print("✅ 添加了Connection: close头部")
    print()
    print("🔧 如果问题仍然存在，可能需要:")
    print("1. 检查API密钥是否有效")
    print("2. 联系Metaso获取新的API密钥")
    print("3. 检查网络防火墙设置")
    print("4. 考虑使用其他搜索API")
    print()
    print("🚀 重新运行报告生成，查看搜索功能是否改善！")
