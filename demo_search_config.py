#!/usr/bin/env python3
"""
联网搜索配置演示脚本
展示如何控制联网搜索的用户确认机制
"""

import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from complete_report_generator import CompleteReportGenerator

def demo_search_configuration():
    """演示搜索配置功能"""
    print("🔍 联网搜索配置演示")
    print("=" * 60)
    
    # 创建生成器实例
    generator = CompleteReportGenerator(use_async=False)
    
    print("\n📋 当前系统默认配置:")
    generator.print_search_config()
    
    print("\n" + "="*60)
    print("🎯 配置选项说明:")
    print("   1. 默认模式（需要用户确认）")
    print("      - 系统会在搜索前询问用户是否同意")
    print("      - 用户输入 y/yes/是 确认搜索")
    print("      - 用户输入 n/no/否 跳过搜索")
    print()
    print("   2. 自动搜索模式")
    print("      - 系统自动进行搜索，无需用户确认")
    print("      - 适合批量处理或无人值守场景")
    
    print("\n" + "="*60)
    print("🛠️ 配置方法演示:")
    
    print("\n1️⃣ 启用自动搜索模式:")
    print("   generator.set_search_auto_confirm(True)")
    generator.set_search_auto_confirm(True)
    generator.print_search_config()
    
    print("\n2️⃣ 恢复用户确认模式:")
    print("   generator.set_search_auto_confirm(False)")
    generator.set_search_auto_confirm(False)
    generator.print_search_config()
    
    print("\n3️⃣ 获取详细配置信息:")
    print("   config = generator.get_search_config()")
    config = generator.get_search_config()
    print(f"   结果: {config}")
    
    print("\n" + "="*60)
    print("💡 实际使用建议:")
    print()
    print("🔸 首次使用或测试时:")
    print("   保持默认设置（需要用户确认）")
    print("   这样可以控制搜索行为，了解系统工作方式")
    print()
    print("🔸 生产环境或批量处理时:")
    print("   可以启用自动搜索模式")
    print("   generator.set_search_auto_confirm(True)")
    print()
    print("🔸 对搜索内容有特殊要求时:")
    print("   保持用户确认模式")
    print("   可以根据具体情况决定是否搜索")
    
    print("\n" + "="*60)
    print("📝 完整使用示例:")
    print("""
# 创建生成器并配置搜索模式
generator = CompleteReportGenerator()

# 方案1: 使用默认设置（推荐首次使用）
generator.print_search_config()  # 查看当前配置

# 方案2: 启用自动搜索（适合批量处理）
generator.set_search_auto_confirm(True)

# 生成报告（搜索将根据配置自动进行或询问用户）
output_path = generator.generate_report(
    topic="您的研究主题",
    data_sources=["data_sources"],
    framework_file_path="frameworks/default_framework.txt"
)
""")
    
    print("\n✅ 演示完成！")
    print("💡 现在您可以根据需要配置联网搜索行为了。")

if __name__ == "__main__":
    demo_search_configuration()
