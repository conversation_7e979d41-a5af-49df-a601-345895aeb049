#!/usr/bin/env python3
"""
测试搜索配置功能
验证联网搜索的用户确认机制
"""

import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from complete_report_generator import CompleteReportGenerator

def test_search_config():
    """测试搜索配置功能"""
    print("🧪 测试搜索配置功能")
    print("=" * 50)
    
    # 创建生成器实例
    generator = CompleteReportGenerator(use_async=False)
    
    print("\n1. 查看默认搜索配置:")
    generator.print_search_config()
    
    print("\n2. 测试设置自动搜索:")
    generator.set_search_auto_confirm(True)
    generator.print_search_config()
    
    print("\n3. 测试恢复需要用户确认:")
    generator.set_search_auto_confirm(False)
    generator.print_search_config()
    
    print("\n4. 获取搜索配置详情:")
    config = generator.get_search_config()
    print(f"配置详情: {config}")
    
    print("\n✅ 搜索配置功能测试完成！")
    print("\n💡 使用说明:")
    print("   • 默认情况下，联网搜索需要用户确认")
    print("   • 使用 generator.set_search_auto_confirm(True) 启用自动搜索")
    print("   • 使用 generator.print_search_config() 查看当前配置")

if __name__ == "__main__":
    test_search_config()
