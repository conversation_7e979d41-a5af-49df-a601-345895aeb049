#!/usr/bin/env python3
"""
测试修复后的报告生成器
"""

import sys
import warnings
from pathlib import Path

# 禁用matplotlib字体警告
warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')

def test_chart_generator():
    """测试ChartGenerator类"""
    print("🧪 测试ChartGenerator类...")
    
    try:
        # 导入ChartGenerator
        from complete_report_generator import ChartGenerator
        
        # 创建图表生成器实例
        chart_generator = ChartGenerator(Path("test_charts"))
        print("✅ ChartGenerator实例创建成功")
        
        # 测试产业链图表生成
        print("📊 测试产业链图表生成...")
        chart_path = chart_generator.generate_industry_chain_chart("测试主题", {})
        if chart_path:
            print(f"   ✅ 产业链图表生成成功: {chart_path}")
        else:
            print("   ⚠️ 产业链图表生成返回空路径")
        
        # 测试市场规模图表生成
        print("📊 测试市场规模图表生成...")
        chart_path = chart_generator.generate_market_size_chart("测试主题", {})
        if chart_path:
            print(f"   ✅ 市场规模图表生成成功: {chart_path}")
        else:
            print("   ⚠️ 市场规模图表生成返回空路径")
        
        # 测试技术趋势图表生成
        print("📊 测试技术趋势图表生成...")
        chart_path = chart_generator.generate_technology_trend_chart("测试主题", {})
        if chart_path:
            print(f"   ✅ 技术趋势图表生成成功: {chart_path}")
        else:
            print("   ⚠️ 技术趋势图表生成返回空路径")
        
        return True
        
    except Exception as e:
        print(f"❌ ChartGenerator测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_basic_imports():
    """测试基本导入"""
    print("🧪 测试基本导入...")
    
    try:
        # 测试主要类的导入
        from complete_report_generator import (
            GeminiModelConfig,
            GeminiAPIManager,
            AsyncGeminiAPIManager,
            ChartGenerator,
            TokenManager,
            ImageMatcher,
            CompleteReportGenerator
        )
        print("✅ 所有主要类导入成功")
        
        # 测试配置类
        config = GeminiModelConfig()
        print("✅ GeminiModelConfig实例创建成功")
        
        # 测试Token管理器
        token_manager = TokenManager()
        test_text = "这是一个测试文本，用于验证token估算功能。"
        tokens = token_manager.estimate_tokens(test_text)
        print(f"✅ TokenManager工作正常，估算tokens: {tokens}")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本导入测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_matplotlib_availability():
    """测试matplotlib可用性"""
    print("🧪 测试matplotlib可用性...")
    
    try:
        from complete_report_generator import MATPLOTLIB_AVAILABLE, PLOTLY_AVAILABLE
        print(f"✅ MATPLOTLIB_AVAILABLE: {MATPLOTLIB_AVAILABLE}")
        print(f"✅ PLOTLY_AVAILABLE: {PLOTLY_AVAILABLE}")
        
        if MATPLOTLIB_AVAILABLE:
            print("✅ matplotlib可用，图表生成功能已启用")
        else:
            print("⚠️ matplotlib不可用，图表生成功能被禁用")
            
        return True
        
    except Exception as e:
        print(f"❌ matplotlib可用性测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试修复后的报告生成器...")
    print("=" * 60)
    
    tests = [
        ("基本导入测试", test_basic_imports),
        ("matplotlib可用性测试", test_matplotlib_availability),
        ("图表生成器测试", test_chart_generator),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 40)
        try:
            if test_func():
                print(f"✅ {test_name} 通过")
                passed += 1
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！报告生成器修复成功！")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
