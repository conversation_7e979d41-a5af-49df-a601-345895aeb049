#!/usr/bin/env python3
"""
测试tokens计算器功能
验证智能文件读取和tokens管理是否正常工作
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from complete_report_generator import CompleteReportGenerator

def create_test_files():
    """创建测试文件"""
    print("🔧 创建测试文件...")
    
    test_dir = Path("test_token_files")
    test_dir.mkdir(exist_ok=True)
    
    created_files = []
    
    # 创建小文本文件
    small_txt = test_dir / "small.txt"
    with open(small_txt, 'w', encoding='utf-8') as f:
        f.write("这是一个小文本文件。" * 10)  # 约100字符
    created_files.append(str(small_txt))
    
    # 创建中等文本文件
    medium_txt = test_dir / "medium.txt"
    with open(medium_txt, 'w', encoding='utf-8') as f:
        f.write("这是一个中等大小的文本文件，包含更多内容。" * 1000)  # 约20000字符
    created_files.append(str(medium_txt))
    
    # 创建大文本文件
    large_txt = test_dir / "large.txt"
    with open(large_txt, 'w', encoding='utf-8') as f:
        content = "这是一个大文本文件，包含大量内容用于测试tokens计算。" * 10000  # 约250000字符
        f.write(content)
    created_files.append(str(large_txt))
    
    # 创建JSON文件
    json_file = test_dir / "data.json"
    with open(json_file, 'w', encoding='utf-8') as f:
        f.write('{"name": "测试数据", "description": "用于测试的JSON数据文件"}')
    created_files.append(str(json_file))
    
    # 创建Markdown文件
    md_file = test_dir / "readme.md"
    with open(md_file, 'w', encoding='utf-8') as f:
        f.write("# 测试文档\n\n这是一个测试用的Markdown文件。\n\n## 内容\n\n包含一些测试内容。")
    created_files.append(str(md_file))
    
    print(f"✅ 创建了 {len(created_files)} 个测试文件")
    return test_dir, created_files

def test_token_estimation():
    """测试tokens估算功能"""
    print("\n🧮 测试tokens估算功能...")
    
    try:
        generator = CompleteReportGenerator(use_async=False)
        token_manager = generator.token_manager
        
        # 测试不同长度的文本
        test_texts = [
            ("短文本", "这是一个短文本。"),
            ("中文本", "这是一个中等长度的文本。" * 100),
            ("长文本", "这是一个很长的文本，用于测试tokens计算功能。" * 1000),
            ("超长文本", "这是一个超长文本，用于测试tokens限制和分批处理。" * 10000)
        ]
        
        print("📊 文本tokens估算测试:")
        for name, text in test_texts:
            tokens = token_manager.estimate_tokens(text)
            needs_split = token_manager.needs_splitting(text)
            print(f"   {name}: {len(text)} 字符 → {tokens:,} tokens (需要分批: {'是' if needs_split else '否'})")
        
        return True
        
    except Exception as e:
        print(f"❌ tokens估算测试失败: {str(e)}")
        return False

def test_file_token_estimation():
    """测试文件tokens估算功能"""
    print("\n📄 测试文件tokens估算功能...")
    
    try:
        test_dir, created_files = create_test_files()
        generator = CompleteReportGenerator(use_async=False)
        token_manager = generator.token_manager
        
        print("📊 文件tokens估算测试:")
        for file_path in created_files:
            file_info = token_manager.estimate_file_tokens(file_path, include_images=True)
            file_name = Path(file_path).name
            tokens = file_info.get("estimated_tokens", 0)
            file_type = file_info.get("file_type", "unknown")
            
            print(f"   {file_name}: {tokens:,} tokens ({file_type})")
        
        # 测试总tokens计算
        print("\n📊 总tokens计算测试:")
        total_calculation = token_manager.calculate_total_tokens_for_files(created_files, include_images=True)
        
        print(f"   总tokens: {total_calculation['total_tokens']:,}")
        print(f"   超过限制: {'是' if total_calculation['exceeds_limit'] else '否'}")
        print(f"   文本文件: {total_calculation['text_files_count']} 个")
        print(f"   图片文件: {total_calculation['image_files_count']} 个")
        
        return True
        
    except Exception as e:
        print(f"❌ 文件tokens估算测试失败: {str(e)}")
        return False
    finally:
        # 清理测试文件
        try:
            import shutil
            if test_dir.exists():
                shutil.rmtree(test_dir)
                print(f"🗑️ 清理测试目录: {test_dir}")
        except:
            pass

def test_reading_strategy():
    """测试读取策略功能"""
    print("\n🎯 测试读取策略功能...")
    
    try:
        test_dir, created_files = create_test_files()
        generator = CompleteReportGenerator(use_async=False)
        token_manager = generator.token_manager
        
        # 测试读取策略
        print("📋 制定读取策略:")
        strategy = token_manager.get_optimal_reading_strategy(created_files)
        
        print(f"   策略: {strategy['strategy']}")
        print(f"   包含图片: {'是' if strategy['include_images'] else '否'}")
        print(f"   读取文件数: {len(strategy['files_to_read'])}")
        print(f"   预计tokens: {strategy['estimated_tokens']:,}")
        
        if strategy.get('excluded_files'):
            print(f"   排除文件数: {len(strategy['excluded_files'])}")
        
        # 测试实际读取
        print("\n📖 测试实际读取:")
        data_files, content, image_files = generator._read_data_source_detailed(str(test_dir))
        
        actual_tokens = token_manager.estimate_tokens(content)
        print(f"   实际读取tokens: {actual_tokens:,}")
        print(f"   是否超过限制: {'是' if actual_tokens > token_manager.max_tokens else '否'}")
        print(f"   读取的数据文件: {len(data_files)} 个")
        print(f"   读取的图片文件: {len(image_files)} 个")
        
        return True
        
    except Exception as e:
        print(f"❌ 读取策略测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 清理测试文件
        try:
            import shutil
            if test_dir.exists():
                shutil.rmtree(test_dir)
                print(f"🗑️ 清理测试目录: {test_dir}")
        except:
            pass

if __name__ == "__main__":
    print("🚀 开始测试tokens计算器功能...")
    
    # 测试基础tokens估算
    estimation_test = test_token_estimation()
    
    if estimation_test:
        # 测试文件tokens估算
        file_test = test_file_token_estimation()
        
        if file_test:
            # 测试读取策略
            strategy_test = test_reading_strategy()
            
            if strategy_test:
                print("\n🎉 所有tokens计算器测试通过！")
                print("✅ 智能文件读取和tokens管理功能正常工作")
            else:
                print("\n⚠️ 读取策略测试失败")
        else:
            print("\n⚠️ 文件tokens估算测试失败")
    else:
        print("\n❌ 基础tokens估算测试失败")
    
    print("\n📋 Tokens计算器功能总结:")
    print("1. ✅ 智能tokens估算：支持中英文混合文本的精确估算")
    print("2. ✅ 文件tokens计算：支持各种文件格式的tokens预估")
    print("3. ✅ 读取策略制定：根据tokens限制自动选择最优读取策略")
    print("4. ✅ 内容截断：超过限制时智能截断保留重要内容")
    print("5. ✅ 图片处理：可选择性包含或排除图片分析")
    print("\n🎯 解决的问题:")
    print("- 修复了超过250000 tokens导致API限额的问题")
    print("- 实现了智能文件选择和内容截断")
    print("- 支持图片内容的可选包含")
    print("- 提供了详细的tokens使用情况报告")
