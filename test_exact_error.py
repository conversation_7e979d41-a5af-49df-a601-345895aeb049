#!/usr/bin/env python3
"""
测试精确的错误情况
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from complete_report_generator import safe_json_loads
import json

def test_exact_error():
    """测试精确的错误情况"""
    
    print("🎯 测试精确的错误情况")
    print("=" * 60)
    
    # 根据错误信息构造的问题JSON - 模拟实际的错误
    # 错误位置：行17, 列81
    problem_json = '''{
    "instructions": {
        "节点001": {
            "title": "技术发展趋势",
            "content_requirements": "",
            "word_count": "30-50字"
        },
        "节点005": {
            "title": "要点分析",
            "content_requirements": "详细分析",
            "word_count": "50-80字"
        },
        "节点010": {
            "title": "市场前景",
            "content_requirements": "",
            "word_count": "80-100字"
        }
    }
}'''
    
    print("📝 测试精确错误场景")
    print("原始JSON:")
    print(problem_json)
    print()
    
    # 先用标准JSON解析器测试
    print("❌ 标准json.loads解析:")
    try:
        result = json.loads(problem_json)
        print(f"✅ 标准解析成功: {len(result.get('instructions', {}))} 个指导")
    except json.JSONDecodeError as e:
        print(f"💥 标准解析失败: {e}")
        print(f"   错误位置: 行{e.lineno}, 列{e.colno}")
    
    print()
    
    # 使用修复后的解析器
    print("✅ safe_json_loads解析:")
    try:
        result = safe_json_loads(problem_json)
        if result:
            print("🎯 修复解析成功!")
            instructions = result.get("instructions", {})
            print(f"   📊 指导数量: {len(instructions)}")
            for node_id, instruction in instructions.items():
                title = instruction.get("title", "无标题")
                word_count = instruction.get("word_count", "")
                print(f"   📋 {node_id}: {title} ({word_count})")
        else:
            print("❌ 修复解析失败，返回空结果")
    except Exception as e:
        print(f"❌ 修复解析异常: {e}")
    
    print()

def test_problematic_json_from_error():
    """测试从错误信息中提取的问题JSON"""
    
    print("🔍 测试从错误信息中提取的问题JSON")
    print("=" * 60)
    
    # 根据实际错误信息构造的JSON
    # 清理后内容显示："word_count": "30-50字"},
    problematic_json = '''{
    "instructions": {
        "节点001": {
            "title": "技术发展趋势",
            "content_requirements": "",
            "word_count": "30-50字"},
        "节点005": {
            "title": "要点分析",
            "content_requirements": "详细分析",
            "word_count": "50-80字"
        }
    }
}'''
    
    print("📝 问题JSON（模拟清理后的状态）:")
    print(problematic_json)
    print()
    
    # 标准解析
    print("❌ 标准json.loads解析:")
    try:
        result = json.loads(problematic_json)
        print(f"✅ 标准解析成功: {len(result.get('instructions', {}))} 个指导")
    except json.JSONDecodeError as e:
        print(f"💥 标准解析失败: {e}")
        print(f"   错误位置: 行{e.lineno}, 列{e.colno}")
        
        # 显示错误位置的内容
        lines = problematic_json.split('\n')
        if e.lineno <= len(lines):
            error_line = lines[e.lineno - 1]
            print(f"   错误行内容: {repr(error_line)}")
            if e.colno <= len(error_line):
                print(f"   错误位置字符: {repr(error_line[e.colno-1:e.colno+5])}")
    
    print()
    
    # 修复解析
    print("✅ safe_json_loads解析:")
    try:
        result = safe_json_loads(problematic_json)
        if result:
            print("🎯 修复解析成功!")
            instructions = result.get("instructions", {})
            print(f"   📊 指导数量: {len(instructions)}")
        else:
            print("❌ 修复解析失败")
    except Exception as e:
        print(f"❌ 修复解析异常: {e}")

if __name__ == "__main__":
    test_exact_error()
    test_problematic_json_from_error()
