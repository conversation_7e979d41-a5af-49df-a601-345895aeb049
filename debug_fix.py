#!/usr/bin/env python3
"""
调试JSON修复过程
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from complete_report_generator import fix_empty_string_values, clean_json_string
import json

def debug_fix_process():
    """调试修复过程"""
    
    problem_json = '''{
    "instructions": {
        "节点001": {
            "title": "1. 总论：核电产业概览与研究界定",
            "content_requirements": "
            "word_count": "80-100字"
        },
        "节点024": {
            "title": "2. 核电技术发展现状",
            "content_requirements": "详细分析核电技术发展",
            "word_count": "100-150字"
        }
    }
}'''
    
    print("🔍 原始JSON:")
    print(repr(problem_json))
    print()
    
    print("🔧 应用fix_empty_string_values修复:")
    fixed = fix_empty_string_values(problem_json)
    print("修复后:")
    print(repr(fixed))
    print()
    
    print("📄 修复后的可读格式:")
    print(fixed)
    print()
    
    print("🧪 尝试解析修复后的JSON:")
    try:
        result = json.loads(fixed)
        print("✅ 解析成功!")
        print(f"指导数量: {len(result.get('instructions', {}))}")
    except json.JSONDecodeError as e:
        print(f"❌ 解析失败: {e}")
        print(f"错误位置: 行{e.lineno}, 列{e.colno}")
        
        # 显示错误位置附近的内容
        lines = fixed.split('\n')
        if e.lineno <= len(lines):
            error_line = lines[e.lineno - 1]
            print(f"错误行: {repr(error_line)}")
            if e.colno <= len(error_line):
                print(f"错误字符: {repr(error_line[e.colno-1:e.colno+5])}")

def test_simple_fix():
    """测试简单修复"""
    
    print("\n" + "="*60)
    print("🎯 测试简单修复方案")
    
    problem_json = '''{
    "instructions": {
        "节点001": {
            "title": "1. 总论：核电产业概览与研究界定",
            "content_requirements": "
            "word_count": "80-100字"
        }
    }
}'''
    
    # 直接字符串替换
    fixed = problem_json.replace(
        '"content_requirements": "\n            "word_count":',
        '"content_requirements": "",\n            "word_count":'
    )
    
    # 添加缺少的逗号
    fixed = fixed.replace(
        '"word_count": "80-100字"\n        }',
        '"word_count": "80-100字"\n        }'
    )
    
    print("简单修复后:")
    print(fixed)
    print()
    
    try:
        result = json.loads(fixed)
        print("✅ 简单修复成功!")
        print(f"指导数量: {len(result.get('instructions', {}))}")
    except Exception as e:
        print(f"❌ 简单修复失败: {e}")

if __name__ == "__main__":
    debug_fix_process()
    test_simple_fix()
