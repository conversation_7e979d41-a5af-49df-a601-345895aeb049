#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
图片嵌入优化功能测试脚本
测试高-中-低优先级的图片嵌入优化
"""

import os
import sys
import asyncio
from pathlib import Path
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

from complete_report_generator import CompleteReportGenerator

def create_test_environment():
    """创建测试环境"""
    print("🔧 创建测试环境...")
    
    # 创建测试目录
    test_dir = Path("test_image_optimization")
    test_dir.mkdir(exist_ok=True)
    
    # 创建数据源目录
    data_dir = test_dir / "data_sources"
    data_dir.mkdir(exist_ok=True)
    
    # 创建测试图片目录
    images_dir = data_dir / "images"
    images_dir.mkdir(exist_ok=True)
    
    # 创建模拟图片文件（空文件，仅用于测试文件名匹配）
    test_images = [
        "market_analysis_chart.png",
        "technology_trend_graph.jpg",
        "competition_comparison.png",
        "data_statistics.jpg",
        "process_flow_diagram.png",
        "market_share_pie_chart.png"
    ]
    
    for img_name in test_images:
        img_path = images_dir / img_name
        if not img_path.exists():
            img_path.touch()
    
    # 创建测试文档
    test_doc = data_dir / "test_document.txt"
    test_content = """
# 人工智能市场分析报告

## 市场概况
人工智能市场在过去几年中经历了快速增长。根据最新数据显示，全球AI市场规模已达到500亿美元。

## 技术发展趋势
机器学习和深度学习技术不断进步，推动了AI应用的广泛普及。主要技术趋势包括：
- 自然语言处理技术的突破
- 计算机视觉的应用扩展
- 强化学习的商业化应用

## 竞争格局分析
当前AI市场的主要参与者包括科技巨头和创新型企业。竞争主要集中在以下几个方面：
- 算法创新能力
- 数据资源获取
- 计算基础设施

## 数据统计
- 市场增长率：35%
- 投资金额：1000亿美元
- 企业数量：5000+家

## 发展流程
AI技术的发展遵循一定的流程和阶段，从基础研究到商业应用需要经历多个环节。
"""
    
    with open(test_doc, 'w', encoding='utf-8') as f:
        f.write(test_content)
    
    print(f"✅ 测试环境创建完成: {test_dir}")
    return test_dir, data_dir

async def test_high_priority_optimization():
    """测试高优先级优化：第一轮生成时集成图片嵌入"""
    print("\n" + "="*60)
    print("🔥 测试高优先级优化：第一轮生成时集成图片嵌入")
    print("="*60)
    
    try:
        # 创建生成器
        generator = CompleteReportGenerator(use_async=True)
        
        # 设置较小的目标字数以便快速测试
        generator.report_config["target_words"] = 5000
        generator.report_config["enable_image_embedding"] = True
        
        # 创建测试环境
        test_dir, data_dir = create_test_environment()
        
        # 生成报告
        print("📝 开始生成报告（包含第一轮图片嵌入）...")
        output_path = await generator.generate_report_async(
            topic="AI市场分析报告测试",
            data_sources=[str(data_dir)],
            framework_file_path=None
        )
        
        # 检查结果
        if output_path and Path(output_path).exists():
            print(f"✅ 高优先级优化测试成功")
            print(f"   📄 输出文件: {output_path}")
            
            # 检查是否包含图片占位符或图片引用
            if output_path.endswith('.docx'):
                print("   🔍 检查DOCX文件中的图片嵌入...")
                try:
                    from docx import Document
                    doc = Document(output_path)
                    
                    image_count = 0
                    placeholder_count = 0
                    
                    for paragraph in doc.paragraphs:
                        if '[IMAGE:' in paragraph.text or '[图片引用:' in paragraph.text:
                            placeholder_count += 1
                    
                    # 检查实际图片对象
                    for rel in doc.part.rels.values():
                        if "image" in rel.target_ref:
                            image_count += 1
                    
                    print(f"   📊 发现图片占位符: {placeholder_count} 个")
                    print(f"   📊 发现实际图片: {image_count} 个")
                    
                    if placeholder_count > 0 or image_count > 0:
                        print("   ✅ 第一轮生成成功嵌入图片！")
                    else:
                        print("   ⚠️ 第一轮生成未发现图片嵌入")
                        
                except Exception as e:
                    print(f"   ⚠️ DOCX检查失败: {str(e)}")
            
            return True
        else:
            print("❌ 高优先级优化测试失败：未生成输出文件")
            return False
            
    except Exception as e:
        print(f"❌ 高优先级优化测试失败: {str(e)}")
        return False

async def test_medium_priority_optimization():
    """测试中优先级优化：节点级图片定位机制"""
    print("\n" + "="*60)
    print("🔥 测试中优先级优化：节点级图片定位机制")
    print("="*60)
    
    try:
        # 创建生成器
        generator = CompleteReportGenerator(use_async=True)
        
        # 创建测试环境
        test_dir, data_dir = create_test_environment()
        
        # 创建一个简单的测试报告
        test_report = test_dir / "test_report.docx"
        
        # 创建简单的DOCX文档用于测试
        try:
            from docx import Document
            doc = Document()
            doc.add_heading("AI市场分析报告", 0)
            
            doc.add_heading("市场概况", 1)
            doc.add_paragraph("人工智能市场在过去几年中经历了快速增长。")
            
            doc.add_heading("技术趋势", 1)
            doc.add_paragraph("机器学习和深度学习技术不断进步。")
            
            doc.add_heading("竞争分析", 1)
            doc.add_paragraph("当前AI市场的主要参与者包括科技巨头。")
            
            doc.save(str(test_report))
            print(f"✅ 创建测试报告: {test_report}")
            
        except ImportError:
            print("⚠️ python-docx未安装，跳过DOCX测试")
            return False
        
        # 测试节点级图片匹配
        print("🎯 测试节点级图片匹配...")
        
        # 收集图片数据
        all_image_data = generator.collect_all_image_data([str(data_dir)])
        
        if all_image_data:
            # 执行节点级匹配
            node_matches = generator._perform_node_level_image_matching(
                str(test_report), all_image_data, "AI市场分析报告"
            )
            
            if node_matches:
                print(f"✅ 节点级匹配成功，找到 {len(node_matches)} 个匹配")
                
                for i, match in enumerate(node_matches, 1):
                    print(f"   {i}. 节点: {match['node_title']}")
                    print(f"      图片数: {len(match['images'])}")
                
                # 测试节点级嵌入
                print("📎 测试节点级图片嵌入...")
                enhanced_path = generator._execute_node_level_image_embedding(
                    str(test_report), node_matches, [str(data_dir)]
                )
                
                if enhanced_path != str(test_report):
                    print(f"✅ 节点级嵌入成功: {enhanced_path}")
                    return True
                else:
                    print("⚠️ 节点级嵌入未产生新文件")
                    return False
            else:
                print("⚠️ 未找到节点级匹配")
                return False
        else:
            print("⚠️ 未找到图片数据")
            return False
            
    except Exception as e:
        print(f"❌ 中优先级优化测试失败: {str(e)}")
        return False

def test_low_priority_optimization():
    """测试低优先级优化：图片匹配算法优化"""
    print("\n" + "="*60)
    print("🔥 测试低优先级优化：图片匹配算法优化")
    print("="*60)
    
    try:
        # 创建生成器
        generator = CompleteReportGenerator()
        
        # 创建测试数据
        test_images = [
            {
                'filename': 'market_analysis_chart.png',
                'description': '市场分析图表',
                'filename_keywords': ['market', 'analysis', 'chart'],
                'image_type': 'chart',
                'importance_score': 0.8
            },
            {
                'filename': 'tech_trend_graph.jpg',
                'description': '技术趋势图',
                'filename_keywords': ['tech', 'trend', 'graph'],
                'image_type': 'trend',
                'importance_score': 0.7
            }
        ]
        
        test_report_analysis = {
            'keywords': ['market', 'analysis', 'technology', 'trend', 'ai'],
            'sections': ['市场概况', '技术趋势', '竞争分析'],
            'data_points': ['35%', '500亿美元', '1000亿美元']
        }
        
        test_context = {
            'topic': 'AI市场分析报告',
            'report_type': '技术分析报告'
        }
        
        # 测试增强匹配算法
        print("🎯 测试增强图片匹配算法...")
        
        gemini_matcher = generator.GeminiImageMatcher(generator)
        
        # 测试多维度匹配
        for img_info in test_images:
            match_result = gemini_matcher._calculate_multi_dimensional_match(
                img_info, test_report_analysis, test_context
            )
            
            print(f"📊 图片: {img_info['filename']}")
            print(f"   总得分: {match_result['overall_score']:.3f}")
            print(f"   语义得分: {match_result['semantic_score']:.3f}")
            print(f"   关键词得分: {match_result['keyword_score']:.3f}")
            print(f"   上下文得分: {match_result['context_score']:.3f}")
            print(f"   置信度: {match_result['match_details']['confidence_level']}")
            print(f"   主要匹配类型: {match_result['match_details']['primary_match_type']}")
            print()
        
        print("✅ 低优先级优化测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 低优先级优化测试失败: {str(e)}")
        return False

async def main():
    """主测试函数"""
    print("🧪 图片嵌入优化功能测试")
    print("="*60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    results = {
        'high_priority': False,
        'medium_priority': False,
        'low_priority': False
    }
    
    # 测试高优先级优化
    results['high_priority'] = await test_high_priority_optimization()
    
    # 测试中优先级优化
    results['medium_priority'] = await test_medium_priority_optimization()
    
    # 测试低优先级优化
    results['low_priority'] = test_low_priority_optimization()
    
    # 输出测试结果
    print("\n" + "="*60)
    print("📊 测试结果总结")
    print("="*60)
    
    for priority, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{priority.replace('_', ' ').title()}: {status}")
    
    total_passed = sum(results.values())
    print(f"\n总体结果: {total_passed}/3 项测试通过")
    
    if total_passed == 3:
        print("🎉 所有优化功能测试通过！")
    elif total_passed >= 2:
        print("✅ 大部分优化功能正常工作")
    else:
        print("⚠️ 需要进一步检查和优化")

if __name__ == "__main__":
    asyncio.run(main())
