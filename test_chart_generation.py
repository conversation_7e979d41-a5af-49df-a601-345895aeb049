#!/usr/bin/env python3
"""
测试图表生成功能
"""

import sys
import warnings
from pathlib import Path

# 禁用matplotlib字体警告
warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')

def test_chart_generation():
    """测试图表生成功能"""
    print("🧪 开始测试图表生成功能...")
    
    try:
        # 导入必要的库
        import matplotlib
        matplotlib.use('Agg')  # 使用非交互式后端
        import matplotlib.pyplot as plt
        import numpy as np
        
        # 设置字体
        plt.rcParams['font.family'] = 'sans-serif'
        plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'Liberation Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        print("✅ matplotlib导入和配置成功")
        
        # 创建输出目录
        output_dir = Path("charts")
        output_dir.mkdir(exist_ok=True)
        
        # 测试1: 简单线图
        print("📊 测试1: 生成简单线图...")
        fig, ax = plt.subplots(figsize=(10, 6))
        x = np.linspace(0, 10, 100)
        y = np.sin(x)
        ax.plot(x, y, 'b-', linewidth=2, label='sin(x)')
        ax.set_title('Test Line Chart', fontsize=14)
        ax.set_xlabel('X Axis', fontsize=12)
        ax.set_ylabel('Y Axis', fontsize=12)
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        chart_path = output_dir / "test_line_chart.png"
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"   ✅ 线图生成成功: {chart_path}")
        
        # 测试2: 柱状图
        print("📊 测试2: 生成柱状图...")
        fig, ax = plt.subplots(figsize=(10, 6))
        categories = ['Category A', 'Category B', 'Category C', 'Category D']
        values = [23, 45, 56, 78]
        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']
        
        bars = ax.bar(categories, values, color=colors, alpha=0.8)
        ax.set_title('Test Bar Chart', fontsize=14)
        ax.set_ylabel('Values', fontsize=12)
        
        # 添加数值标签
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 1,
                   f'{value}', ha='center', va='bottom')
        
        chart_path = output_dir / "test_bar_chart.png"
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"   ✅ 柱状图生成成功: {chart_path}")
        
        # 测试3: 散点图
        print("📊 测试3: 生成散点图...")
        fig, ax = plt.subplots(figsize=(10, 6))
        
        # 生成随机数据
        np.random.seed(42)
        x = np.random.randn(100)
        y = np.random.randn(100)
        colors = np.random.rand(100)
        sizes = 1000 * np.random.rand(100)
        
        scatter = ax.scatter(x, y, c=colors, s=sizes, alpha=0.6, cmap='viridis')
        ax.set_title('Test Scatter Plot', fontsize=14)
        ax.set_xlabel('X Values', fontsize=12)
        ax.set_ylabel('Y Values', fontsize=12)
        
        # 添加颜色条
        plt.colorbar(scatter)
        
        chart_path = output_dir / "test_scatter_chart.png"
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"   ✅ 散点图生成成功: {chart_path}")
        
        # 测试4: 多子图
        print("📊 测试4: 生成多子图...")
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 10))
        
        # 子图1: 线图
        x = np.linspace(0, 2*np.pi, 100)
        ax1.plot(x, np.sin(x), 'r-', label='sin(x)')
        ax1.plot(x, np.cos(x), 'b-', label='cos(x)')
        ax1.set_title('Trigonometric Functions')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 子图2: 柱状图
        categories = ['A', 'B', 'C', 'D']
        values = [20, 35, 30, 25]
        ax2.bar(categories, values, color='skyblue')
        ax2.set_title('Sample Bar Chart')
        
        # 子图3: 饼图
        sizes = [30, 25, 20, 25]
        labels = ['Segment 1', 'Segment 2', 'Segment 3', 'Segment 4']
        ax3.pie(sizes, labels=labels, autopct='%1.1f%%', startangle=90)
        ax3.set_title('Sample Pie Chart')
        
        # 子图4: 直方图
        data = np.random.normal(0, 1, 1000)
        ax4.hist(data, bins=30, alpha=0.7, color='green')
        ax4.set_title('Sample Histogram')
        ax4.set_xlabel('Value')
        ax4.set_ylabel('Frequency')
        
        plt.tight_layout()
        chart_path = output_dir / "test_multi_chart.png"
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"   ✅ 多子图生成成功: {chart_path}")
        
        print("\n🎉 所有图表测试完成！")
        print(f"📁 图表保存在: {output_dir.absolute()}")
        
        # 列出生成的文件
        chart_files = list(output_dir.glob("test_*.png"))
        print(f"📊 共生成 {len(chart_files)} 个图表文件:")
        for chart_file in chart_files:
            size_kb = chart_file.stat().st_size / 1024
            print(f"   - {chart_file.name} ({size_kb:.1f} KB)")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 图表生成失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_chart_generation()
    sys.exit(0 if success else 1)
