#!/usr/bin/env python3
"""
简化的联网搜索功能修复测试
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from complete_report_generator import CompleteReportGenerator

def test_search_enhancement_only():
    """只测试搜索增强功能"""
    print("🔧 测试联网搜索增强功能...")
    
    try:
        # 初始化报告生成器
        generator = CompleteReportGenerator(use_async=False)
        
        # 验证配置
        auto_confirm = generator.report_config.get("search_auto_confirm", False)
        print(f"✅ 自动搜索确认: {auto_confirm}")
        
        # 测试搜索API
        search_works = generator.test_search_functionality()
        if not search_works:
            print("❌ 搜索API测试失败")
            return False
        
        # 创建简单测试报告
        test_content = """# AI技术报告

## 概述
人工智能技术发展迅速。

## 现状
当前AI在多个领域应用广泛。
"""
        
        test_report_path = "simple_test_report.md"
        with open(test_report_path, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        print("\n🔍 测试搜索增强...")
        
        # 测试搜索增强（使用修复后的逻辑）
        auto_confirm = generator.report_config.get("search_auto_confirm", True)
        enhanced_path = generator.enhance_report_with_tool_calling(
            test_report_path, 
            "AI技术", 
            user_confirm=not auto_confirm  # 修复后的逻辑
        )
        
        success = False
        if enhanced_path != test_report_path:
            print(f"✅ 搜索增强成功: {enhanced_path}")
            
            # 检查增强效果
            if os.path.exists(enhanced_path):
                with open(enhanced_path, 'r', encoding='utf-8') as f:
                    enhanced_content = f.read()
                
                print(f"📊 原始长度: {len(test_content)} 字符")
                print(f"📊 增强长度: {len(enhanced_content)} 字符")
                
                if len(enhanced_content) > len(test_content):
                    print("✅ 报告内容成功增强！")
                    success = True
                else:
                    print("⚠️ 报告内容未明显增强")
            else:
                print("❌ 增强报告文件不存在")
        else:
            print("⚠️ 搜索增强未执行")
        
        # 清理测试文件
        for file_path in [test_report_path, enhanced_path]:
            if file_path and os.path.exists(file_path):
                try:
                    os.remove(file_path)
                    print(f"🗑️ 已清理: {file_path}")
                except:
                    pass
        
        return success
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("🔧 简化搜索功能修复测试")
    print("=" * 50)
    
    success = test_search_enhancement_only()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ 联网搜索功能修复成功！")
        print("🎉 搜索增强功能正常工作")
    else:
        print("❌ 联网搜索功能仍有问题")
        print("💡 请检查API配置和网络连接")
    print("=" * 50)
    
    return success

if __name__ == "__main__":
    main()
